/*
錄音
https://github.com/xiangyuecn/Recorder
src: engine/g711x.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,r=t.i18n;!function(e,t,r){"use strict";var n=function(t,n,a,o){e.prototype["enc_"+t]={stable:!0,fast:!0,getTestMsg:function(){return r("d8YX::{1}；{2}音频文件无法直接播放，可用Recorder.{2}2wav()转码成wav播放；采样率比特率设置无效，固定为8000hz采样率、16位，每个采样压缩成8位存储，音频文件大小为8000字节/秒；如需任意采样率支持，请使用Recorder.{2}_encode()方法",0,n,t)}},e.prototype[t]=function(n,o,f){var i=this.set,u=i.sampleRate,c=8e3;if(i.bitRate=16,i.sampleRate=c,u>c)n=e.SampleData([n],u,c).data;else if(u<c)return void f(r("29UK::数据采样率低于{1}",0,c));o(a(n).buffer,"audio/"+t)},e[t+"_encode"]=function(e){return a(e)},e[t+"_decode"]=function(e){return o(e)},e[t+"2wav"]=function(n,a,f){if(e.prototype.wav){var i=function(t,r){var n=new Uint8Array(t),i=o(n),u=e({type:"wav",sampleRate:8e3,bitRate:16});r&&(u.dataType="arraybuffer"),u.mock(i,8e3).stop(function(e,t,r){a(e,t,r)},f)};if(n instanceof ArrayBuffer)i(n,1);else{var u=new FileReader;u.onloadend=function(){i(u.result)},u.readAsArrayBuffer(n)}}else f(r.G("NeedImport-2",[t+"2wav","src/engine/wav.js"]))},e.prototype[t+"_envCheck"]=function(e,t){return""},e.prototype[t+"_start"]=function(e){return e.bitRate=16,e.sampleRate=8e3,{set:e,memory:new Uint8Array(5e5),mOffset:0}};var f=function(e,t){var r=t.length;if(e.mOffset+r>e.memory.length){var n=new Uint8Array(e.memory.length+Math.max(5e5,r));n.set(e.memory.subarray(0,e.mOffset)),e.memory=n}e.memory.set(t,e.mOffset),e.mOffset+=r};e.prototype[t+"_stop"]=function(e){e&&e.memory&&(e.memory=null)},e.prototype[t+"_encode"]=function(e,t){if(e&&e.memory){var r=e.set,n=a(t);r.takeoffEncodeChunk?r.takeoffEncodeChunk(n):f(e,n)}},e.prototype[t+"_complete"]=function(e,n,a,o){e&&e.memory?(o&&this[t+"_stop"](e),n(e.memory.buffer.slice(0,e.mOffset),"audio/"+t)):a(r("quVJ::{1}编码器未start",0,t))}},a=[1,2,3,3,4,4,4,4,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];n("g711a","G.711 A-law (pcma)",function(e){for(var t=new Uint8Array(e.length),r=0;r<e.length;r++){var n,o=e[r];o>=0?n=213:(n=85,o=-o-1);var f=(a[o>>8&127]||8)-1,i=f<<4;i|=f<2?o>>4&15:o>>f+3&15,t[r]=i^n}return t},function(e){for(var t=new Int16Array(e.length),r=0;r<e.length;r++){var n=85^e[r],a=(15&n)<<4,o=(112&n)>>4;switch(o){case 0:a+=8;break;case 1:a+=264;break;default:a+=264,a<<=o-1}t[r]=128&n?a:-a}return t}),n("g711u","G.711 μ-law (pcmu、mu-law)",function(e){for(var t=new Uint8Array(e.length),r=0;r<e.length;r++){var n,o=e[r];o<0?(o=132-o,n=127):(o+=132,n=255);var f=(a[o>>8&127]||8)-1,i=f<<4|o>>f+3&15;t[r]=i^n}return t},function(e){for(var t=new Int16Array(e.length),r=0;r<e.length;r++){var n=~e[r],a=132+((15&n)<<3);a<<=(112&n)>>4,t[r]=128&n?132-a:a-132}return t})}(t,0,r.$T)}();