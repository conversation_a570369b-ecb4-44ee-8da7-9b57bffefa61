/*
錄音
https://github.com/xiangyuecn/Recorder
src: engine/mp3.js,engine/mp3-engine.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,a=t.i18n;!function(e,t,a,s){"use strict";var n="48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000",r="8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160, 192, 224, 256, 320";e.prototype.enc_mp3={stable:!0,takeEC:"full",getTestMsg:function(){return a("Zm7L::采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）",0,n,r)}};var i,_=function(t){var s=t.bitRate,i=t.sampleRate,_=i;if(-1==(" "+r+",").indexOf(" "+s+",")&&e.CLog(a("eGB9::{1}不在mp3支持的取值范围：{2}",0,"bitRate="+s,r),3),-1==(" "+n+",").indexOf(" "+i+",")){for(var o=n.split(", "),l=[],f=0;f<o.length;f++)l.push({v:+o[f],s:Math.abs(o[f]-i)});l.sort(function(e,t){return e.s-t.s}),_=l[0].v,t.sampleRate=_,e.CLog(a("zLTa::sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}",0,_,i,n),3)}},o=function(){return a.G("NeedImport-2",["mp3.js","src/engine/mp3-engine.js"])},l=s&&"function"==typeof Worker;e.prototype.mp3=function(t,a,s){var n=this,r=n.set,i=t.length;if(e.lamejs){if(l){var f=n.mp3_start(r);if(f){if(f.isW)return n.mp3_encode(f,t),void n.mp3_complete(f,a,s,1);n.mp3_stop(f)}}_(r);var c=new e.lamejs.Mp3Encoder(1,r.sampleRate,r.bitRate),b=57600,p=new Int8Array(5e5),m=0,d=0,v=0,g=function(){try{if(d<i)var e=c.encodeBuffer(t.subarray(d,d+b));else{v=1;e=c.flush()}}catch(e){if(console.error(e),!v)try{c.flush()}catch(e){console.error(e)}return void s("MP3 Encoder: "+e.message)}var n=e.length;if(n>0){if(m+n>p.length){var _=new Int8Array(p.length+Math.max(5e5,n));_.set(p.subarray(0,m)),p=_}p.set(e,m),m+=n}if(d<i)d+=b,setTimeout(g);else{var o=[p.buffer.slice(0,m)],l=u.fn(o,m,i,r.sampleRate);h(l,r),a(o[0]||new ArrayBuffer(0),"audio/mp3")}};g()}else s(o())},e.BindDestroy("mp3Worker",function(){i&&(e.CLog("mp3Worker Destroy"),i.terminate(),i=null)}),e.prototype.mp3_envCheck=function(t,s){var n="";return s.takeoffEncodeChunk&&(c()||(n=a("yhUs::当前浏览器版本太低，无法实时处理"))),n||e.lamejs||(n=o()),n},e.prototype.mp3_start=function(e){return c(e)};var f={id:0},c=function(t,s){var n,r=function(e){var t=e.data,a=n.wkScope.wk_ctxs,s=n.wkScope.wk_lame,r=n.wkScope.wk_mp3TrimFix,i=a[t.id];if("init"==t.action)a[t.id]={sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:t.takeoff,pcmSize:0,memory:new Int8Array(5e5),mOffset:0,encObj:new s.Mp3Encoder(1,t.sampleRate,t.bitRate)};else if(!i)return;var _=function(e){var t=e.length;if(i.mOffset+t>i.memory.length){var a=new Int8Array(i.memory.length+Math.max(5e5,t));a.set(i.memory.subarray(0,i.mOffset)),i.memory=a}i.memory.set(e,i.mOffset),i.mOffset+=t};switch(t.action){case"stop":if(!i.isCp)try{i.encObj.flush()}catch(e){console.error(e)}i.encObj=null,delete a[t.id];break;case"encode":if(i.isCp)break;i.pcmSize+=t.pcm.length;try{var o=i.encObj.encodeBuffer(t.pcm)}catch(e){i.err=e,console.error(e)}o&&o.length>0&&(i.takeoff?b.onmessage({action:"takeoff",id:t.id,chunk:o}):_(o));break;case"complete":i.isCp=1;try{o=i.encObj.flush()}catch(e){i.err=e,console.error(e)}if(o&&o.length>0&&(i.takeoff?b.onmessage({action:"takeoff",id:t.id,chunk:o}):_(o)),i.err){b.onmessage({action:t.action,id:t.id,err:"MP3 Encoder: "+i.err.message});break}var l=[i.memory.buffer.slice(0,i.mOffset)],f=r.fn(l,i.mOffset,i.pcmSize,i.sampleRate);b.onmessage({action:t.action,id:t.id,blob:l[0]||new ArrayBuffer(0),meta:f})}},o=function(e){b.onmessage=function(t){var a=t;e&&(a=t.data);var s=f[a.id];s&&("takeoff"==a.action?s.set.takeoffEncodeChunk(new Uint8Array(a.chunk.buffer)):(s.call&&s.call(a),s.call=null))}},h=function(){var e={worker:b,set:t};return t?(e.id=++f.id,f[e.id]=e,_(t),b.postMessage({action:"init",id:e.id,sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:!!t.takeoffEncodeChunk,x:new Int16Array(5)})):b.postMessage({x:new Int16Array(5)}),e},b=i;if(s||!l)return e.CLog(a("k9PT::当前环境不支持Web Worker，mp3实时编码器运行在主线程中"),3),b={postMessage:function(e){r({data:e})}},n={wkScope:{wk_ctxs:{},wk_lame:e.lamejs,wk_mp3TrimFix:u}},o(),h();try{if(!b){var p=(r+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),m=");wk_lame();self.onmessage="+(p=p.replace(/[\w\$]+\.wkScope/g,"wkScope"));m+=";var wkScope={ wk_ctxs:{},wk_lame:wk_lame",m+=",wk_mp3TrimFix:{rm:"+u.rm+",fn:"+u.fn+"} }";var d=e.lamejs.toString(),v=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_lame=(",d,m],{type:"text/javascript"}));b=new Worker(v),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(v)},1e4),o(1)}var g=h();return g.isW=1,i=b,g}catch(e){return b&&b.terminate(),console.error(e),c(t,1)}};e.prototype.mp3_stop=function(t){if(t&&t.worker){t.worker.postMessage({action:"stop",id:t.id}),t.worker=null,delete f[t.id];var s=-1;for(var n in f)s++;s&&e.CLog(a("fT6M::mp3 worker剩{1}个未stop",0,s),3)}},e.prototype.mp3_encode=function(e,t){e&&e.worker&&e.worker.postMessage({action:"encode",id:e.id,pcm:t})},e.prototype.mp3_complete=function(e,t,s,n){var r=this;e&&e.worker?(e.call=function(a){n&&r.mp3_stop(e),a.err?s(a.err):(h(a.meta,e.set),t(a.blob,"audio/mp3"))},e.worker.postMessage({action:"complete",id:e.id})):s(a("mPxH::mp3编码器未start"))},e.mp3ReadMeta=function(e,t){var a="undefined"!=typeof window&&window.parseInt||"undefined"!=typeof self&&self.parseInt||parseInt,s=new Uint8Array(e[0]||[]);if(s.length<4)return null;var n=function(e,t){return("0000000"+((t||s)[e]||0).toString(2)).substr(-8)},r=n(0)+n(1),i=n(2)+n(3);if(!/^1{11}/.test(r))return null;var _={"00":2.5,10:2,11:1}[r.substr(11,2)],o={"01":3}[r.substr(13,2)],l={1:[44100,48e3,32e3],2:[22050,24e3,16e3],2.5:[11025,12e3,8e3]}[_];l&&(l=l[a(i.substr(4,2),2)]);var f=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320]][1==_?1:0][a(i.substr(0,4),2)];if(!(_&&o&&f&&l))return null;for(var c=Math.round(8*t/f),u=1==o?384:2==o||1==_?1152:576,h=u/l*1e3,b=Math.floor(u*f/8/l*1e3),p=0,m=0,d=0;d<e.length;d++){var v=e[d];if((m+=v.byteLength)>=b+3){var g=new Uint8Array(v);p="1"==n(v.byteLength-(m-(b+3)+1),g).charAt(6);break}}return p&&b++,{version:_,layer:o,sampleRate:l,bitRate:f,duration:c,size:t,hasPadding:p,frameSize:b,frameDurationFloat:h}};var u={rm:e.mp3ReadMeta,fn:function(e,t,a,s){var n=this.rm(e,t);if(!n)return{size:t,err:"mp3 unknown format"};var r=Math.round(a/s*1e3),i=Math.floor((n.duration-r)/n.frameDurationFloat);if(i>0){var _=i*n.frameSize-(n.hasPadding?1:0);t-=_;for(var o=0,l=[],f=0;f<e.length;f++){var c=e[f];if(_<=0)break;_>=c.byteLength?(_-=c.byteLength,l.push(c),e.splice(f,1),f--):(e[f]=c.slice(_),o=c,_=0)}if(!this.rm(e,t)){o&&(e[0]=o);for(f=0;f<l.length;f++)e.splice(f,0,l[f]);n.err="mp3 fix error: 已还原，错误原因不明"}var u=n.trimFix={};u.remove=i,u.removeDuration=Math.round(i*n.frameDurationFloat),u.duration=Math.round(8*t/n.bitRate)}return n}},h=function(t,s){var n="MP3 Info: ";(t.sampleRate&&t.sampleRate!=s.sampleRate||t.bitRate&&t.bitRate!=s.bitRate)&&(e.CLog(n+a("uY9i::和设置的不匹配{1}，已更新成{2}",0,"set:"+s.bitRate+"kbps "+s.sampleRate+"hz","set:"+t.bitRate+"kbps "+t.sampleRate+"hz"),3,s),s.sampleRate=t.sampleRate,s.bitRate=t.bitRate);var r=t.trimFix;r?(n+=a("iMSm::Fix移除{1}帧",0,r.remove)+" "+r.removeDuration+"ms -> "+r.duration+"ms",r.remove>2&&(t.err=(t.err?t.err+", ":"")+a("b9zm::移除帧数过多"))):n+=(t.duration||"-")+"ms",t.err?e.CLog(n,t.size?1:0,t.err,t):e.CLog(n,t)}}(t,0,a.$T,e)}(),function(e){"use strict";function t(){var e=function(e){return Math.log(e)/Math.log(10)},a=function(e){throw new Error("abort("+e+")")};function s(e){return new Int8Array(e)}function n(e){return new Int16Array(e)}function r(e){return new Int32Array(e)}function i(e){return new Float32Array(e)}function _(e){return new Float64Array(e)}function o(e){if(1==e.length)return i(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(o(e));return a}function l(e){if(1==e.length)return r(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(l(e));return a}function f(e){if(1==e.length)return n(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(f(e));return a}function c(e){if(1==e.length)return new Array(e[0]);var t=e[0];e=e.slice(1);for(var a=[],s=0;s<t;s++)a.push(c(e));return a}var u={fill:function(e,t,a,s){if(2==arguments.length)for(var n=0;n<e.length;n++)e[n]=arguments[1];else for(n=t;n<a;n++)e[n]=s}},h={arraycopy:function(e,t,a,s,n){for(var r=t+n;t<r;)a[s++]=e[t++]}},b={};function p(e){this.ordinal=e}b.SQRT2=1.4142135623730951,b.FAST_LOG10=function(t){return e(t)},b.FAST_LOG10_X=function(t,a){return e(t)*a},p.short_block_allowed=new p(0),p.short_block_coupled=new p(1),p.short_block_dispensed=new p(2),p.short_block_forced=new p(3);var m={};function d(e){this.ordinal=e}function v(e){var t=e;this.ordinal=function(){return t}}function g(){var e=3,t=98,a=4;this.getLameShortVersion=function(){return e+"."+t+"."+a}}function w(){var e=null;function t(e){this.bits=0|e}this.qupvt=null,this.setModules=function(t){this.qupvt=t,e=t};var s=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function n(e,t,a,s,n,r){var i=.5946/t;for(e>>=1;0!=e--;)n[r++]=i>a[s++]?0:1,n[r++]=i>a[s++]?0:1}function i(t,a,s,n,r,i){var _=(t>>=1)%2;for(t>>=1;0!=t--;){var o,l,f,c,u,h,b,p;o=s[n++]*a,l=s[n++]*a,u=0|o,f=s[n++]*a,h=0|l,c=s[n++]*a,b=0|f,o+=e.adj43[u],p=0|c,l+=e.adj43[h],r[i++]=0|o,f+=e.adj43[b],r[i++]=0|l,c+=e.adj43[p],r[i++]=0|f,r[i++]=0|c}0!=_&&(u=0|(o=s[n++]*a),h=0|(l=s[n++]*a),o+=e.adj43[u],l+=e.adj43[h],r[i++]=0|o,r[i++]=0|l)}function _(t,s,r,_,o){var l,f,c,h=0,b=0,p=0,m=0,d=s,v=0,g=d,w=0,S=t,M=0;for(c=null!=o&&_.global_gain==o.global_gain,f=_.block_type==F.SHORT_TYPE?38:21,l=0;l<=f;l++){var A=-1;if((c||_.block_type==F.NORM_TYPE)&&(A=_.global_gain-(_.scalefac[l]+(0!=_.preflag?e.pretab[l]:0)<<_.scalefac_scale+1)-8*_.subblock_gain[_.window[l]]),c&&o.step[l]==A)0!=b&&(i(b,r,S,M,g,w),b=0),0!=p&&a();else{var B,R=_.width[l];if(h+_.width[l]>_.max_nonzero_coeff&&(B=_.max_nonzero_coeff-h+1,u.fill(s,_.max_nonzero_coeff,576,0),(R=B)<0&&(R=0),l=f+1),0==b&&0==p&&(g=d,w=v,S=t,M=m),null!=o&&o.sfb_count1>0&&l>=o.sfb_count1&&o.step[l]>0&&A>=o.step[l]?(0!=b&&(i(b,r,S,M,g,w),b=0,g=d,w=v,S=t,M=m),p+=R):(0!=p&&(n(p,r,S,M,g,w),p=0,g=d,w=v,S=t,M=m),b+=R),R<=0){0!=p&&a(),0!=b&&a();break}}l<=f&&(v+=_.width[l],m+=_.width[l],h+=_.width[l])}0!=b&&(i(b,r,S,M,g,w),b=0),0!=p&&a()}function o(e,t,a){var s=0,n=0;do{var r=e[t++],i=e[t++];s<r&&(s=r),n<i&&(n=i)}while(t<a);return s<n&&(s=n),s}function l(e,t,a,s,n,r){var i,_=65536*T.ht[s].xlen+T.ht[n].xlen,o=0;do{var l=e[t++],f=e[t++];0!=l&&(l>14&&(l=15,o+=_),l*=16),0!=f&&(f>14&&(f=15,o+=_),l+=f),o+=T.largetbl[l]}while(t<a);return i=65535&o,(o>>=16)>i&&(o=i,s=n),r.bits+=o,s}function f(e,t,a,s){var n=0,r=T.ht[1].hlen;do{var i=2*e[t+0]+e[t+1];t+=2,n+=r[i]}while(t<a);return s.bits+=n,1}function c(e,t,a,s,n){var r,i,_=0,o=T.ht[s].xlen;i=2==s?T.table23:T.table56;do{var l=e[t+0]*o+e[t+1];t+=2,_+=i[l]}while(t<a);return r=65535&_,(_>>=16)>r&&(_=r,s++),n.bits+=_,s}function h(e,t,a,s,n){var r=0,i=0,_=0,o=T.ht[s].xlen,l=T.ht[s].hlen,f=T.ht[s+1].hlen,c=T.ht[s+2].hlen;do{var u=e[t+0]*o+e[t+1];t+=2,r+=l[u],i+=f[u],_+=c[u]}while(t<a);var h=s;return r>i&&(r=i,h++),r>_&&(r=_,h=s+2),n.bits+=r,h}var b=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function p(e,t,s,n){var r=o(e,t,s);switch(r){case 0:return r;case 1:return f(e,t,s,n);case 2:case 3:return c(e,t,s,b[r-1],n);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return h(e,t,s,b[r-1],n);default:var i,_;for(r>V.IXMAX_VAL&&a(),r-=15,i=24;i<32&&!(T.ht[i].linmax>=r);i++);for(_=i-8;_<24&&!(T.ht[_].linmax>=r);_++);return l(e,t,s,_,i,n)}}function m(e,a,s,n,r,i,_){for(var o=a.big_values,l=0;l<=22;l++)n[l]=V.LARGE_BITS;for(l=0;l<16;l++){var f=e.scalefac_band.l[l+1];if(f>=o)break;var c=0,u=new t(c),h=p(s,0,f,u);c=u.bits;for(var b=0;b<8;b++){var m=e.scalefac_band.l[l+b+2];if(m>=o)break;var d=c,v=p(s,f,m,u=new t(d));d=u.bits,n[l+b]>d&&(n[l+b]=d,r[l+b]=l,i[l+b]=h,_[l+b]=v)}}}function d(e,a,s,n,r,i,_,o){for(var l=a.big_values,f=2;f<F.SBMAX_l+1;f++){var c=e.scalefac_band.l[f];if(c>=l)break;var u=r[f-2]+a.count1bits;if(s.part2_3_length<=u)break;var h=new t(u),b=p(n,c,l,h);u=h.bits,s.part2_3_length<=u||(s.assign(a),s.part2_3_length=u,s.region0_count=i[f-2],s.region1_count=f-2-i[f-2],s.table_select[0]=_[f-2],s.table_select[1]=o[f-2],s.table_select[2]=b)}}this.noquant_count_bits=function(e,s,n){var r=s.l3_enc,i=Math.min(576,s.max_nonzero_coeff+2>>1<<1);for(null!=n&&(n.sfb_count1=0);i>1&&0==(r[i-1]|r[i-2]);i-=2);s.count1=i;for(var _=0,o=0;i>3;i-=4){var l;if((2147483647&(r[i-1]|r[i-2]|r[i-3]|r[i-4]))>1)break;l=2*(2*(2*r[i-4]+r[i-3])+r[i-2])+r[i-1],_+=T.t32l[l],o+=T.t33l[l]}var f=_;if(s.count1table_select=0,_>o&&(f=o,s.count1table_select=1),s.count1bits=f,s.big_values=i,0==i)return f;if(s.block_type==F.SHORT_TYPE)(_=3*e.scalefac_band.s[3])>s.big_values&&(_=s.big_values),o=s.big_values;else if(s.block_type==F.NORM_TYPE){if(_=s.region0_count=e.bv_scf[i-2],o=s.region1_count=e.bv_scf[i-1],o=e.scalefac_band.l[_+o+2],_=e.scalefac_band.l[_+1],o<i){var c=new t(f);s.table_select[2]=p(r,o,i,c),f=c.bits}}else s.region0_count=7,s.region1_count=F.SBMAX_l-1-7-1,(_=e.scalefac_band.l[8])>(o=i)&&(_=o);if(_=Math.min(_,i),o=Math.min(o,i),0<_&&(c=new t(f),s.table_select[0]=p(r,0,_,c),f=c.bits),_<o&&(c=new t(f),s.table_select[1]=p(r,_,o,c),f=c.bits),2==e.use_best_huffman&&a(),null!=n&&s.block_type==F.NORM_TYPE){for(var u=0;e.scalefac_band.l[u]<s.big_values;)u++;n.sfb_count1=u}return f},this.count_bits=function(t,s,n,r){var i=n.l3_enc,o=V.IXMAX_VAL/e.IPOW20(n.global_gain);return n.xrpow_max>o?V.LARGE_BITS:(_(s,i,e.IPOW20(n.global_gain),n,r),2&t.substep_shaping&&a(),this.noquant_count_bits(t,n,r))},this.best_huffman_divide=function(e,a){var s=new D,n=a.l3_enc,i=r(23),_=r(23),o=r(23),l=r(23);if(a.block_type!=F.SHORT_TYPE||1!=e.mode_gr){s.assign(a),a.block_type==F.NORM_TYPE&&(m(e,a,n,i,_,o,l),d(e,s,a,n,i,_,o,l));var f=s.big_values;if(!(0==f||(n[f-2]|n[f-1])>1||(f=a.count1+2)>576)){s.assign(a),s.count1=f;for(var c=0,u=0;f>s.big_values;f-=4){var h=2*(2*(2*n[f-4]+n[f-3])+n[f-2])+n[f-1];c+=T.t32l[h],u+=T.t33l[h]}if(s.big_values=f,s.count1table_select=0,c>u&&(c=u,s.count1table_select=1),s.count1bits=c,s.block_type==F.NORM_TYPE)d(e,s,a,n,i,_,o,l);else{if(s.part2_3_length=c,(c=e.scalefac_band.l[8])>f&&(c=f),c>0){var b=new t(s.part2_3_length);s.table_select[0]=p(n,0,c,b),s.part2_3_length=b.bits}f>c&&(b=new t(s.part2_3_length),s.table_select[1]=p(n,c,f,b),s.part2_3_length=b.bits),a.part2_3_length>s.part2_3_length&&a.assign(s)}}}};var v=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],g=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],S=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],M=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];function A(e,t){for(var a,s=t.tt[1][e],n=t.tt[0][e],r=0;r<T.scfsi_band.length-1;r++){for(a=T.scfsi_band[r];a<T.scfsi_band[r+1]&&!(n.scalefac[a]!=s.scalefac[a]&&s.scalefac[a]>=0);a++);if(a==T.scfsi_band[r+1]){for(a=T.scfsi_band[r];a<T.scfsi_band[r+1];a++)s.scalefac[a]=-1;t.scfsi[e][r]=1}}var i=0,_=0;for(a=0;a<11;a++)-1!=s.scalefac[a]&&(_++,i<s.scalefac[a]&&(i=s.scalefac[a]));for(var o=0,l=0;a<F.SBPSY_l;a++)-1!=s.scalefac[a]&&(l++,o<s.scalefac[a]&&(o=s.scalefac[a]));for(r=0;r<16;r++)if(i<v[r]&&o<g[r]){var f=S[r]*_+M[r]*l;s.part2_length>f&&(s.part2_length=f,s.scalefac_compress=r)}}w.slen1_tab=S,w.slen2_tab=M,this.best_scalefac_store=function(t,a,s,n){var r,i,_,o,l=n.tt[a][s],f=0;for(_=0,r=0;r<l.sfbmax;r++){var c=l.width[r];for(_+=c,o=-c;o<0&&0==l.l3_enc[o+_];o++);0==o&&(l.scalefac[r]=f=-2)}if(0==l.scalefac_scale&&0==l.preflag){var u=0;for(r=0;r<l.sfbmax;r++)l.scalefac[r]>0&&(u|=l.scalefac[r]);if(!(1&u)&&0!=u){for(r=0;r<l.sfbmax;r++)l.scalefac[r]>0&&(l.scalefac[r]>>=1);l.scalefac_scale=f=1}}if(0==l.preflag&&l.block_type!=F.SHORT_TYPE&&2==t.mode_gr){for(r=11;r<F.SBPSY_l&&!(l.scalefac[r]<e.pretab[r]&&-2!=l.scalefac[r]);r++);if(r==F.SBPSY_l){for(r=11;r<F.SBPSY_l;r++)l.scalefac[r]>0&&(l.scalefac[r]-=e.pretab[r]);l.preflag=f=1}}for(i=0;i<4;i++)n.scfsi[s][i]=0;for(2==t.mode_gr&&1==a&&n.tt[0][s].block_type!=F.SHORT_TYPE&&n.tt[1][s].block_type!=F.SHORT_TYPE&&(A(s,n),f=0),r=0;r<l.sfbmax;r++)-2==l.scalefac[r]&&(l.scalefac[r]=0);0!=f&&(2==t.mode_gr?this.scale_bitcount(l):this.scale_bitcount_lsf(t,l))};var B=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],R=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],k=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(t){var a,s,n,r=0,i=0,_=t.scalefac;if(t.block_type==F.SHORT_TYPE)n=B,0!=t.mixed_block_flag&&(n=R);else if(n=k,0==t.preflag){for(s=11;s<F.SBPSY_l&&!(_[s]<e.pretab[s]);s++);if(s==F.SBPSY_l)for(t.preflag=1,s=11;s<F.SBPSY_l;s++)_[s]-=e.pretab[s]}for(s=0;s<t.sfbdivide;s++)r<_[s]&&(r=_[s]);for(;s<t.sfbmax;s++)i<_[s]&&(i=_[s]);for(t.part2_length=V.LARGE_BITS,a=0;a<16;a++)r<v[a]&&i<g[a]&&t.part2_length>n[a]&&(t.part2_length=n[a],t.scalefac_compress=a);return t.part2_length==V.LARGE_BITS};var y=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(t,a){var s,n,i,_,o,l,f,c,u=r(4),h=a.scalefac;for(s=0!=a.preflag?2:0,f=0;f<4;f++)u[f]=0;if(a.block_type==F.SHORT_TYPE){n=1;var b=e.nr_of_sfb_block[s][n];for(c=0,i=0;i<4;i++)for(_=b[i]/3,f=0;f<_;f++,c++)for(o=0;o<3;o++)h[3*c+o]>u[i]&&(u[i]=h[3*c+o])}else for(n=0,b=e.nr_of_sfb_block[s][n],c=0,i=0;i<4;i++)for(_=b[i],f=0;f<_;f++,c++)h[c]>u[i]&&(u[i]=h[c]);for(l=!1,i=0;i<4;i++)u[i]>y[s][i]&&(l=!0);if(!l){var p,m,d,v;for(a.sfb_partition_table=e.nr_of_sfb_block[s][n],i=0;i<4;i++)a.slen[i]=E[u[i]];switch(p=a.slen[0],m=a.slen[1],d=a.slen[2],v=a.slen[3],s){case 0:a.scalefac_compress=(5*p+m<<4)+(d<<2)+v;break;case 1:a.scalefac_compress=400+(5*p+m<<2)+d;break;case 2:a.scalefac_compress=500+3*p+m}}if(!l)for(a.part2_length=0,i=0;i<4;i++)a.part2_length+=a.slen[i]*a.sfb_partition_table[i];return l};var E=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(e){for(var t=2;t<=576;t+=2){for(var a,n=0;e.scalefac_band.l[++n]<t;);for(a=s[n][0];e.scalefac_band.l[a+1]>t;)a--;for(a<0&&(a=s[n][0]),e.bv_scf[t-2]=a,a=s[n][1];e.scalefac_band.l[a+e.bv_scf[t-2]+2]>t;)a--;a<0&&(a=s[n][1]),e.bv_scf[t-1]=a}}}function S(){}function M(){function e(e,t,a,s,n,r,i,_,o,l,f,c,u,h){this.quant_comp=t,this.quant_comp_s=a,this.safejoint=s,this.nsmsfix=n,this.st_lrm=r,this.st_s=i,this.nsbass=_,this.scale=o,this.masking_adj=l,this.ath_lower=f,this.ath_curve=c,this.interch=u,this.sfscale=h}var t;function s(e,t,s){a()}this.setModules=function(e){t=e};var n=[new e(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new e(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new e(24,9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new e(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new e(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8e-4,1),new e(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8e-4,1),new e(80,9,9,0,0,6.6,145,0,.95,0,0,8,7e-4,1),new e(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6e-4,1),new e(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5e-4,1),new e(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2e-4,1),new e(160,9,9,1,1.79,6,135,0,.95,-2,5,3.5,0,1),new e(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new e(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new e(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new e(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];function r(e,a,s){var r=a,i=t.nearestBitrateFullIndex(a);if(e.VBR=d.vbr_abr,e.VBR_mean_bitrate_kbps=r,e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320),e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.brate=e.VBR_mean_bitrate_kbps,e.VBR_mean_bitrate_kbps>320&&(e.disable_reservoir=!0),n[i].safejoint>0&&(e.exp_nspsytune=2|e.exp_nspsytune),n[i].sfscale>0&&(e.internal_flags.noise_shaping=2),Math.abs(n[i].nsbass)>0){var _=int(4*n[i].nsbass);_<0&&(_+=64),e.exp_nspsytune=e.exp_nspsytune|_<<2}return 0!=s?e.quant_comp=n[i].quant_comp:Math.abs(e.quant_comp- -1)>0||(e.quant_comp=n[i].quant_comp),0!=s?e.quant_comp_short=n[i].quant_comp_s:Math.abs(e.quant_comp_short- -1)>0||(e.quant_comp_short=n[i].quant_comp_s),0!=s?e.msfix=n[i].nsmsfix:Math.abs(e.msfix- -1)>0||(e.msfix=n[i].nsmsfix),0!=s?e.internal_flags.nsPsy.attackthre=n[i].st_lrm:Math.abs(e.internal_flags.nsPsy.attackthre- -1)>0||(e.internal_flags.nsPsy.attackthre=n[i].st_lrm),0!=s?e.internal_flags.nsPsy.attackthre_s=n[i].st_s:Math.abs(e.internal_flags.nsPsy.attackthre_s- -1)>0||(e.internal_flags.nsPsy.attackthre_s=n[i].st_s),0!=s?e.scale=n[i].scale:Math.abs(e.scale- -1)>0||(e.scale=n[i].scale),0!=s?e.maskingadjust=n[i].masking_adj:Math.abs(e.maskingadjust-0)>0||(e.maskingadjust=n[i].masking_adj),n[i].masking_adj>0?0!=s?e.maskingadjust_short=.9*n[i].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=.9*n[i].masking_adj):0!=s?e.maskingadjust_short=1.1*n[i].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=1.1*n[i].masking_adj),0!=s?e.ATHlower=-n[i].ath_lower/10:Math.abs(10*-e.ATHlower-0)>0||(e.ATHlower=-n[i].ath_lower/10),0!=s?e.ATHcurve=n[i].ath_curve:Math.abs(e.ATHcurve- -1)>0||(e.ATHcurve=n[i].ath_curve),0!=s?e.interChRatio=n[i].interch:Math.abs(e.interChRatio- -1)>0||(e.interChRatio=n[i].interch),a}this.apply_preset=function(e,t,a){switch(t){case W.R3MIX:t=W.V3,e.VBR=d.vbr_mtrh;break;case W.MEDIUM:t=W.V4,e.VBR=d.vbr_rh;break;case W.MEDIUM_FAST:t=W.V4,e.VBR=d.vbr_mtrh;break;case W.STANDARD:t=W.V2,e.VBR=d.vbr_rh;break;case W.STANDARD_FAST:t=W.V2,e.VBR=d.vbr_mtrh;break;case W.EXTREME:t=W.V0,e.VBR=d.vbr_rh;break;case W.EXTREME_FAST:t=W.V0,e.VBR=d.vbr_mtrh;break;case W.INSANE:return t=320,e.preset=t,r(e,t,a),e.VBR=d.vbr_off,t}switch(e.preset=t,t){case W.V9:return s(e,9,a),t;case W.V8:return s(e,8,a),t;case W.V7:return s(e,7,a),t;case W.V6:return s(e,6,a),t;case W.V5:return s(e,5,a),t;case W.V4:return s(e,4,a),t;case W.V3:return s(e,3,a),t;case W.V2:return s(e,2,a),t;case W.V1:return s(e,1,a),t;case W.V0:return s(e,0,a),t}return 8<=t&&t<=320?r(e,t,a):(e.preset=0,t)}}function A(){var e;this.setModules=function(t){e=t},this.ResvFrameBegin=function(t,s){var n,r=t.internal_flags,i=r.l3_side,_=e.getframebits(t);s.bits=(_-8*r.sideinfo_len)/r.mode_gr;var o=2048*r.mode_gr-8;t.brate>320?a():(n=11520,t.strict_ISO&&a()),r.ResvMax=n-_,r.ResvMax>o&&(r.ResvMax=o),(r.ResvMax<0||t.disable_reservoir)&&(r.ResvMax=0);var l=s.bits*r.mode_gr+Math.min(r.ResvSize,r.ResvMax);return l>n&&(l=n),i.resvDrain_pre=0,null!=r.pinfo&&a(),l},this.ResvMaxBits=function(e,t,a,s){var n,r=e.internal_flags,i=r.ResvSize,_=r.ResvMax;0!=s&&(i+=t),1&r.substep_shaping&&(_*=.9),a.bits=t,10*i>9*_?(n=i-9*_/10,a.bits+=n,r.substep_shaping|=128):(n=0,r.substep_shaping&=127,e.disable_reservoir||1&r.substep_shaping||(a.bits-=.1*t));var o=i<6*r.ResvMax/10?i:6*r.ResvMax/10;return(o-=n)<0&&(o=0),o},this.ResvAdjust=function(e,t){e.ResvSize-=t.part2_3_length+t.part2_length},this.ResvFrameEnd=function(e,t){var a,s=e.l3_side;e.ResvSize+=t*e.mode_gr;var n=0;s.resvDrain_post=0,s.resvDrain_pre=0,0!=(a=e.ResvSize%8)&&(n+=a),(a=e.ResvSize-n-e.ResvMax)>0&&(n+=a);var r=Math.min(8*s.main_data_begin,n)/8;s.resvDrain_pre+=8*r,n-=8*r,e.ResvSize-=8*r,s.main_data_begin-=r,s.resvDrain_post+=n,e.ResvSize-=n}}function B(){this.setModules=function(e,t,a){};var e=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];function t(t,a){return a=a>>8^e[255&(a^t)]}this.updateMusicCRC=function(e,a,s,n){for(var r=0;r<n;++r)e[0]=t(a[s+r],e[0])}}function R(){var e=this,t=null,n=null;this.setModules=function(e,a,s,r){t=s,n=r};var i=null,_=0,o=0,l=0;function f(e){h.arraycopy(e.header[e.w_ptr].buf,0,i,o,e.sideinfo_len),o+=e.sideinfo_len,_+=8*e.sideinfo_len,e.w_ptr=e.w_ptr+1&G.MAX_HEADER_BUF-1}function c(e,t,a){for(;a>0;){var s;0==l&&(l=8,o++,e.header[e.w_ptr].write_timing==_&&f(e),i[o]=0),a-=s=Math.min(a,l),l-=s,i[o]|=t>>a<<l,_+=s}}function b(e,a){var s,n=e.internal_flags;if(a>=8&&(c(n,76,8),a-=8),a>=8&&(c(n,65,8),a-=8),a>=8&&(c(n,77,8),a-=8),a>=8&&(c(n,69,8),a-=8),a>=32){var r=t.getLameShortVersion();if(a>=32)for(s=0;s<r.length&&a>=8;++s)a-=8,c(n,r.charCodeAt(s),8)}for(;a>=1;a-=1)c(n,n.ancillary_flag,1),n.ancillary_flag^=e.disable_reservoir?0:1}function p(e,t,a){for(var s=e.header[e.h_ptr].ptr;a>0;){var n=Math.min(a,8-(7&s));a-=n,e.header[e.h_ptr].buf[s>>3]|=t>>a<<8-(7&s)-n,s+=n}e.header[e.h_ptr].ptr=s}function m(e,t){var s,n,r,i=e.internal_flags;if(s=i.l3_side,i.header[i.h_ptr].ptr=0,u.fill(i.header[i.h_ptr].buf,0,i.sideinfo_len,0),e.out_samplerate<16e3?p(i,4094,12):p(i,4095,12),p(i,e.version,1),p(i,1,2),p(i,e.error_protection?0:1,1),p(i,i.bitrate_index,4),p(i,i.samplerate_index,2),p(i,i.padding,1),p(i,e.extension,1),p(i,e.mode.ordinal(),2),p(i,i.mode_ext,2),p(i,e.copyright,1),p(i,e.original,1),p(i,e.emphasis,2),e.error_protection&&p(i,0,16),1==e.version){for(p(i,s.main_data_begin,9),2==i.channels_out?p(i,s.private_bits,3):p(i,s.private_bits,5),r=0;r<i.channels_out;r++){var _;for(_=0;_<4;_++)p(i,s.scfsi[r][_],1)}for(n=0;n<2;n++)for(r=0;r<i.channels_out;r++)p(i,(o=s.tt[n][r]).part2_3_length+o.part2_length,12),p(i,o.big_values/2,9),p(i,o.global_gain,8),p(i,o.scalefac_compress,4),o.block_type!=F.NORM_TYPE?(p(i,1,1),p(i,o.block_type,2),p(i,o.mixed_block_flag,1),14==o.table_select[0]&&(o.table_select[0]=16),p(i,o.table_select[0],5),14==o.table_select[1]&&(o.table_select[1]=16),p(i,o.table_select[1],5),p(i,o.subblock_gain[0],3),p(i,o.subblock_gain[1],3),p(i,o.subblock_gain[2],3)):(p(i,0,1),14==o.table_select[0]&&(o.table_select[0]=16),p(i,o.table_select[0],5),14==o.table_select[1]&&(o.table_select[1]=16),p(i,o.table_select[1],5),14==o.table_select[2]&&(o.table_select[2]=16),p(i,o.table_select[2],5),p(i,o.region0_count,4),p(i,o.region1_count,3)),p(i,o.preflag,1),p(i,o.scalefac_scale,1),p(i,o.count1table_select,1)}else for(p(i,s.main_data_begin,8),p(i,s.private_bits,i.channels_out),n=0,r=0;r<i.channels_out;r++){var o;p(i,(o=s.tt[n][r]).part2_3_length+o.part2_length,12),p(i,o.big_values/2,9),p(i,o.global_gain,8),p(i,o.scalefac_compress,9),o.block_type!=F.NORM_TYPE?(p(i,1,1),p(i,o.block_type,2),p(i,o.mixed_block_flag,1),14==o.table_select[0]&&(o.table_select[0]=16),p(i,o.table_select[0],5),14==o.table_select[1]&&(o.table_select[1]=16),p(i,o.table_select[1],5),p(i,o.subblock_gain[0],3),p(i,o.subblock_gain[1],3),p(i,o.subblock_gain[2],3)):(p(i,0,1),14==o.table_select[0]&&(o.table_select[0]=16),p(i,o.table_select[0],5),14==o.table_select[1]&&(o.table_select[1]=16),p(i,o.table_select[1],5),14==o.table_select[2]&&(o.table_select[2]=16),p(i,o.table_select[2],5),p(i,o.region0_count,4),p(i,o.region1_count,3)),p(i,o.scalefac_scale,1),p(i,o.count1table_select,1)}e.error_protection&&a();var l=i.h_ptr;i.h_ptr=l+1&G.MAX_HEADER_BUF-1,i.header[i.h_ptr].write_timing=i.header[l].write_timing+t,i.h_ptr,i.w_ptr}function d(e,t){var a,s=T.ht[t.count1table_select+32],n=0,r=t.big_values,i=t.big_values;for(a=(t.count1-t.big_values)/4;a>0;--a){var _=0,o=0;0!=t.l3_enc[r+0]&&(o+=8,t.xr[i+0]<0&&_++),0!=t.l3_enc[r+1]&&(o+=4,_*=2,t.xr[i+1]<0&&_++),0!=t.l3_enc[r+2]&&(o+=2,_*=2,t.xr[i+2]<0&&_++),0!=t.l3_enc[r+3]&&(o++,_*=2,t.xr[i+3]<0&&_++),r+=4,i+=4,c(e,_+s.table[o],s.hlen[o]),n+=s.hlen[o]}return n}function v(e,t,a,s,n){var r=T.ht[t],i=0;if(0==t)return i;for(var _=a;_<s;_+=2){var o=0,l=0,f=r.xlen,u=r.xlen,h=0,b=n.l3_enc[_],p=n.l3_enc[_+1];0!=b&&(n.xr[_]<0&&h++,o--),t>15&&(b>14&&(h|=b-15<<1,l=f,b=15),p>14&&(h<<=f,h|=p-15,l+=f,p=15),u=16),0!=p&&(h<<=1,n.xr[_+1]<0&&h++,o--),b=b*u+p,l-=o,o+=r.hlen[b],c(e,r.table[b],o),c(e,h,l),i+=o+l}return i}function g(e,t){var a=3*e.scalefac_band.s[3];a>t.big_values&&(a=t.big_values);var s=v(e,t.table_select[0],0,a,t);return s+=v(e,t.table_select[1],a,t.big_values,t)}function S(e,t){var a,s,n,r;a=t.big_values;var i=t.region0_count+1;return n=e.scalefac_band.l[i],i+=t.region1_count+1,n>a&&(n=a),(r=e.scalefac_band.l[i])>a&&(r=a),s=v(e,t.table_select[0],0,n,t),s+=v(e,t.table_select[1],n,r,t),s+=v(e,t.table_select[2],r,a,t)}function M(e){var t,a,s,n,r=0,i=e.internal_flags,_=i.l3_side;if(1==e.version)for(t=0;t<2;t++)for(a=0;a<i.channels_out;a++){var o=_.tt[t][a],l=w.slen1_tab[o.scalefac_compress],f=w.slen2_tab[o.scalefac_compress];for(n=0,s=0;s<o.sfbdivide;s++)-1!=o.scalefac[s]&&(c(i,o.scalefac[s],l),n+=l);for(;s<o.sfbmax;s++)-1!=o.scalefac[s]&&(c(i,o.scalefac[s],f),n+=f);o.block_type==F.SHORT_TYPE?n+=g(i,o):n+=S(i,o),r+=n+=d(i,o)}else for(t=0,a=0;a<i.channels_out;a++){var u,h,b=0;if(n=0,s=0,h=0,(o=_.tt[t][a]).block_type==F.SHORT_TYPE){for(;h<4;h++){var p=o.sfb_partition_table[h]/3,m=o.slen[h];for(u=0;u<p;u++,s++)c(i,Math.max(o.scalefac[3*s+0],0),m),c(i,Math.max(o.scalefac[3*s+1],0),m),c(i,Math.max(o.scalefac[3*s+2],0),m),b+=3*m}n+=g(i,o)}else{for(;h<4;h++)for(p=o.sfb_partition_table[h],m=o.slen[h],u=0;u<p;u++,s++)c(i,Math.max(o.scalefac[s],0),m),b+=m;n+=S(i,o)}r+=b+(n+=d(i,o))}return r}function A(){this.total=0}function B(t,s){var n,r,i,l=t.internal_flags;return l.w_ptr,-1==(i=l.h_ptr-1)&&(i=G.MAX_HEADER_BUF-1),n=l.header[i].write_timing-_,s.total=n,n>=0&&a(),n+=r=e.getframebits(t),s.total+=r,s.total%8!=0?s.total=1+s.total/8:s.total=s.total/8,s.total+=o+1,n}this.getframebits=function(e){var t,a=e.internal_flags;return t=0!=a.bitrate_index?T.bitrate_table[e.version][a.bitrate_index]:e.brate,8*(0|72e3*(e.version+1)*t/e.out_samplerate+a.padding)},this.flush_bitstream=function(e){var t,s,n=e.internal_flags,r=n.h_ptr-1;-1==r&&(r=G.MAX_HEADER_BUF-1),t=n.l3_side,(s=B(e,new A))<0||(b(e,s),n.ResvSize=0,t.main_data_begin=0,n.findReplayGain&&a(),n.findPeakSample&&a())},this.format_bitstream=function(e){var t,a=e.internal_flags;t=a.l3_side;var s=this.getframebits(e);b(e,t.resvDrain_pre),m(e,s);var n=8*a.sideinfo_len;if(n+=M(e),b(e,t.resvDrain_post),n+=t.resvDrain_post,t.main_data_begin+=(s-n)/8,B(e,new A),a.ResvSize,8*t.main_data_begin!=a.ResvSize&&(a.ResvSize=8*t.main_data_begin),_>1e9){var r;for(r=0;r<G.MAX_HEADER_BUF;++r)a.header[r].write_timing-=_;_=0}return 0},this.copy_buffer=function(e,t,s,_,f){var c=o+1;if(c<=0)return 0;if(0!=_&&c>_)return-1;if(h.arraycopy(i,0,t,s,c),o=-1,l=0,0!=f){var u=r(1);u[0]=e.nMusicCRC,n.updateMusicCRC(u,t,s,c),e.nMusicCRC=u[0],c>0&&(e.VBR_seek_table.nBytesWritten+=c),e.decode_on_the_fly&&a()}return c},this.init_bit_stream_w=function(e){i=s(W.LAME_MAXMP3BUFFER),e.h_ptr=e.w_ptr=0,e.header[e.h_ptr].write_timing=0,o=-1,l=0,_=0}}function k(e,t,a,s){this.xlen=e,this.linmax=t,this.table=a,this.hlen=s}m.MAX_VALUE=34028235e31,d.vbr_off=new d(0),d.vbr_mt=new d(1),d.vbr_rh=new d(2),d.vbr_abr=new d(3),d.vbr_mtrh=new d(4),d.vbr_default=d.vbr_mtrh,v.STEREO=new v(0),v.JOINT_STEREO=new v(1),v.DUAL_CHANNEL=new v(2),v.MONO=new v(3),v.NOT_SET=new v(4),S.STEPS_per_dB=100,S.MAX_dB=120,S.GAIN_NOT_ENOUGH_SAMPLES=-24601,S.GAIN_ANALYSIS_ERROR=0,S.GAIN_ANALYSIS_OK=1,S.INIT_GAIN_ANALYSIS_ERROR=0,S.INIT_GAIN_ANALYSIS_OK=1,S.YULE_ORDER=10,S.MAX_ORDER=S.YULE_ORDER,S.MAX_SAMP_FREQ=48e3,S.RMS_WINDOW_TIME_NUMERATOR=1,S.RMS_WINDOW_TIME_DENOMINATOR=20,S.MAX_SAMPLES_PER_WINDOW=S.MAX_SAMP_FREQ*S.RMS_WINDOW_TIME_NUMERATOR/S.RMS_WINDOW_TIME_DENOMINATOR+1,B.NUMTOCENTRIES=100,B.MAXFRAMESIZE=2880,R.EQ=function(e,t){return Math.abs(e)>Math.abs(t)?Math.abs(e-t)<=1e-6*Math.abs(e):Math.abs(e-t)<=1e-6*Math.abs(t)},R.NEQ=function(e,t){return!R.EQ(e,t)};var T={};function y(e){this.bits=e}function E(){this.over_noise=0,this.tot_noise=0,this.max_noise=0,this.over_count=0,this.over_SSD=0,this.bits=0}function x(){this.setModules=function(e,t){}}function H(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=i(F.SBMAX_l),this.s=i(F.SBMAX_s),this.psfb21=i(F.PSFB21),this.psfb12=i(F.PSFB12),this.cb_l=i(F.CBANDS),this.cb_s=i(F.CBANDS),this.eql_w=i(F.BLKSIZE/2)}function P(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=v.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null}function I(e){var t=e;this.quantize=t,this.iteration_loop=function(e,t,s,n){var _=e.internal_flags,o=i(Y.SFBMAX),l=i(576),f=r(2),c=0,u=_.l3_side,h=new y(c);this.quantize.rv.ResvFrameBegin(e,h),c=h.bits;for(var b=0;b<_.mode_gr;b++){this.quantize.qupvt.on_pe(e,t,f,c,b,b),_.mode_ext==F.MPG_MD_MS_LR&&a();for(var p=0;p<_.channels_out;p++){var m,d,v=u.tt[b][p];v.block_type!=F.SHORT_TYPE?(m=0,d=_.PSY.mask_adjust-m):(m=0,d=_.PSY.mask_adjust_short-m),_.masking_lower=Math.pow(10,.1*d),this.quantize.init_outer_loop(_,v),this.quantize.init_xrpow(_,v,l)&&(this.quantize.qupvt.calc_xmin(e,n[b][p],v,o),this.quantize.outer_loop(e,v,o,l,p,f[p])),this.quantize.iteration_finish_one(_,b,p)}}this.quantize.rv.ResvFrameEnd(_,c)}}function L(){}function O(e,t,a,s){this.l=r(1+F.SBMAX_l),this.s=r(1+F.SBMAX_s),this.psfb21=r(1+F.PSFB21),this.psfb12=r(1+F.PSFB12);var n=this.l,i=this.s;4==arguments.length&&(this.arrL=arguments[0],this.arrS=arguments[1],this.arr21=arguments[2],this.arr12=arguments[3],h.arraycopy(this.arrL,0,n,0,Math.min(this.arrL.length,this.l.length)),h.arraycopy(this.arrS,0,i,0,Math.min(this.arrS.length,this.s.length)),h.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),h.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function V(){var t=null,s=null,n=null;function _(e){return p[e+V.Q_MAX2]}this.setModules=function(e,a,r){t=e,s=a,n=r},this.IPOW20=function(e){return v[e]};var o=2220446049250313e-31,l=V.IXMAX_VAL+2,f=V.Q_MAX,c=V.Q_MAX2,u=(V.LARGE_BITS,100);this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var h=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=h,this.sfBandIndex=[new O([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new O([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new O([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new O([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new O([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new O([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new O([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new O([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new O([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var p=i(f+c+1),v=i(f),g=i(l),w=i(l);function S(e,t){var a=n.ATHformula(t,e);return a-=u,a=Math.pow(10,a/10+e.ATHlower)}function M(t){for(var s=t.internal_flags.ATH.l,n=t.internal_flags.ATH.psfb21,r=t.internal_flags.ATH.s,i=t.internal_flags.ATH.psfb12,_=t.internal_flags,o=t.out_samplerate,l=0;l<F.SBMAX_l;l++){var f=_.scalefac_band.l[l],c=_.scalefac_band.l[l+1];s[l]=m.MAX_VALUE;for(var u=f;u<c;u++){var h=S(t,u*o/1152);s[l]=Math.min(s[l],h)}}for(l=0;l<F.PSFB21;l++)for(f=_.scalefac_band.psfb21[l],c=_.scalefac_band.psfb21[l+1],n[l]=m.MAX_VALUE,u=f;u<c;u++)h=S(t,u*o/1152),n[l]=Math.min(n[l],h);for(l=0;l<F.SBMAX_s;l++){for(f=_.scalefac_band.s[l],c=_.scalefac_band.s[l+1],r[l]=m.MAX_VALUE,u=f;u<c;u++)h=S(t,u*o/384),r[l]=Math.min(r[l],h);r[l]*=_.scalefac_band.s[l+1]-_.scalefac_band.s[l]}for(l=0;l<F.PSFB12;l++){for(f=_.scalefac_band.psfb12[l],c=_.scalefac_band.psfb12[l+1],i[l]=m.MAX_VALUE,u=f;u<c;u++)h=S(t,u*o/384),i[l]=Math.min(i[l],h);i[l]*=_.scalefac_band.s[13]-_.scalefac_band.s[12]}t.noATH&&a(),_.ATH.floor=10*e(S(t,-1))}function A(e){this.s=e}this.adj43=w,this.iteration_init=function(e){var a,s=e.internal_flags,n=s.l3_side;if(0==s.iteration_init_init){for(s.iteration_init_init=1,n.main_data_begin=0,M(e),g[0]=0,a=1;a<l;a++)g[a]=Math.pow(a,4/3);for(a=0;a<l-1;a++)w[a]=a+1-Math.pow(.5*(g[a]+g[a+1]),.75);for(w[a]=.5,a=0;a<f;a++)v[a]=Math.pow(2,-.1875*(a-210));for(a=0;a<=f+c;a++)p[a]=Math.pow(2,.25*(a-210-c));var r,i,_,o;for(t.huffman_init(s),(a=e.exp_nspsytune>>2&63)>=32&&(a-=64),r=Math.pow(10,a/4/10),(a=e.exp_nspsytune>>8&63)>=32&&(a-=64),i=Math.pow(10,a/4/10),(a=e.exp_nspsytune>>14&63)>=32&&(a-=64),_=Math.pow(10,a/4/10),(a=e.exp_nspsytune>>20&63)>=32&&(a-=64),o=_*Math.pow(10,a/4/10),a=0;a<F.SBMAX_l;a++)u=a<=6?r:a<=13?i:a<=20?_:o,s.nsPsy.longfact[a]=u;for(a=0;a<F.SBMAX_s;a++){var u;u=a<=5?r:a<=10?i:a<=11?_:o,s.nsPsy.shortfact[a]=u}}},this.on_pe=function(e,t,n,i,_,o){var l,f,c=e.internal_flags,u=0,h=r(2),b=new y(u),p=s.ResvMaxBits(e,i,b,o),m=(u=b.bits)+p;for(m>G.MAX_BITS_PER_GRANULE&&(m=G.MAX_BITS_PER_GRANULE),l=0,f=0;f<c.channels_out;++f)n[f]=Math.min(G.MAX_BITS_PER_CHANNEL,u/c.channels_out),h[f]=0|n[f]*t[_][f]/700-n[f],h[f]>3*i/4&&(h[f]=3*i/4),h[f]<0&&(h[f]=0),h[f]+n[f]>G.MAX_BITS_PER_CHANNEL&&(h[f]=Math.max(0,G.MAX_BITS_PER_CHANNEL-n[f])),l+=h[f];if(l>p)for(f=0;f<c.channels_out;++f)h[f]=p*h[f]/l;for(f=0;f<c.channels_out;++f)n[f]+=h[f],p-=h[f];for(l=0,f=0;f<c.channels_out;++f)l+=n[f];return l>G.MAX_BITS_PER_GRANULE&&a(),m},this.athAdjust=function(e,t,a){var s=90.30873362,n=94.82444863,r=b.FAST_LOG10_X(t,10),i=e*e,_=0;return r-=a,i>1e-20&&(_=1+b.FAST_LOG10_X(i,10/s)),_<0&&(_=0),r*=_,r+=a+s-n,Math.pow(10,.1*r)},this.calc_xmin=function(e,t,s,n){var r,i=0,_=e.internal_flags,l=0,f=0,c=_.ATH,u=s.xr,h=e.VBR==d.vbr_mtrh?1:0,b=_.masking_lower;for(e.VBR!=d.vbr_mtrh&&e.VBR!=d.vbr_mt||(b=1),r=0;r<s.psy_lmax;r++){A=(M=e.VBR==d.vbr_rh||e.VBR==d.vbr_mtrh?athAdjust(c.adjust,c.l[r],c.floor):c.adjust*c.l[r])/(g=s.width[r]),B=o,E=g>>1,y=0;do{y+=x=u[l]*u[l],B+=x<A?x:A,y+=H=u[++l]*u[l],B+=H<A?H:A,l++}while(--E>0);y>M&&f++,r==F.SBPSY_l&&a(),0!=h&&(M=B),e.ATHonly||(k=t.en.l[r])>0&&(T=y*t.thm.l[r]*b/k,0!=h&&(T*=_.nsPsy.longfact[r]),M<T&&(M=T)),n[i++]=0!=h?M:M*_.nsPsy.longfact[r]}var p=575;if(s.block_type!=F.SHORT_TYPE)for(var m=576;0!=m--&&R.EQ(u[m],0);)p=m;s.max_nonzero_coeff=p;for(var v=s.sfb_smin;r<s.psymax;v++,r+=3){var g,w,S;for(S=e.VBR==d.vbr_rh||e.VBR==d.vbr_mtrh?athAdjust(c.adjust,c.s[v],c.floor):c.adjust*c.s[v],g=s.width[r],w=0;w<3;w++){var M,A,B,k,T,y=0,E=g>>1;A=S/g,B=o;do{var x,H;y+=x=u[l]*u[l],B+=x<A?x:A,y+=H=u[++l]*u[l],B+=H<A?H:A,l++}while(--E>0);y>S&&f++,v==F.SBPSY_s&&a(),M=0!=h?B:S,e.ATHonly||e.ATHshort||(k=t.en.s[v][w])>0&&(T=y*t.thm.s[v][w]*b/k,0!=h&&(T*=_.nsPsy.shortfact[v]),M<T&&(M=T)),n[i++]=0!=h?M:M*_.nsPsy.shortfact[v]}e.useTemporal&&(n[i-3]>n[i-3+1]&&(n[i-3+1]+=(n[i-3]-n[i-3+1])*_.decay),n[i-3+1]>n[i-3+2]&&(n[i-3+2]+=(n[i-3+1]-n[i-3+2])*_.decay))}return f},this.calc_noise_core=function(e,t,a,s){var n=0,r=t.s,_=e.l3_enc;if(r>e.count1)for(;0!=a--;)l=e.xr[r],r++,n+=l*l,l=e.xr[r],r++,n+=l*l;else if(r>e.big_values){var o=i(2);for(o[0]=0,o[1]=s;0!=a--;)l=Math.abs(e.xr[r])-o[_[r]],r++,n+=l*l,l=Math.abs(e.xr[r])-o[_[r]],r++,n+=l*l}else for(;0!=a--;){var l;l=Math.abs(e.xr[r])-g[_[r]]*s,r++,n+=l*l,l=Math.abs(e.xr[r])-g[_[r]]*s,r++,n+=l*l}return t.s=r,n},this.calc_noise=function(e,t,a,s,n){var r,i,o=0,l=0,f=0,c=0,u=0,p=-20,m=0,d=e.scalefac,v=0;for(s.over_SSD=0,r=0;r<e.psymax;r++){var g,w=e.global_gain-(d[v++]+(0!=e.preflag?h[r]:0)<<e.scalefac_scale+1)-8*e.subblock_gain[e.window[r]],S=0;if(null!=n&&n.step[r]==w)S=n.noise[r],m+=e.width[r],a[o++]=S/t[l++],S=n.noise_log[r];else{var M,B=_(w);i=e.width[r]>>1,m+e.width[r]>e.max_nonzero_coeff&&(i=(M=e.max_nonzero_coeff-m+1)>0?M>>1:0);var R=new A(m);S=this.calc_noise_core(e,R,i,B),m=R.s,null!=n&&(n.step[r]=w,n.noise[r]=S),S=a[o++]=S/t[l++],S=b.FAST_LOG10(Math.max(S,1e-20)),null!=n&&(n.noise_log[r]=S)}null!=n&&(n.global_gain=e.global_gain),u+=S,S>0&&(g=Math.max(0|10*S+.5,1),s.over_SSD+=g*g,f++,c+=S),p=Math.max(p,S)}return s.over_count=f,s.tot_noise=u,s.over_noise=c,s.max_noise=p,f}}function N(){this.global_gain=0,this.sfb_count1=0,this.step=r(39),this.noise=i(39),this.noise_log=i(39)}function D(){this.xr=i(576),this.l3_enc=r(576),this.scalefac=r(Y.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=r(3),this.subblock_gain=r(4),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=r(Y.SFBMAX),this.window=r(Y.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=r(4),this.max_nonzero_coeff=0;var e=this;function t(e){return new Int32Array(e)}function a(e){return new Float32Array(e)}this.assign=function(s){e.xr=a(s.xr),e.l3_enc=t(s.l3_enc),e.scalefac=t(s.scalefac),e.xrpow_max=s.xrpow_max,e.part2_3_length=s.part2_3_length,e.big_values=s.big_values,e.count1=s.count1,e.global_gain=s.global_gain,e.scalefac_compress=s.scalefac_compress,e.block_type=s.block_type,e.mixed_block_flag=s.mixed_block_flag,e.table_select=t(s.table_select),e.subblock_gain=t(s.subblock_gain),e.region0_count=s.region0_count,e.region1_count=s.region1_count,e.preflag=s.preflag,e.scalefac_scale=s.scalefac_scale,e.count1table_select=s.count1table_select,e.part2_length=s.part2_length,e.sfb_lmax=s.sfb_lmax,e.sfb_smin=s.sfb_smin,e.psy_lmax=s.psy_lmax,e.sfbmax=s.sfbmax,e.psymax=s.psymax,e.sfbdivide=s.sfbdivide,e.width=t(s.width),e.window=t(s.window),e.count1bits=s.count1bits,e.sfb_partition_table=s.sfb_partition_table.slice(0),e.slen=t(s.slen),e.max_nonzero_coeff=s.max_nonzero_coeff}}T.t1HB=[1,1,1,0],T.t2HB=[1,2,1,3,1,1,3,2,0],T.t3HB=[3,2,1,1,1,1,3,2,0],T.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],T.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],T.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],T.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],T.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],T.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],T.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],T.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],T.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],T.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],T.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],T.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],T.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],T.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],T.t1l=[1,4,3,5],T.t2l=[1,4,7,4,5,7,6,7,8],T.t3l=[2,3,7,4,4,7,6,7,8],T.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],T.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],T.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],T.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],T.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],T.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],T.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],T.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],T.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],T.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],T.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],T.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],T.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],T.t32l=[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],T.t33l=[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8],T.ht=[new k(0,0,null,null),new k(2,0,T.t1HB,T.t1l),new k(3,0,T.t2HB,T.t2l),new k(3,0,T.t3HB,T.t3l),new k(0,0,null,null),new k(4,0,T.t5HB,T.t5l),new k(4,0,T.t6HB,T.t6l),new k(6,0,T.t7HB,T.t7l),new k(6,0,T.t8HB,T.t8l),new k(6,0,T.t9HB,T.t9l),new k(8,0,T.t10HB,T.t10l),new k(8,0,T.t11HB,T.t11l),new k(8,0,T.t12HB,T.t12l),new k(16,0,T.t13HB,T.t13l),new k(0,0,null,T.t16_5l),new k(16,0,T.t15HB,T.t15l),new k(1,1,T.t16HB,T.t16l),new k(2,3,T.t16HB,T.t16l),new k(3,7,T.t16HB,T.t16l),new k(4,15,T.t16HB,T.t16l),new k(6,63,T.t16HB,T.t16l),new k(8,255,T.t16HB,T.t16l),new k(10,1023,T.t16HB,T.t16l),new k(13,8191,T.t16HB,T.t16l),new k(4,15,T.t24HB,T.t24l),new k(5,31,T.t24HB,T.t24l),new k(6,63,T.t24HB,T.t24l),new k(7,127,T.t24HB,T.t24l),new k(8,255,T.t24HB,T.t24l),new k(9,511,T.t24HB,T.t24l),new k(11,2047,T.t24HB,T.t24l),new k(13,8191,T.t24HB,T.t24l),new k(0,0,T.t32HB,T.t32l),new k(0,0,T.t33HB,T.t33l)],T.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366],T.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296],T.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369],T.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]],T.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]],T.scfsi_band=[0,6,11,16,21],V.Q_MAX=257,V.Q_MAX2=116,V.LARGE_BITS=1e5,V.IXMAX_VAL=8206;var Y={};function C(){var e,t;this.rv=null,this.qupvt=null;var s,n=new x;function r(e,t,a,s){s=0;for(var n=0;n<=a;++n){var r=Math.abs(e.xr[n]);s+=r,t[n]=Math.sqrt(r*Math.sqrt(r)),t[n]>e.xrpow_max&&(e.xrpow_max=t[n])}return s}function _(e,a){var s=e.ATH,n=a.xr;if(a.block_type!=F.SHORT_TYPE)for(var r=!1,i=F.PSFB21-1;i>=0&&!r;i--){var _=e.scalefac_band.psfb21[i],o=e.scalefac_band.psfb21[i+1],l=t.athAdjust(s.adjust,s.psfb21[i],s.floor);e.nsPsy.longfact[21]>1e-12&&(l*=e.nsPsy.longfact[21]);for(var f=o-1;f>=_;f--){if(!(Math.abs(n[f])<l)){r=!0;break}n[f]=0}}else for(var c=0;c<3;c++)for(r=!1,i=F.PSFB12-1;i>=0&&!r;i--){o=(_=3*e.scalefac_band.s[12]+(e.scalefac_band.s[13]-e.scalefac_band.s[12])*c+(e.scalefac_band.psfb12[i]-e.scalefac_band.psfb12[0]))+(e.scalefac_band.psfb12[i+1]-e.scalefac_band.psfb12[i]);var u=t.athAdjust(s.adjust,s.psfb12[i],s.floor);for(e.nsPsy.shortfact[12]>1e-12&&(u*=e.nsPsy.shortfact[12]),f=o-1;f>=_;f--){if(!(Math.abs(n[f])<u)){r=!0;break}n[f]=0}}}function o(e){this.ordinal=e}function l(e,t,n,r,i){var _,l=e.CurrentStep[r],f=!1,c=e.OldValue[r],u=o.BINSEARCH_NONE;for(t.global_gain=c,n-=t.part2_length;;){var h;if(_=s.count_bits(e,i,t,null),1==l||_==n)break;_>n?(u==o.BINSEARCH_DOWN&&(f=!0),f&&(l/=2),u=o.BINSEARCH_UP,h=l):(u==o.BINSEARCH_UP&&(f=!0),f&&(l/=2),u=o.BINSEARCH_DOWN,h=-l),t.global_gain+=h,t.global_gain<0&&a(),t.global_gain>255&&a()}for(;_>n&&t.global_gain<255;)t.global_gain++,_=s.count_bits(e,i,t,null);return e.CurrentStep[r]=c-t.global_gain>=4?4:2,e.OldValue[r]=t.global_gain,t.part2_3_length=_,_}function f(e){for(var t=0;t<e.sfbmax;t++)if(e.scalefac[t]+e.subblock_gain[e.window[t]]==0)return!1;return!0}function c(e,t,s,n,r){var i;switch(e){default:case 9:t.over_count>0?(i=s.over_SSD<=t.over_SSD,s.over_SSD==t.over_SSD&&(i=s.bits<t.bits)):i=s.max_noise<0&&10*s.max_noise+s.bits<=10*t.max_noise+t.bits;break;case 0:i=s.over_count<t.over_count||s.over_count==t.over_count&&s.over_noise<t.over_noise||s.over_count==t.over_count&&R.EQ(s.over_noise,t.over_noise)&&s.tot_noise<t.tot_noise;break;case 8:a();case 1:i=s.max_noise<t.max_noise;break;case 2:i=s.tot_noise<t.tot_noise;break;case 3:i=s.tot_noise<t.tot_noise&&s.max_noise<t.max_noise;break;case 4:i=s.max_noise<=0&&t.max_noise>.2||s.max_noise<=0&&t.max_noise<0&&t.max_noise>s.max_noise-.2&&s.tot_noise<t.tot_noise||s.max_noise<=0&&t.max_noise>0&&t.max_noise>s.max_noise-.2&&s.tot_noise<t.tot_noise+t.over_noise||s.max_noise>0&&t.max_noise>-.05&&t.max_noise>s.max_noise-.1&&s.tot_noise+s.over_noise<t.tot_noise+t.over_noise||s.max_noise>0&&t.max_noise>-.1&&t.max_noise>s.max_noise-.15&&s.tot_noise+s.over_noise+s.over_noise<t.tot_noise+t.over_noise+t.over_noise;break;case 5:i=s.over_noise<t.over_noise||R.EQ(s.over_noise,t.over_noise)&&s.tot_noise<t.tot_noise;break;case 6:i=s.over_noise<t.over_noise||R.EQ(s.over_noise,t.over_noise)&&(s.max_noise<t.max_noise||R.EQ(s.max_noise,t.max_noise)&&s.tot_noise<=t.tot_noise);break;case 7:i=s.over_count<t.over_count||s.over_noise<t.over_noise}return 0==t.over_count&&(i=i&&s.bits<t.bits),i}function b(e,t,s,n,r){var i,_=e.internal_flags;i=0==t.scalefac_scale?1.2968395546510096:1.6817928305074292;for(var o=0,l=0;l<t.sfbmax;l++)o<s[l]&&(o=s[l]);var f=_.noise_shaping_amp;switch(3==f&&a(),f){case 2:break;case 1:o>1?o=Math.pow(o,.5):o*=.95;break;default:o>1?o=1:o*=.95}var c=0;for(l=0;l<t.sfbmax;l++){var u,h=t.width[l];if(c+=h,!(s[l]<o)){for(2&_.substep_shaping&&a(),t.scalefac[l]++,u=-h;u<0;u++)n[c+u]*=i,n[c+u]>t.xrpow_max&&(t.xrpow_max=n[c+u]);if(2==_.noise_shaping_amp)return}}}function p(e,a){for(var s=1.2968395546510096,n=0,r=0;r<e.sfbmax;r++){var i=e.width[r],_=e.scalefac[r];if(0!=e.preflag&&(_+=t.pretab[r]),n+=i,1&_){_++;for(var o=-i;o<0;o++)a[n+o]*=s,a[n+o]>e.xrpow_max&&(e.xrpow_max=a[n+o])}e.scalefac[r]=_>>1}e.preflag=0,e.scalefac_scale=1}function m(e,a,s){var n,r=a.scalefac;for(n=0;n<a.sfb_lmax;n++)if(r[n]>=16)return!0;for(var i=0;i<3;i++){var _=0,o=0;for(n=a.sfb_lmax+i;n<a.sfbdivide;n+=3)_<r[n]&&(_=r[n]);for(;n<a.sfbmax;n+=3)o<r[n]&&(o=r[n]);if(!(_<16&&o<8)){if(a.subblock_gain[i]>=7)return!0;a.subblock_gain[i]++;var l=e.scalefac_band.l[a.sfb_lmax];for(n=a.sfb_lmax+i;n<a.sfbmax;n+=3){var f=a.width[n],c=r[n];if((c-=4>>a.scalefac_scale)>=0)r[n]=c,l+=3*f;else{r[n]=0;var u=210+(c<<a.scalefac_scale+1);b=t.IPOW20(u),l+=f*(i+1);for(var h=-f;h<0;h++)s[l+h]*=b,s[l+h]>a.xrpow_max&&(a.xrpow_max=s[l+h]);l+=f*(3-i-1)}}var b=t.IPOW20(202);for(l+=a.width[n]*(i+1),h=-a.width[n];h<0;h++)s[l+h]*=b,s[l+h]>a.xrpow_max&&(a.xrpow_max=s[l+h])}}return!1}function v(e,t,a,n,r){var i=e.internal_flags;b(e,t,a,n,r);var _=f(t);return!(_||(_=2==i.mode_gr?s.scale_bitcount(t):s.scale_bitcount_lsf(i,t))&&(i.noise_shaping>1&&(u.fill(i.pseudohalf,0),0==t.scalefac_scale?(p(t,n),_=!1):t.block_type==F.SHORT_TYPE&&i.subblock_gain>0&&(_=m(i,t,n)||f(t))),_||(_=2==i.mode_gr?s.scale_bitcount(t):s.scale_bitcount_lsf(i,t)),_))}this.setModules=function(a,r,i,_){e=r,this.rv=r,t=i,this.qupvt=i,s=_,n.setModules(t,s)},this.init_xrpow=function(e,t,a){var s=0,n=0|t.max_nonzero_coeff;if(t.xrpow_max=0,u.fill(a,n,576,0),(s=r(t,a,n,s))>1e-20){var i=0;2&e.substep_shaping&&(i=1);for(var _=0;_<t.psymax;_++)e.pseudohalf[_]=i;return!0}return u.fill(t.l3_enc,0,576,0),!1},this.init_outer_loop=function(e,s){s.part2_3_length=0,s.big_values=0,s.count1=0,s.global_gain=210,s.scalefac_compress=0,s.table_select[0]=0,s.table_select[1]=0,s.table_select[2]=0,s.subblock_gain[0]=0,s.subblock_gain[1]=0,s.subblock_gain[2]=0,s.subblock_gain[3]=0,s.region0_count=0,s.region1_count=0,s.preflag=0,s.scalefac_scale=0,s.count1table_select=0,s.part2_length=0,s.sfb_lmax=F.SBPSY_l,s.sfb_smin=F.SBPSY_s,s.psy_lmax=e.sfb21_extra?F.SBMAX_l:F.SBPSY_l,s.psymax=s.psy_lmax,s.sfbmax=s.sfb_lmax,s.sfbdivide=11;for(var n=0;n<F.SBMAX_l;n++)s.width[n]=e.scalefac_band.l[n+1]-e.scalefac_band.l[n],s.window[n]=3;if(s.block_type==F.SHORT_TYPE){var r=i(576);s.sfb_smin=0,s.sfb_lmax=0,0!=s.mixed_block_flag&&a(),s.psymax=s.sfb_lmax+3*((e.sfb21_extra?F.SBMAX_s:F.SBPSY_s)-s.sfb_smin),s.sfbmax=s.sfb_lmax+3*(F.SBPSY_s-s.sfb_smin),s.sfbdivide=s.sfbmax-18,s.psy_lmax=s.sfb_lmax;var o=e.scalefac_band.l[s.sfb_lmax];for(h.arraycopy(s.xr,0,r,0,576),n=s.sfb_smin;n<F.SBMAX_s;n++)for(var l=e.scalefac_band.s[n],f=e.scalefac_band.s[n+1],c=0;c<3;c++)for(var b=l;b<f;b++)s.xr[o++]=r[3*b+c];var p=s.sfb_lmax;for(n=s.sfb_smin;n<F.SBMAX_s;n++)s.width[p]=s.width[p+1]=s.width[p+2]=e.scalefac_band.s[n+1]-e.scalefac_band.s[n],s.window[p]=0,s.window[p+1]=1,s.window[p+2]=2,p+=3}s.count1bits=0,s.sfb_partition_table=t.nr_of_sfb_block[0][0],s.slen[0]=0,s.slen[1]=0,s.slen[2]=0,s.slen[3]=0,s.max_nonzero_coeff=575,u.fill(s.scalefac,0),_(e,s)},o.BINSEARCH_NONE=new o(0),o.BINSEARCH_UP=new o(1),o.BINSEARCH_DOWN=new o(2),this.outer_loop=function(e,n,r,_,o,f){var u=e.internal_flags,b=new D,p=i(576),m=i(Y.SFBMAX),g=new E,w=new N,S=9999999,M=!1,A=!1,B=0;if(l(u,n,f,o,_),0==u.noise_shaping)return 100;t.calc_noise(n,r,m,g,w),g.bits=n.part2_3_length,b.assign(n);var R=0;for(h.arraycopy(_,0,p,0,576);!M;){do{var k,T=new E,y=255;if(k=2&u.substep_shaping?20:3,u.sfb21_extra&&a(),!v(e,b,m,_,A))break;0!=b.scalefac_scale&&(y=254);var x=f-b.part2_length;if(x<=0)break;for(;(b.part2_3_length=s.count_bits(u,_,b,w))>x&&b.global_gain<=y;)b.global_gain++;if(b.global_gain>y)break;if(0==g.over_count){for(;(b.part2_3_length=s.count_bits(u,_,b,w))>S&&b.global_gain<=y;)b.global_gain++;if(b.global_gain>y)break}if(t.calc_noise(b,r,m,T,w),T.bits=b.part2_3_length,0!=(c(n.block_type!=F.SHORT_TYPE?e.quant_comp:e.quant_comp_short,g,T,b,m)?1:0))S=n.part2_3_length,g=T,n.assign(b),R=0,h.arraycopy(_,0,p,0,576);else if(0==u.full_outer_loop){if(++R>k&&0==g.over_count)break;if(3==u.noise_shaping_amp&&A&&R>30)break;if(3==u.noise_shaping_amp&&A&&b.global_gain-B>15)break}}while(b.global_gain+b.scalefac_scale<255);3==u.noise_shaping_amp?a():M=!0}return e.VBR==d.vbr_rh||e.VBR==d.vbr_mtrh?h.arraycopy(p,0,_,0,576):1&u.substep_shaping&&a(),g.over_count},this.iteration_finish_one=function(t,a,n){var r=t.l3_side,i=r.tt[a][n];s.best_scalefac_store(t,a,n,r),1==t.use_best_huffman&&s.best_huffman_divide(t,i),e.ResvAdjust(t,i)}}function X(){var e=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*b.SQRT2*.5/2384e-9,.017876148*b.SQRT2*.5/2384e-9,.003134727*b.SQRT2*.5/2384e-9,.002457142*b.SQRT2*.5/2384e-9,971317e-9*b.SQRT2*.5/2384e-9,218868e-9*b.SQRT2*.5/2384e-9,101566e-9*b.SQRT2*.5/2384e-9,13828e-9*b.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,-29.20218120805369],t=12,s=36,n=[[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,940084909404969e-27,6423305872147839e-28,2382191739347918e-28,5456116108943412e-27,4878985199565852e-27,4240448995017367e-27,3559909094758252e-27,2858043359288075e-27,2156177623817898e-27,1475637723558783e-27,8371015190102974e-28,2599706096327376e-28,-5456116108943412e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758252e-27,-2858043359288076e-27,-2156177623817898e-27,-1475637723558783e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347923e-28,-6423305872147843e-28,-9400849094049696e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049694e-28,-642330587214784e-27,-2382191739347918e-28],[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,9400849094049688e-28,6423305872147841e-28,2382191739347918e-28,5456116108943413e-27,4878985199565852e-27,4240448995017367e-27,3559909094758253e-27,2858043359288075e-27,2156177623817898e-27,1475637723558782e-27,8371015190102975e-28,2599706096327376e-28,-5461314069809755e-27,-4921085770524055e-27,-4343405037091838e-27,-3732668368707687e-27,-3093523840190885e-27,-2430835727329465e-27,-1734679010007751e-27,-974825365660928e-27,-2797435120168326e-28,0,0,0,0,0,0,-2283748241799531e-28,-4037858874020686e-28,-2146547464825323e-28],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2283748241799531e-28,4037858874020686e-28,2146547464825323e-28,5461314069809755e-27,4921085770524055e-27,4343405037091838e-27,3732668368707687e-27,3093523840190885e-27,2430835727329466e-27,1734679010007751e-27,974825365660928e-27,2797435120168326e-28,-5456116108943413e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758253e-27,-2858043359288075e-27,-2156177623817898e-27,-1475637723558782e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347913e-28,-6423305872147834e-28,-9400849094049688e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049688e-28,-6423305872147841e-28,-2382191739347918e-28]],r=n[F.SHORT_TYPE],_=n[F.SHORT_TYPE],o=n[F.SHORT_TYPE],l=n[F.SHORT_TYPE],f=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function c(t,a,s){for(var n,r,i,_=10,o=a+238-14-286,l=-15;l<0;l++){var f,c,u;f=e[_+-10],c=t[o+-224]*f,u=t[a+224]*f,f=e[_+-9],c+=t[o+-160]*f,u+=t[a+160]*f,f=e[_+-8],c+=t[o+-96]*f,u+=t[a+96]*f,f=e[_+-7],c+=t[o+-32]*f,u+=t[a+32]*f,f=e[_+-6],c+=t[o+32]*f,u+=t[a+-32]*f,f=e[_+-5],c+=t[o+96]*f,u+=t[a+-96]*f,f=e[_+-4],c+=t[o+160]*f,u+=t[a+-160]*f,f=e[_+-3],c+=t[o+224]*f,u+=t[a+-224]*f,f=e[_+-2],c+=t[a+-256]*f,u-=t[o+256]*f,f=e[_+-1],c+=t[a+-192]*f,u-=t[o+192]*f,f=e[_+0],c+=t[a+-128]*f,u-=t[o+128]*f,f=e[_+1],c+=t[a+-64]*f,u-=t[o+64]*f,f=e[_+2],c+=t[a+0]*f,u-=t[o+0]*f,f=e[_+3],c+=t[a+64]*f,u-=t[o+-64]*f,f=e[_+4],c+=t[a+128]*f,u-=t[o+-128]*f,f=e[_+5],c+=t[a+192]*f,f=(u-=t[o+-192]*f)-(c*=e[_+6]),s[30+2*l]=u+c,s[31+2*l]=e[_+7]*f,_+=18,a--,o++}u=t[a+-16]*e[_+-10],c=t[a+-32]*e[_+-2],u+=(t[a+-48]-t[a+16])*e[_+-9],c+=t[a+-96]*e[_+-1],u+=(t[a+-80]+t[a+48])*e[_+-8],c+=t[a+-160]*e[_+0],u+=(t[a+-112]-t[a+80])*e[_+-7],c+=t[a+-224]*e[_+1],u+=(t[a+-144]+t[a+112])*e[_+-6],c-=t[a+32]*e[_+2],u+=(t[a+-176]-t[a+144])*e[_+-5],c-=t[a+96]*e[_+3],u+=(t[a+-208]+t[a+176])*e[_+-4],c-=t[a+160]*e[_+4],u+=(t[a+-240]-t[a+208])*e[_+-3],n=(c-=t[a+224])-u,r=c+u,u=s[14],c=s[15]-u,s[31]=r+u,s[30]=n+c,s[15]=n-c,s[14]=r-u,i=s[28]-s[0],s[0]+=s[28],s[28]=i*e[_+-36+7],i=s[29]-s[1],s[1]+=s[29],s[29]=i*e[_+-36+7],i=s[26]-s[2],s[2]+=s[26],s[26]=i*e[_+-72+7],i=s[27]-s[3],s[3]+=s[27],s[27]=i*e[_+-72+7],i=s[24]-s[4],s[4]+=s[24],s[24]=i*e[_+-108+7],i=s[25]-s[5],s[5]+=s[25],s[25]=i*e[_+-108+7],i=s[22]-s[6],s[6]+=s[22],s[22]=i*b.SQRT2,i=s[23]-s[7],s[7]+=s[23],s[23]=i*b.SQRT2-s[7],s[7]-=s[6],s[22]-=s[7],s[23]-=s[22],i=s[6],s[6]=s[31]-i,s[31]=s[31]+i,i=s[7],s[7]=s[30]-i,s[30]=s[30]+i,i=s[22],s[22]=s[15]-i,s[15]=s[15]+i,i=s[23],s[23]=s[14]-i,s[14]=s[14]+i,i=s[20]-s[8],s[8]+=s[20],s[20]=i*e[_+-180+7],i=s[21]-s[9],s[9]+=s[21],s[21]=i*e[_+-180+7],i=s[18]-s[10],s[10]+=s[18],s[18]=i*e[_+-216+7],i=s[19]-s[11],s[11]+=s[19],s[19]=i*e[_+-216+7],i=s[16]-s[12],s[12]+=s[16],s[16]=i*e[_+-252+7],i=s[17]-s[13],s[13]+=s[17],s[17]=i*e[_+-252+7],i=-s[20]+s[24],s[20]+=s[24],s[24]=i*e[_+-216+7],i=-s[21]+s[25],s[21]+=s[25],s[25]=i*e[_+-216+7],i=s[4]-s[8],s[4]+=s[8],s[8]=i*e[_+-216+7],i=s[5]-s[9],s[5]+=s[9],s[9]=i*e[_+-216+7],i=s[0]-s[12],s[0]+=s[12],s[12]=i*e[_+-72+7],i=s[1]-s[13],s[1]+=s[13],s[13]=i*e[_+-72+7],i=s[16]-s[28],s[16]+=s[28],s[28]=i*e[_+-72+7],i=-s[17]+s[29],s[17]+=s[29],s[29]=i*e[_+-72+7],i=b.SQRT2*(s[2]-s[10]),s[2]+=s[10],s[10]=i,i=b.SQRT2*(s[3]-s[11]),s[3]+=s[11],s[11]=i,i=b.SQRT2*(-s[18]+s[26]),s[18]+=s[26],s[26]=i-s[18],i=b.SQRT2*(-s[19]+s[27]),s[19]+=s[27],s[27]=i-s[19],i=s[2],s[19]-=s[3],s[3]-=i,s[2]=s[31]-i,s[31]+=i,i=s[3],s[11]-=s[19],s[18]-=i,s[3]=s[30]-i,s[30]+=i,i=s[18],s[27]-=s[11],s[19]-=i,s[18]=s[15]-i,s[15]+=i,i=s[19],s[10]-=i,s[19]=s[14]-i,s[14]+=i,i=s[10],s[11]-=i,s[10]=s[23]-i,s[23]+=i,i=s[11],s[26]-=i,s[11]=s[22]-i,s[22]+=i,i=s[26],s[27]-=i,s[26]=s[7]-i,s[7]+=i,i=s[27],s[27]=s[6]-i,s[6]+=i,i=b.SQRT2*(s[0]-s[4]),s[0]+=s[4],s[4]=i,i=b.SQRT2*(s[1]-s[5]),s[1]+=s[5],s[5]=i,i=b.SQRT2*(s[16]-s[20]),s[16]+=s[20],s[20]=i,i=b.SQRT2*(s[17]-s[21]),s[17]+=s[21],s[21]=i,i=-b.SQRT2*(s[8]-s[12]),s[8]+=s[12],s[12]=i-s[8],i=-b.SQRT2*(s[9]-s[13]),s[9]+=s[13],s[13]=i-s[9],i=-b.SQRT2*(s[25]-s[29]),s[25]+=s[29],s[29]=i-s[25],i=-b.SQRT2*(s[24]+s[28]),s[24]-=s[28],s[28]=i-s[24],i=s[24]-s[16],s[24]=i,i=s[20]-i,s[20]=i,i=s[28]-i,s[28]=i,i=s[25]-s[17],s[25]=i,i=s[21]-i,s[21]=i,i=s[29]-i,s[29]=i,i=s[17]-s[1],s[17]=i,i=s[9]-i,s[9]=i,i=s[25]-i,s[25]=i,i=s[5]-i,s[5]=i,i=s[21]-i,s[21]=i,i=s[13]-i,s[13]=i,i=s[29]-i,s[29]=i,i=s[1]-s[0],s[1]=i,i=s[16]-i,s[16]=i,i=s[17]-i,s[17]=i,i=s[8]-i,s[8]=i,i=s[9]-i,s[9]=i,i=s[24]-i,s[24]=i,i=s[25]-i,s[25]=i,i=s[4]-i,s[4]=i,i=s[5]-i,s[5]=i,i=s[20]-i,s[20]=i,i=s[21]-i,s[21]=i,i=s[12]-i,s[12]=i,i=s[13]-i,s[13]=i,i=s[28]-i,s[28]=i,i=s[29]-i,s[29]=i,i=s[0],s[0]+=s[31],s[31]-=i,i=s[1],s[1]+=s[30],s[30]-=i,i=s[16],s[16]+=s[15],s[15]-=i,i=s[17],s[17]+=s[14],s[14]-=i,i=s[8],s[8]+=s[23],s[23]-=i,i=s[9],s[9]+=s[22],s[22]-=i,i=s[24],s[24]+=s[7],s[7]-=i,i=s[25],s[25]+=s[6],s[6]-=i,i=s[4],s[4]+=s[27],s[27]-=i,i=s[5],s[5]+=s[26],s[26]-=i,i=s[20],s[20]+=s[11],s[11]-=i,i=s[21],s[21]+=s[10],s[10]-=i,i=s[12],s[12]+=s[19],s[19]-=i,i=s[13],s[13]+=s[18],s[18]-=i,i=s[28],s[28]+=s[3],s[3]-=i,i=s[29],s[29]+=s[2],s[2]-=i}function p(e,t){for(var a=0;a<3;a++){var s,r,i,_,o,l;r=(_=e[t+6]*n[F.SHORT_TYPE][0]-e[t+15])+(s=e[t+0]*n[F.SHORT_TYPE][2]-e[t+9]),i=_-s,o=(_=e[t+15]*n[F.SHORT_TYPE][0]+e[t+6])+(s=e[t+9]*n[F.SHORT_TYPE][2]+e[t+0]),l=-_+s,s=2069978111953089e-26*(e[t+3]*n[F.SHORT_TYPE][1]-e[t+12]),_=2069978111953089e-26*(e[t+12]*n[F.SHORT_TYPE][1]+e[t+3]),e[t+0]=190752519173728e-25*r+s,e[t+15]=190752519173728e-25*-o+_,i=.8660254037844387*i*1907525191737281e-26,o=.5*o*1907525191737281e-26+_,e[t+3]=i-o,e[t+6]=i+o,r=.5*r*1907525191737281e-26-s,l=.8660254037844387*l*1907525191737281e-26,e[t+9]=r+l,e[t+12]=r-l,t++}}function m(e,t,a){var s,n,r,i,o,l,f,c,u,h,b,p,m,d,v,g,w,S;r=a[17]-a[9],o=a[15]-a[11],l=a[14]-a[12],f=a[0]+a[8],c=a[1]+a[7],u=a[2]+a[6],h=a[3]+a[5],e[t+17]=f+u-h-(c-a[4]),n=(f+u-h)*_[19]+(c-a[4]),s=(r-o-l)*_[18],e[t+5]=s+n,e[t+6]=s-n,i=(a[16]-a[10])*_[18],c=c*_[19]+a[4],s=r*_[12]+i+o*_[13]+l*_[14],n=-f*_[16]+c-u*_[17]+h*_[15],e[t+1]=s+n,e[t+2]=s-n,s=r*_[13]-i-o*_[14]+l*_[12],n=-f*_[17]+c-u*_[15]+h*_[16],e[t+9]=s+n,e[t+10]=s-n,s=r*_[14]-i+o*_[12]-l*_[13],n=f*_[15]-c+u*_[16]-h*_[17],e[t+13]=s+n,e[t+14]=s-n,b=a[8]-a[0],m=a[6]-a[2],d=a[5]-a[3],v=a[17]+a[9],g=a[16]+a[10],w=a[15]+a[11],S=a[14]+a[12],e[t+0]=v+w+S+(g+a[13]),s=(v+w+S)*_[19]-(g+a[13]),n=(b-m+d)*_[18],e[t+11]=s+n,e[t+12]=s-n,p=(a[7]-a[1])*_[18],g=a[13]-g*_[19],s=v*_[15]-g+w*_[16]+S*_[17],n=b*_[14]+p+m*_[12]+d*_[13],e[t+3]=s+n,e[t+4]=s-n,s=-v*_[17]+g-w*_[15]-S*_[16],n=b*_[13]+p-m*_[14]-d*_[12],e[t+7]=s+n,e[t+8]=s-n,s=-v*_[16]+g-w*_[17]-S*_[15],n=b*_[12]-p+m*_[13]-d*_[14],e[t+15]=s+n,e[t+16]=s-n}this.mdct_sub48=function(e,_,b){for(var d=_,v=286,g=0;g<e.channels_out;g++){for(var w=0;w<e.mode_gr;w++){for(var S,M=e.l3_side.tt[w][g],A=M.xr,B=0,R=e.sb_sample[g][1-w],k=0,T=0;T<9;T++)for(c(d,v,R[k]),c(d,v+32,R[k+1]),k+=2,v+=64,S=1;S<32;S+=2)R[k-1][S]*=-1;for(S=0;S<32;S++,B+=18){var y=M.block_type,E=e.sb_sample[g][w],x=e.sb_sample[g][1-w];if(0!=M.mixed_block_flag&&S<2&&(y=0),e.amp_filter[S]<1e-12)u.fill(A,B+0,B+18,0);else if(e.amp_filter[S]<1&&a(),y==F.SHORT_TYPE){for(T=-t/4;T<0;T++){var H=n[F.SHORT_TYPE][T+3];A[B+3*T+9]=E[9+T][f[S]]*H-E[8-T][f[S]],A[B+3*T+18]=E[14-T][f[S]]*H+E[15+T][f[S]],A[B+3*T+10]=E[15+T][f[S]]*H-E[14-T][f[S]],A[B+3*T+19]=x[2-T][f[S]]*H+x[3+T][f[S]],A[B+3*T+11]=x[3+T][f[S]]*H-x[2-T][f[S]],A[B+3*T+20]=x[8-T][f[S]]*H+x[9+T][f[S]]}p(A,B)}else{var P=i(18);for(T=-s/4;T<0;T++){var I,L;I=n[y][T+27]*x[T+9][f[S]]+n[y][T+36]*x[8-T][f[S]],L=n[y][T+9]*E[T+9][f[S]]-n[y][T+18]*E[8-T][f[S]],P[T+9]=I-L*r[3+T+9],P[T+18]=I*r[3+T+9]+L}m(A,B,P)}if(y!=F.SHORT_TYPE&&0!=S)for(T=7;T>=0;--T){var O,V;O=A[B+T]*o[20+T]+A[B+-1-T]*l[28+T],V=A[B+T]*l[28+T]-A[B+-1-T]*o[20+T],A[B+-1-T]=O,A[B+T]=V}}}if(d=b,v=286,1==e.mode_gr)for(var N=0;N<18;N++)h.arraycopy(e.sb_sample[g][1][N],0,e.sb_sample[g][0][N],0,32)}}}function j(){this.thm=new Z,this.en=new Z}function F(){F.FFTOFFSET;var e=F.MPG_MD_MS_LR,t=null;this.psy=null;var s=null,n=null;this.setModules=function(e,a,r,i){t=e,this.psy=a,s=a,n=i};var _=new X;function l(e){var t,s;if(0!=e.ATH.useAdjust)if(s=e.loudness_sq[0][0],t=e.loudness_sq[1][0],2==e.channels_out?a():(s+=s,t+=t),2==e.mode_gr&&(s=Math.max(s,t)),s*=.5,(s*=e.ATH.aaSensitivityP)>.03125)e.ATH.adjust>=1?e.ATH.adjust=1:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=1;else{var n=31.98*s+625e-6;e.ATH.adjust>=n?(e.ATH.adjust*=.075*n+.925,e.ATH.adjust<n&&(e.ATH.adjust=n)):e.ATH.adjustLimit>=n?e.ATH.adjust=n:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=n}else e.ATH.adjust=1}function f(e){var t,s;for(e.bitrate_stereoMode_Hist[e.bitrate_index][4]++,e.bitrate_stereoMode_Hist[15][4]++,2==e.channels_out&&a(),t=0;t<e.mode_gr;++t)for(s=0;s<e.channels_out;++s){var n=0|e.l3_side.tt[t][s].block_type;0!=e.l3_side.tt[t][s].mixed_block_flag&&(n=4),e.bitrate_blockType_Hist[e.bitrate_index][n]++,e.bitrate_blockType_Hist[e.bitrate_index][5]++,e.bitrate_blockType_Hist[15][n]++,e.bitrate_blockType_Hist[15][5]++}}function u(e,t){var a,s,n=e.internal_flags;if(0==n.lame_encode_frame_init){var r,o,l=i(2014),f=i(2014);for(n.lame_encode_frame_init=1,r=0,o=0;r<286+576*(1+n.mode_gr);++r)r<576*n.mode_gr?(l[r]=0,2==n.channels_out&&(f[r]=0)):(l[r]=t[0][o],2==n.channels_out&&(f[r]=t[1][o]),++o);for(s=0;s<n.mode_gr;s++)for(a=0;a<n.channels_out;a++)n.l3_side.tt[s][a].block_type=F.SHORT_TYPE;_.mdct_sub48(n,l,f)}}this.lame_encode_mp3_frame=function(i,h,b,p,m,g){var w,S=c([2,2]);S[0][0]=new j,S[0][1]=new j,S[1][0]=new j,S[1][1]=new j;var M,A=c([2,2]);A[0][0]=new j,A[0][1]=new j,A[1][0]=new j,A[1][1]=new j;var B,R,k,T=[null,null],y=i.internal_flags,E=o([2,4]),x=[.5,.5],H=[[0,0],[0,0]],P=[[0,0],[0,0]];if(T[0]=h,T[1]=b,0==y.lame_encode_frame_init&&u(i,T),y.padding=0,(y.slot_lag-=y.frac_SpF)<0&&(y.slot_lag+=i.out_samplerate,y.padding=1),0!=y.psymodel){var I,L=[null,null],O=0,V=r(2);for(k=0;k<y.mode_gr;k++){for(R=0;R<y.channels_out;R++)L[R]=T[R],O=576+576*k-F.FFTOFFSET;if(i.VBR==d.vbr_mtrh||i.VBR==d.vbr_mt?a():I=s.L3psycho_anal_ns(i,L,O,k,S,A,H[k],P[k],E[k],V),0!=I)return-4;for(i.mode==v.JOINT_STEREO&&a(),R=0;R<y.channels_out;R++){var N=y.l3_side.tt[k][R];N.block_type=V[R],N.mixed_block_flag=0}}}else a();if(l(y),_.mdct_sub48(y,T[0],T[1]),y.mode_ext=F.MPG_MD_LR_LR,i.force_ms?y.mode_ext=F.MPG_MD_MS_LR:i.mode==v.JOINT_STEREO&&a(),y.mode_ext==e?(M=A,B=P):(M=S,B=H),i.analysis&&null!=y.pinfo&&a(),i.VBR==d.vbr_off||i.VBR==d.vbr_abr){var D,Y;for(D=0;D<18;D++)y.nsPsy.pefirbuf[D]=y.nsPsy.pefirbuf[D+1];for(Y=0,k=0;k<y.mode_gr;k++)for(R=0;R<y.channels_out;R++)Y+=B[k][R];for(y.nsPsy.pefirbuf[18]=Y,Y=y.nsPsy.pefirbuf[9],D=0;D<9;D++)Y+=(y.nsPsy.pefirbuf[D]+y.nsPsy.pefirbuf[18-D])*F.fircoef[D];for(Y=3350*y.mode_gr*y.channels_out/Y,k=0;k<y.mode_gr;k++)for(R=0;R<y.channels_out;R++)B[k][R]*=Y}return y.iteration_loop.iteration_loop(i,B,x,M),t.format_bitstream(i),w=t.copy_buffer(y,p,m,g,1),i.bWriteVbrTag&&n.addVbrFrame(i),i.analysis&&null!=y.pinfo&&a(),f(y),w}}function q(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0}function z(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[r(4),r(4)];for(var e=0;e<2;e++)for(var t=0;t<2;t++)this.tt[e][t]=new D}function Z(){this.l=i(F.SBMAX_l),this.s=o([F.SBMAX_s,3]);var e=this;this.assign=function(t){h.arraycopy(t.l,0,e.l,0,F.SBMAX_l);for(var a=0;a<F.SBMAX_s;a++)for(var s=0;s<3;s++)e.s[a][s]=t.s[a][s]}}function K(){this.last_en_subshort=o([4,9]),this.lastAttacks=r(4),this.pefirbuf=i(19),this.longfact=i(F.SBMAX_l),this.shortfact=i(F.SBMAX_s),this.attackthre=0,this.attackthre_s=0}function G(){var e=40;function t(){this.write_timing=0,this.ptr=0,this.buf=s(e)}this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=o([2,G.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new z,this.ms_ratio=i(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=r(2),this.CurrentStep=r(2),this.masking_lower=0,this.bv_scf=r(576),this.pseudohalf=r(Y.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*G.BPC+1),this.itime=_(2),this.sideinfo_len=0,this.sb_sample=o([2,2,18,F.SBLIMIT]),this.amp_filter=i(32),this.header=new Array(G.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new O,this.minval_l=i(F.CBANDS),this.minval_s=i(F.CBANDS),this.nb_1=o([4,F.CBANDS]),this.nb_2=o([4,F.CBANDS]),this.nb_s1=o([4,F.CBANDS]),this.nb_s2=o([4,F.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=i(4),this.loudness_sq=o([2,2]),this.loudness_sq_save=i(2),this.mld_l=i(F.SBMAX_l),this.mld_s=i(F.SBMAX_s),this.bm_l=r(F.SBMAX_l),this.bo_l=r(F.SBMAX_l),this.bm_s=r(F.SBMAX_s),this.bo_s=r(F.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=l([F.CBANDS,2]),this.s3ind_s=l([F.CBANDS,2]),this.numlines_s=r(F.CBANDS),this.numlines_l=r(F.CBANDS),this.rnumlines_l=i(F.CBANDS),this.mld_cb_l=i(F.CBANDS),this.mld_cb_s=i(F.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=i(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=r(2),this.nsPsy=new K,this.VBR_seek_table=new q,this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=l([16,5]),this.bitrate_blockType_Hist=l([16,6]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var a=0;a<this.en.length;a++)this.en[a]=new Z;for(a=0;a<this.thm.length;a++)this.thm[a]=new Z;for(a=0;a<this.header.length;a++)this.header[a]=new t}function U(){var e=i(F.BLKSIZE),t=i(F.BLKSIZE_s/2),a=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function s(e,t,s){var n,r,i,_=0,o=t+(s<<=1);n=4;do{var l,f,c,u,h,p,m;m=n>>1,p=(h=n<<1)+(u=n),n=h<<1,i=(r=t)+m;do{A=e[r+0]-e[r+u],M=e[r+0]+e[r+u],T=e[r+h]-e[r+p],R=e[r+h]+e[r+p],e[r+h]=M-R,e[r+0]=M+R,e[r+p]=A-T,e[r+u]=A+T,A=e[i+0]-e[i+u],M=e[i+0]+e[i+u],T=b.SQRT2*e[i+p],R=b.SQRT2*e[i+h],e[i+h]=M-R,e[i+0]=M+R,e[i+p]=A-T,e[i+u]=A+T,i+=n,r+=n}while(r<o);for(f=a[_+0],l=a[_+1],c=1;c<m;c++){var d,v;d=1-2*l*l,v=2*l*f,r=t+c,i=t+u-c;do{var g,w,S,M,A,B,R,k,T,y;w=v*e[r+u]-d*e[i+u],g=d*e[r+u]+v*e[i+u],A=e[r+0]-g,M=e[r+0]+g,B=e[i+0]-w,S=e[i+0]+w,w=v*e[r+p]-d*e[i+p],g=d*e[r+p]+v*e[i+p],T=e[r+h]-g,R=e[r+h]+g,y=e[i+h]-w,k=e[i+h]+w,w=l*R-f*y,g=f*R+l*y,e[r+h]=M-g,e[r+0]=M+g,e[i+p]=B-w,e[i+u]=B+w,w=f*k-l*T,g=l*k+f*T,e[i+h]=S-g,e[i+0]=S+g,e[r+p]=A-w,e[r+u]=A+w,i+=n,r+=n}while(r<o);f=(d=f)*a[_+0]-l*a[_+1],l=d*a[_+1]+l*a[_+0]}_+=2}while(n<s)}var n=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(e,a,r,i,_){for(var o=0;o<3;o++){var l=F.BLKSIZE_s/2,f=65535&192*(o+1),c=F.BLKSIZE_s/8-1;do{var u,h,b,p,m,d=255&n[c<<2];h=(u=t[d]*i[r][_+d+f])-(m=t[127-d]*i[r][_+d+f+128]),u+=m,p=(b=t[d+64]*i[r][_+d+f+64])-(m=t[63-d]*i[r][_+d+f+192]),b+=m,l-=4,a[o][l+0]=u+b,a[o][l+2]=u-b,a[o][l+1]=h+p,a[o][l+3]=h-p,h=(u=t[d+1]*i[r][_+d+f+1])-(m=t[126-d]*i[r][_+d+f+129]),u+=m,p=(b=t[d+65]*i[r][_+d+f+65])-(m=t[62-d]*i[r][_+d+f+193]),b+=m,a[o][l+F.BLKSIZE_s/2+0]=u+b,a[o][l+F.BLKSIZE_s/2+2]=u-b,a[o][l+F.BLKSIZE_s/2+1]=h+p,a[o][l+F.BLKSIZE_s/2+3]=h-p}while(--c>=0);s(a[o],l,F.BLKSIZE_s/2)}},this.fft_long=function(t,a,r,i,_){var o=F.BLKSIZE/8-1,l=F.BLKSIZE/2;do{var f,c,u,h,b,p=255&n[o];c=(f=e[p]*i[r][_+p])-(b=e[p+512]*i[r][_+p+512]),f+=b,h=(u=e[p+256]*i[r][_+p+256])-(b=e[p+768]*i[r][_+p+768]),u+=b,a[0+(l-=4)]=f+u,a[l+2]=f-u,a[l+1]=c+h,a[l+3]=c-h,c=(f=e[p+1]*i[r][_+p+1])-(b=e[p+513]*i[r][_+p+513]),f+=b,h=(u=e[p+257]*i[r][_+p+257])-(b=e[p+769]*i[r][_+p+769]),u+=b,a[l+F.BLKSIZE/2+0]=f+u,a[l+F.BLKSIZE/2+2]=f-u,a[l+F.BLKSIZE/2+1]=c+h,a[l+F.BLKSIZE/2+3]=c-h}while(--o>=0);s(a,l,F.BLKSIZE/2)},this.init_fft=function(a){for(var s=0;s<F.BLKSIZE;s++)e[s]=.42-.5*Math.cos(2*Math.PI*(s+.5)/F.BLKSIZE)+.08*Math.cos(4*Math.PI*(s+.5)/F.BLKSIZE);for(s=0;s<F.BLKSIZE_s/2;s++)t[s]=.5*(1-Math.cos(2*Math.PI*(s+.5)/F.BLKSIZE_s))}}function Q(){var e=new U,t=2.302585092994046,s=2,n=16,_=2,l=16,f=.34,c=1/217621504/(F.BLKSIZE/2),h=.01,g=.8,w=.6,S=.3,M=3.5,A=21,B=.2302585093;function R(e,t){for(var a=0,s=0;s<F.BLKSIZE/2;++s)a+=e[s]*t.ATH.eql_w[s];return a*=c}function k(t,s,n,r,i,_,o,l,f,c,u){var h=t.internal_flags;f<2?(e.fft_long(h,r[i],f,c,u),e.fft_short(h,_[o],f,c,u)):2==f&&a(),s[0]=r[i+0][0],s[0]*=s[0];for(var b=F.BLKSIZE/2-1;b>=0;--b){var p=r[i+0][F.BLKSIZE/2-b],m=r[i+0][F.BLKSIZE/2+b];s[F.BLKSIZE/2-b]=.5*(p*p+m*m)}for(var d=2;d>=0;--d)for(n[d][0]=_[o+0][d][0],n[d][0]*=n[d][0],b=F.BLKSIZE_s/2-1;b>=0;--b)p=_[o+0][d][F.BLKSIZE_s/2-b],m=_[o+0][d][F.BLKSIZE_s/2+b],n[d][F.BLKSIZE_s/2-b]=.5*(p*p+m*m);var v=0;for(b=11;b<F.HBLKSIZE;b++)v+=s[b];h.tot_ener[f]=v,t.analysis&&a(),2==t.athaa_loudapprox&&f<2&&(h.loudness_sq[l][f]=h.loudness_sq_save[f],h.loudness_sq_save[f]=R(s,h))}var T,y,E,x=8,H=23,P=15,I=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749];function L(){T=Math.pow(10,(x+1)/16),y=Math.pow(10,(H+1)/16),E=Math.pow(10,P/10)}var O=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],V=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],N=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276];function D(e,t,a,s,n,r){var i,_,o;if(t>e){if(!(t<e*y))return e+t;i=t/e}else{if(e>=t*y)return e+t;i=e/t}if(e+=t,s+3<=6){if(i>=T)return e;var l=0|b.FAST_LOG10_X(i,16);return e*V[l]}return l=0|b.FAST_LOG10_X(i,16),t=0!=r?n.ATH.cb_s[a]*n.ATH.adjust:n.ATH.cb_l[a]*n.ATH.adjust,e<E*t?e>t?(_=1,l<=13&&(_=N[l]),o=b.FAST_LOG10_X(e/t,10/15),e*((O[l]-_)*o+_)):l>13?e:e*N[l]:e*O[l]}function Y(e,t,a,s,n){var r,i,_=0,o=0;for(r=i=0;r<F.SBMAX_s;++i,++r){for(var l=e.bo_s[r],f=e.npart_s,c=l<f?l:f;i<c;)_+=t[i],o+=a[i],i++;if(e.en[s].s[r][n]=_,e.thm[s].s[r][n]=o,i>=f){++r;break}var u=e.PSY.bo_s_weight[r],h=1-u;_=u*t[i],o=u*a[i],e.en[s].s[r][n]+=_,e.thm[s].s[r][n]+=o,_=h*t[i],o=h*a[i]}for(;r<F.SBMAX_s;++r)e.en[s].s[r][n]=0,e.thm[s].s[r][n]=0}function C(e,t,a,s){var n,r,i=0,_=0;for(n=r=0;n<F.SBMAX_l;++r,++n){for(var o=e.bo_l[n],l=e.npart_l,f=o<l?o:l;r<f;)i+=t[r],_+=a[r],r++;if(e.en[s].l[n]=i,e.thm[s].l[n]=_,r>=l){++n;break}var c=e.PSY.bo_l_weight[n],u=1-c;i=c*t[r],_=c*a[r],e.en[s].l[n]+=i,e.thm[s].l[n]+=_,i=u*t[r],_=u*a[r]}for(;n<F.SBMAX_l;++n)e.en[s].l[n]=0,e.thm[s].l[n]=0}function X(e,t,a,s,n,r){var i,o,f=e.internal_flags;for(o=i=0;o<f.npart_s;++o){for(var c=0,u=0,h=f.numlines_s[o],b=0;b<h;++b,++i){var p=t[r][i];c+=p,u<p&&(u=p)}a[o]=c}for(i=o=0;o<f.npart_s;o++){var m=f.s3ind_s[o][0],d=f.s3_ss[i++]*a[m];for(++m;m<=f.s3ind_s[o][1];)d+=f.s3_ss[i]*a[m],++i,++m;var v=_*f.nb_s1[n][o];if(s[o]=Math.min(d,v),f.blocktype_old[1&n]==F.SHORT_TYPE){v=l*f.nb_s2[n][o];var g=s[o];s[o]=Math.min(v,g)}f.nb_s2[n][o]=f.nb_s1[n][o],f.nb_s1[n][o]=d}for(;o<=F.CBANDS;++o)a[o]=0,s[o]=0}function j(e,t,a,s){var n=e.internal_flags;e.short_blocks!=p.short_block_coupled||0!=t[0]&&0!=t[1]||(t[0]=t[1]=0);for(var r=0;r<n.channels_out;r++)s[r]=F.NORM_TYPE,e.short_blocks==p.short_block_dispensed&&(t[r]=1),e.short_blocks==p.short_block_forced&&(t[r]=0),0!=t[r]?n.blocktype_old[r]==F.SHORT_TYPE&&(s[r]=F.STOP_TYPE):(s[r]=F.SHORT_TYPE,n.blocktype_old[r]==F.NORM_TYPE&&(n.blocktype_old[r]=F.START_TYPE),n.blocktype_old[r]==F.STOP_TYPE&&(n.blocktype_old[r]=F.SHORT_TYPE)),a[r]=n.blocktype_old[r],n.blocktype_old[r]=s[r]}function q(e,t,a){return a>=1?e:a<=0?t:t>0?Math.pow(e/t,a)*t:0}var z=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function Z(e,a){for(var s=309.07,n=0;n<F.SBMAX_s-1;n++)for(var r=0;r<3;r++){var i=e.thm.s[n][r];if(i>0){var _=i*a,o=e.en.s[n][r];o>_&&(s+=o>1e10*_?z[n]*(10*t):z[n]*b.FAST_LOG10(o/_))}}return s}var K=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function G(e,a){for(var s=281.0575,n=0;n<F.SBMAX_l-1;n++){var r=e.thm.l[n];if(r>0){var i=r*a,_=e.en.l[n];_>i&&(s+=_>1e10*i?K[n]*(10*t):K[n]*b.FAST_LOG10(_/i))}}return s}function Q(e,t,a,s,n){var r,i;for(r=i=0;r<e.npart_l;++r){var _,o=0,l=0;for(_=0;_<e.numlines_l[r];++_,++i){var f=t[i];o+=f,l<f&&(l=f)}a[r]=o,s[r]=l,n[r]=o*e.rnumlines_l[r]}}function W(e,t,a,s){var n=I.length-1,r=0,i=a[r]+a[r+1];for(i>0?((_=t[r])<t[r+1]&&(_=t[r+1]),(o=0|(i=20*(2*_-i)/(i*(e.numlines_l[r]+e.numlines_l[r+1]-1))))>n&&(o=n),s[r]=o):s[r]=0,r=1;r<e.npart_l-1;r++){var _,o;(i=a[r-1]+a[r]+a[r+1])>0?((_=t[r-1])<t[r]&&(_=t[r]),_<t[r+1]&&(_=t[r+1]),(o=0|(i=20*(3*_-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]+e.numlines_l[r+1]-1))))>n&&(o=n),s[r]=o):s[r]=0}(i=a[r-1]+a[r])>0?((_=t[r-1])<t[r]&&(_=t[r]),(o=0|(i=20*(2*_-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]-1))))>n&&(o=n),s[r]=o):s[r]=0}var J=[-1730326e-23,-.01703172,-1349528e-23,.0418072,-673278e-22,-.0876324,-30835e-21,.1863476,-1104424e-22,-.627638];function $(e){var t,a,s,n;return t=e,a=(t*=t>=0?3:1.5)>=.5&&t<=2.5?8*((n=t-.5)*n-2*n):0,(s=15.811389+7.5*(t+=.474)-17.5*Math.sqrt(1+t*t))<=-60?0:(t=Math.exp((a+s)*B),t/=.6609193)}function ee(e){return e<0&&(e=0),e*=.001,13*Math.atan(.76*e)+3.5*Math.atan(e*e/56.25)}function te(e,t,a,s,n,_,o,l,c,u,h,b){var p,m=i(F.CBANDS+1),d=l/(b>15?1152:384),v=r(F.HBLKSIZE);l/=c;var g=0,w=0;for(p=0;p<F.CBANDS;p++){var S;for(x=ee(l*g),m[p]=l*g,S=g;ee(l*S)-x<f&&S<=c/2;S++);for(e[p]=S-g,w=p+1;g<S;)v[g++]=p;if(g>c/2){g=c/2,++p;break}}m[p]=l*g;for(var M=0;M<b;M++){var A,B,R,k,T;R=u[M],k=u[M+1],(A=0|Math.floor(.5+h*(R-.5)))<0&&(A=0),(B=0|Math.floor(.5+h*(k-.5)))>c/2&&(B=c/2),a[M]=(v[A]+v[B])/2,t[M]=v[B];var y=d*k;o[M]=(y-m[t[M]])/(m[t[M]+1]-m[t[M]]),o[M]<0?o[M]=0:o[M]>1&&(o[M]=1),T=ee(l*u[M]*h),T=Math.min(T,15.5)/15.5,_[M]=Math.pow(10,1.25*(1-Math.cos(Math.PI*T))-2.5)}g=0;for(var E=0;E<w;E++){var x,H,P=e[E];x=ee(l*g),H=ee(l*(g+P-1)),s[E]=.5*(x+H),x=ee(l*(g-.5)),H=ee(l*(g+P-.5)),n[E]=H-x,g+=P}return w}function ae(e,t,s,n,r,_){var l,f=o([F.CBANDS,F.CBANDS]),c=0;if(_)for(var u=0;u<t;u++)for(l=0;l<t;l++){var h=$(s[u]-s[l])*n[l];f[u][l]=h*r[u]}else a();for(u=0;u<t;u++){for(l=0;l<t&&!(f[u][l]>0);l++);for(e[u][0]=l,l=t-1;l>0&&!(f[u][l]>0);l--);e[u][1]=l,c+=e[u][1]-e[u][0]+1}var b=i(c),p=0;for(u=0;u<t;u++)for(l=e[u][0];l<=e[u][1];l++)b[p++]=f[u][l];return b}function se(e){var t=ee(e);return t=Math.min(t,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*t))-2.5)}function ne(e,t){return e<-.3&&(e=3410),e/=1e3,e=Math.max(.1,e),3.64*Math.pow(e,-.8)-6.8*Math.exp(-.6*Math.pow(e-3.4,2))+6*Math.exp(-.15*Math.pow(e-8.7,2))+.001*(.6+.04*t)*Math.pow(e,4)}this.L3psycho_anal_ns=function(e,t,_,l,f,c,h,b,p,m){var M,B,R,T,y,E,x,H,P,L=e.internal_flags,O=o([2,F.BLKSIZE]),V=o([2,3,F.BLKSIZE_s]),N=i(F.CBANDS+1),z=i(F.CBANDS+1),K=i(F.CBANDS+2),U=r(2),$=r(2),ee=o([2,576]),te=r(F.CBANDS+2),ae=r(F.CBANDS+2);for(u.fill(ae,0),M=L.channels_out,e.mode==v.JOINT_STEREO&&(M=4),P=e.VBR==d.vbr_off?0==L.ResvMax?0:L.ResvSize/L.ResvMax*.5:e.VBR==d.vbr_rh||e.VBR==d.vbr_mtrh||e.VBR==d.vbr_mt?.6:1,B=0;B<L.channels_out;B++){var se=t[B],ne=_+576-350-A+192;for(T=0;T<576;T++){var re,ie;for(re=se[ne+T+10],ie=0,y=0;y<(A-1)/2-1;y+=2)re+=J[y]*(se[ne+T+y]+se[ne+T+A-y]),ie+=J[y+1]*(se[ne+T+y+1]+se[ne+T+A-y-1]);ee[B][T]=re+ie}f[l][B].en.assign(L.en[B]),f[l][B].thm.assign(L.thm[B]),M>2&&a()}for(B=0;B<M;B++){var _e,oe=i(12),le=[0,0,0,0],fe=i(12),ce=1,ue=i(F.CBANDS),he=i(F.CBANDS),be=[0,0,0,0],pe=i(F.HBLKSIZE),me=o([3,F.HBLKSIZE_s]);for(T=0;T<3;T++)oe[T]=L.nsPsy.last_en_subshort[B][T+6],fe[T]=oe[T]/L.nsPsy.last_en_subshort[B][T+4],le[0]+=oe[T];2==B&&a();var de=ee[1&B],ve=0;for(T=0;T<9;T++){for(var ge=ve+64,we=1;ve<ge;ve++)we<Math.abs(de[ve])&&(we=Math.abs(de[ve]));L.nsPsy.last_en_subshort[B][T]=oe[T+3]=we,le[1+T/3]+=we,we>oe[T+3-2]?we/=oe[T+3-2]:we=oe[T+3-2]>10*we?oe[T+3-2]/(10*we):0,fe[T+3]=we}for(e.analysis&&a(),_e=3==B?L.nsPsy.attackthre_s:L.nsPsy.attackthre,T=0;T<12;T++)0==be[T/3]&&fe[T]>_e&&(be[T/3]=T%3+1);for(T=1;T<4;T++)(le[T-1]>le[T]?le[T-1]/le[T]:le[T]/le[T-1])<1.7&&(be[T]=0,1==T&&(be[0]=0));for(0!=be[0]&&0!=L.nsPsy.lastAttacks[B]&&(be[0]=0),3!=L.nsPsy.lastAttacks[B]&&be[0]+be[1]+be[2]+be[3]==0||(ce=0,0!=be[1]&&0!=be[0]&&(be[1]=0),0!=be[2]&&0!=be[1]&&(be[2]=0),0!=be[3]&&0!=be[2]&&(be[3]=0)),B<2?$[B]=ce:a(),p[B]=L.tot_ener[B],k(e,pe,me,O,1&B,V,1&B,l,B,t,_),Q(L,pe,N,ue,he),W(L,ue,he,te),H=0;H<3;H++){var Se,Me;for(X(e,me,z,K,B,H),Y(L,z,K,B,H),x=0;x<F.SBMAX_s;x++){if(Me=L.thm[B].s[x][H],Me*=g,be[H]>=2||1==be[H+1]){var Ae=0!=H?H-1:2;we=q(L.thm[B].s[x][Ae],Me,w*P),Me=Math.min(Me,we)}1==be[H]?(Ae=0!=H?H-1:2,we=q(L.thm[B].s[x][Ae],Me,S*P),Me=Math.min(Me,we)):(0!=H&&3==be[H-1]||0==H&&3==L.nsPsy.lastAttacks[B])&&(Ae=2!=H?H+1:0,we=q(L.thm[B].s[x][Ae],Me,S*P),Me=Math.min(Me,we)),Se=oe[3*H+3]+oe[3*H+4]+oe[3*H+5],6*oe[3*H+5]<Se&&(Me*=.5,6*oe[3*H+4]<Se&&(Me*=.5)),L.thm[B].s[x][H]=Me}}for(L.nsPsy.lastAttacks[B]=be[2],E=0,R=0;R<L.npart_l;R++){for(var Be=L.s3ind[R][0],Re=N[Be]*I[te[Be]],ke=L.s3_ll[E++]*Re;++Be<=L.s3ind[R][1];)Re=N[Be]*I[te[Be]],ke=D(ke,L.s3_ll[E++]*Re,Be,Be-R,L,0);ke*=.158489319246111,L.blocktype_old[1&B]==F.SHORT_TYPE?K[R]=ke:K[R]=q(Math.min(ke,Math.min(s*L.nb_1[B][R],n*L.nb_2[B][R])),ke,P),L.nb_2[B][R]=L.nb_1[B][R],L.nb_1[B][R]=ke}for(;R<=F.CBANDS;++R)N[R]=0,K[R]=0;C(L,N,K,B)}for(e.mode!=v.STEREO&&e.mode!=v.JOINT_STEREO||a(),e.mode==v.JOINT_STEREO&&a(),j(e,$,m,U),B=0;B<M;B++){var Te,ye,Ee,xe=0;B>1?a():(Te=h,xe=0,ye=m[B],Ee=f[l][B]),ye==F.SHORT_TYPE?Te[xe+B]=Z(Ee,L.masking_lower):Te[xe+B]=G(Ee,L.masking_lower),e.analysis&&(L.pinfo.pe[l][B]=Te[xe+B])}return 0},this.psymodel_init=function(a){var s,n,r=a.internal_flags,_=!0,o=13,l=24,f=0,c=0,u=-8.25,b=-4.5,p=i(F.CBANDS),v=i(F.CBANDS),g=i(F.CBANDS),w=a.out_samplerate;switch(a.experimentalZ){default:case 0:_=!0;break;case 1:_=a.VBR!=d.vbr_mtrh&&a.VBR!=d.vbr_mt;break;case 2:_=!1;break;case 3:o=8,f=-1.75,c=-.0125,u=-8.25,b=-2.25}for(r.ms_ener_ratio_old=.25,r.blocktype_old[0]=r.blocktype_old[1]=F.NORM_TYPE,s=0;s<4;++s){for(var S=0;S<F.CBANDS;++S)r.nb_1[s][S]=1e20,r.nb_2[s][S]=1e20,r.nb_s1[s][S]=r.nb_s2[s][S]=1;for(var A=0;A<F.SBMAX_l;A++)r.en[s].l[A]=1e20,r.thm[s].l[A]=1e20;for(S=0;S<3;++S){for(A=0;A<F.SBMAX_s;A++)r.en[s].s[A][S]=1e20,r.thm[s].s[A][S]=1e20;r.nsPsy.lastAttacks[s]=0}for(S=0;S<9;S++)r.nsPsy.last_en_subshort[s][S]=10}for(r.loudness_sq_save[0]=r.loudness_sq_save[1]=0,r.npart_l=te(r.numlines_l,r.bo_l,r.bm_l,p,v,r.mld_l,r.PSY.bo_l_weight,w,F.BLKSIZE,r.scalefac_band.l,F.BLKSIZE/1152,F.SBMAX_l),s=0;s<r.npart_l;s++){var B=f;p[s]>=o&&(B=c*(p[s]-o)/(l-o)+f*(l-p[s])/(l-o)),g[s]=Math.pow(10,B/10),r.numlines_l[s]>0?r.rnumlines_l[s]=1/r.numlines_l[s]:r.rnumlines_l[s]=0}for(r.s3_ll=ae(r.s3ind,r.npart_l,p,v,g,_),S=0,s=0;s<r.npart_l;s++){T=m.MAX_VALUE;for(var R=0;R<r.numlines_l[s];R++,S++){var k=w*S/(1e3*F.BLKSIZE);y=this.ATHformula(1e3*k,a)-20,y=Math.pow(10,.1*y),T>(y*=r.numlines_l[s])&&(T=y)}r.ATH.cb_l[s]=T,(T=20*p[s]/10-20)>6&&(T=100),T<-15&&(T=-15),T-=8,r.minval_l[s]=Math.pow(10,T/10)*r.numlines_l[s]}for(r.npart_s=te(r.numlines_s,r.bo_s,r.bm_s,p,v,r.mld_s,r.PSY.bo_s_weight,w,F.BLKSIZE_s,r.scalefac_band.s,F.BLKSIZE_s/384,F.SBMAX_s),S=0,s=0;s<r.npart_s;s++){var T;for(B=u,p[s]>=o&&(B=b*(p[s]-o)/(l-o)+u*(l-p[s])/(l-o)),g[s]=Math.pow(10,B/10),T=m.MAX_VALUE,R=0;R<r.numlines_s[s];R++,S++){var y;k=w*S/(1e3*F.BLKSIZE_s),y=this.ATHformula(1e3*k,a)-20,y=Math.pow(10,.1*y),T>(y*=r.numlines_s[s])&&(T=y)}r.ATH.cb_s[s]=T,T=7*p[s]/12-7,p[s]>12&&(T*=1+3.1*Math.log(1+T)),p[s]<12&&(T*=1+2.3*Math.log(1-T)),T<-15&&(T=-15),T-=8,r.minval_s[s]=Math.pow(10,T/10)*r.numlines_s[s]}r.s3_ss=ae(r.s3ind_s,r.npart_s,p,v,g,_),L(),e.init_fft(r),r.decay=Math.exp(-1*t/(h*w/192)),n=M,2&a.exp_nspsytune&&(n=1),Math.abs(a.msfix)>0&&(n=a.msfix),a.msfix=n;for(var E=0;E<r.npart_l;E++)r.s3ind[E][1]>r.npart_l-1&&(r.s3ind[E][1]=r.npart_l-1);var x=576*r.mode_gr/w;if(r.ATH.decay=Math.pow(10,-1.2*x),r.ATH.adjust=.01,r.ATH.adjustLimit=1,-1!=a.ATHtype){var H=a.out_samplerate/F.BLKSIZE,P=0;for(k=0,s=0;s<F.BLKSIZE/2;++s)k+=H,r.ATH.eql_w[s]=1/Math.pow(10,this.ATHformula(k,a)/10),P+=r.ATH.eql_w[s];for(P=1/P,s=F.BLKSIZE/2;--s>=0;)r.ATH.eql_w[s]*=P}for(E=S=0;E<r.npart_s;++E)for(s=0;s<r.numlines_s[E];++s)++S;for(E=S=0;E<r.npart_l;++E)for(s=0;s<r.numlines_l[E];++s)++S;for(S=0,s=0;s<r.npart_l;s++)k=w*(S+r.numlines_l[s]/2)/(1*F.BLKSIZE),r.mld_cb_l[s]=se(k),S+=r.numlines_l[s];for(;s<F.CBANDS;++s)r.mld_cb_l[s]=1;for(S=0,s=0;s<r.npart_s;s++)k=w*(S+r.numlines_s[s]/2)/(1*F.BLKSIZE_s),r.mld_cb_s[s]=se(k),S+=r.numlines_s[s];for(;s<F.CBANDS;++s)r.mld_cb_s[s]=1;return 0},this.ATHformula=function(e,t){var a;switch(t.ATHtype){case 0:a=ne(e,9);break;case 1:a=ne(e,-1);break;case 2:default:a=ne(e,0);break;case 3:a=ne(e,1)+6;break;case 4:a=ne(e,t.ATHcurve)}return a}}function W(){var e=this,t=131072;W.V9=410,W.V8=420,W.V7=430,W.V6=440,W.V5=450,W.V4=460,W.V3=470,W.V2=480,W.V1=490,W.V0=500,W.R3MIX=1e3,W.STANDARD=1001,W.EXTREME=1002,W.INSANE=1003,W.STANDARD_FAST=1004,W.EXTREME_FAST=1005,W.MEDIUM=1006,W.MEDIUM_FAST=1007;var s,n,r,_,o,c=16384+t;W.LAME_MAXMP3BUFFER=c;var u,h,b=new Q;function m(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=i(F.SBMAX_l),this.bo_s_weight=i(F.SBMAX_s)}function g(){this.lowerlimit=0}function w(e,t){this.lowpass=t}this.enc=new F,this.setModules=function(e,t,a,i,l,f,c,p,m){s=e,n=t,r=a,_=i,o=l,u=f,h=p,this.enc.setModules(n,b,_,u)};var M=4294479419;function A(e){var t;return e.class_id=M,t=e.internal_flags=new G,e.mode=v.NOT_SET,e.original=1,e.in_samplerate=44100,e.num_channels=2,e.num_samples=-1,e.bWriteVbrTag=!0,e.quality=-1,e.short_blocks=null,t.subblock_gain=-1,e.lowpassfreq=0,e.highpassfreq=0,e.lowpasswidth=-1,e.highpasswidth=-1,e.VBR=d.vbr_off,e.VBR_q=4,e.ATHcurve=-1,e.VBR_mean_bitrate_kbps=128,e.VBR_min_bitrate_kbps=0,e.VBR_max_bitrate_kbps=0,e.VBR_hard_min=0,t.VBR_min_bitrate=1,t.VBR_max_bitrate=13,e.quant_comp=-1,e.quant_comp_short=-1,e.msfix=-1,t.resample_ratio=1,t.OldValue[0]=180,t.OldValue[1]=180,t.CurrentStep[0]=4,t.CurrentStep[1]=4,t.masking_lower=1,t.nsPsy.attackthre=-1,t.nsPsy.attackthre_s=-1,e.scale=-1,e.athaa_type=-1,e.ATHtype=-1,e.athaa_loudapprox=-1,e.athaa_sensitivity=0,e.useTemporal=null,e.interChRatio=-1,t.mf_samples_to_encode=F.ENCDELAY+F.POSTDELAY,e.encoder_padding=0,t.mf_size=F.ENCDELAY-F.MDCTDELAY,e.findReplayGain=!1,e.decode_on_the_fly=!1,t.decode_on_the_fly=!1,t.findReplayGain=!1,t.findPeakSample=!1,t.RadioGain=0,t.AudiophileGain=0,t.noclipGainChange=0,t.noclipScale=-1,e.preset=0,e.write_id3tag_automatic=!0,0}function B(e){return e>1?0:e<=0?1:Math.cos(Math.PI/2*e)}function k(e,t){switch(e){case 44100:return t.version=1,0;case 48e3:return t.version=1,1;case 32e3:return t.version=1,2;case 22050:case 11025:return t.version=0,0;case 24e3:case 12e3:return t.version=0,1;case 16e3:case 8e3:return t.version=0,2;default:return t.version=0,-1}}function y(e,t,a){a<16e3&&(t=2);for(var s=T.bitrate_table[t][1],n=2;n<=14;n++)T.bitrate_table[t][n]>0&&Math.abs(T.bitrate_table[t][n]-e)<Math.abs(s-e)&&(s=T.bitrate_table[t][n]);return s}function E(e,t,a){a<16e3&&(t=2);for(var s=0;s<=14;s++)if(T.bitrate_table[t][s]>0&&T.bitrate_table[t][s]==e)return s;return-1}function x(t,a){var s=[new w(8,2e3),new w(16,3700),new w(24,3900),new w(32,5500),new w(40,7e3),new w(48,7500),new w(56,1e4),new w(64,11e3),new w(80,13500),new w(96,15100),new w(112,15600),new w(128,17e3),new w(160,17500),new w(192,18600),new w(224,19400),new w(256,19700),new w(320,20500)],n=e.nearestBitrateFullIndex(a);t.lowerlimit=s[n].lowpass}function O(e){var t=e.internal_flags,s=32;if(t.lowpass1>0){for(var n=999,r=0;r<=31;r++)(o=r/31)>=t.lowpass2&&(s=Math.min(s,r)),t.lowpass1<o&&o<t.lowpass2&&(n=Math.min(n,r));t.lowpass1=999==n?(s-.75)/31:(n-.75)/31,t.lowpass2=s/31}for(t.highpass2>0&&a(),t.highpass2>0&&a(),r=0;r<32;r++){var i,_,o=r/31;t.highpass2>t.highpass1?a():i=1,_=t.lowpass2>t.lowpass1?B((o-t.lowpass1)/(t.lowpass2-t.lowpass1+1e-20)):1,t.amp_filter[r]=i*_}}function V(e){var t=e.internal_flags;switch(e.quality){default:case 9:t.psymodel=0,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 8:e.quality=7;case 7:t.psymodel=1,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 6:case 5:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=0,t.full_outer_loop=0;break;case 4:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 3:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 2:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 1:case 0:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=2,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0}}function N(e){var t=e.internal_flags;e.frameNum=0,e.write_id3tag_automatic&&h.id3tag_write_v2(e),t.bitrate_stereoMode_Hist=l([16,5]),t.bitrate_blockType_Hist=l([16,6]),t.PeakSample=0,e.bWriteVbrTag&&u.InitVbrTag(e)}function D(e,t){(null==e.in_buffer_0||e.in_buffer_nsamples<t)&&(e.in_buffer_0=i(t),e.in_buffer_1=i(t),e.in_buffer_nsamples=t)}function Y(e){var t=F.BLKSIZE+e.framesize-F.FFTOFFSET;return t=Math.max(t,512+e.framesize-32)}function C(e,t,r,i,_,o,l){var f,c,u,h,b,p=e.internal_flags,m=0,d=[null,null],v=[null,null];if(p.Class_ID!=M)return-3;if(0==i)return 0;if((b=n.copy_buffer(p,_,o,l,0))<0)return b;if(o+=b,m+=b,v[0]=t,v[1]=r,R.NEQ(e.scale,0)&&R.NEQ(e.scale,1))for(c=0;c<i;++c)v[0][c]*=e.scale,2==p.channels_out&&(v[1][c]*=e.scale);if(R.NEQ(e.scale_left,0)&&R.NEQ(e.scale_left,1))for(c=0;c<i;++c)v[0][c]*=e.scale_left;if(R.NEQ(e.scale_right,0)&&R.NEQ(e.scale_right,1))for(c=0;c<i;++c)v[1][c]*=e.scale_right;2==e.num_channels&&1==p.channels_out&&a(),h=Y(e),d[0]=p.mfbuf[0],d[1]=p.mfbuf[1];for(var g=0;i>0;){var w=[null,null],A=0,B=0;w[0]=v[0],w[1]=v[1];var k=new j;if(q(e,d,w,g,i,k),A=k.n_in,B=k.n_out,p.findReplayGain&&!p.decode_on_the_fly&&s.AnalyzeSamples(p.rgdata,d[0],p.mf_size,d[1],p.mf_size,B,p.channels_out)==S.GAIN_ANALYSIS_ERROR)return-6;if(i-=A,g+=A,p.channels_out,p.mf_size+=B,p.mf_samples_to_encode<1&&a(),p.mf_samples_to_encode+=B,p.mf_size>=h){var T=l-m;if(0==l&&(T=0),(f=X(e,d[0],d[1],_,o,T))<0)return f;for(o+=f,m+=f,p.mf_size-=e.framesize,p.mf_samples_to_encode-=e.framesize,u=0;u<p.channels_out;u++)for(c=0;c<p.mf_size;c++)d[u][c]=d[u][c+e.framesize]}}return m}function X(t,a,s,n,r,i){var _=e.enc.lame_encode_mp3_frame(t,a,s,n,r,i);return t.frameNum++,_}function j(){this.n_in=0,this.n_out=0}function q(e,t,s,n,r,i){var _=e.internal_flags;if(_.resample_ratio<.9999||_.resample_ratio>1.0001)a();else{i.n_out=Math.min(e.framesize,r),i.n_in=i.n_out;for(var o=0;o<i.n_out;++o)t[0][_.mf_size+o]=s[0][n+o],2==_.channels_out&&(t[1][_.mf_size+o]=s[1][n+o])}}this.lame_init=function(){var e=new P;return 0!=A(e)?null:(e.lame_allocated_gfp=1,e)},this.nearestBitrateFullIndex=function(e){var t=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],a=0,s=0,n=0,r=0;r=t[16],n=16,s=t[16],a=16;for(var i=0;i<16;i++)if(Math.max(e,t[i+1])!=e){r=t[i+1],n=i+1,s=t[i],a=i;break}return r-e>e-s?a:n},this.lame_init_params=function(e){var t=e.internal_flags;if(t.Class_ID=0,null==t.ATH&&(t.ATH=new H),null==t.PSY&&(t.PSY=new m),null==t.rgdata&&(t.rgdata=new L),t.channels_in=e.num_channels,1==t.channels_in&&(e.mode=v.MONO),t.channels_out=e.mode==v.MONO?1:2,t.mode_ext=F.MPG_MD_MS_LR,e.mode==v.MONO&&(e.force_ms=!1),e.VBR==d.vbr_off&&128!=e.VBR_mean_bitrate_kbps&&0==e.brate&&(e.brate=e.VBR_mean_bitrate_kbps),e.VBR==d.vbr_off||e.VBR==d.vbr_mtrh||e.VBR==d.vbr_mt||(e.free_format=!1),e.VBR==d.vbr_off&&0==e.brate&&a(),e.VBR==d.vbr_off&&e.compression_ratio>0&&a(),0!=e.out_samplerate&&(e.out_samplerate<16e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,64)):e.out_samplerate<32e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,160)):(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,32),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320))),0==e.lowpassfreq){var s=16e3;switch(e.VBR){case d.vbr_off:x(i=new g,e.brate),s=i.lowerlimit;break;case d.vbr_abr:var i;x(i=new g,e.VBR_mean_bitrate_kbps),s=i.lowerlimit;break;case d.vbr_rh:a();default:a()}e.mode!=v.MONO||e.VBR!=d.vbr_off&&e.VBR!=d.vbr_abr||(s*=1.5),e.lowpassfreq=0|s}switch(0==e.out_samplerate&&a(),e.lowpassfreq=Math.min(20500,e.lowpassfreq),e.lowpassfreq=Math.min(e.out_samplerate/2,e.lowpassfreq),e.VBR==d.vbr_off&&(e.compression_ratio=16*e.out_samplerate*t.channels_out/(1e3*e.brate)),e.VBR==d.vbr_abr&&a(),e.bWriteVbrTag||(e.findReplayGain=!1,e.decode_on_the_fly=!1,t.findPeakSample=!1),t.findReplayGain=e.findReplayGain,t.decode_on_the_fly=e.decode_on_the_fly,t.decode_on_the_fly&&(t.findPeakSample=!0),t.findReplayGain&&a(),t.decode_on_the_fly&&!e.decode_only&&a(),t.mode_gr=e.out_samplerate<=24e3?1:2,e.framesize=576*t.mode_gr,e.encoder_delay=F.ENCDELAY,t.resample_ratio=e.in_samplerate/e.out_samplerate,e.VBR){case d.vbr_mt:case d.vbr_rh:case d.vbr_mtrh:var l=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5];e.compression_ratio=l[e.VBR_q];break;case d.vbr_abr:e.compression_ratio=16*e.out_samplerate*t.channels_out/(1e3*e.VBR_mean_bitrate_kbps);break;default:e.compression_ratio=16*e.out_samplerate*t.channels_out/(1e3*e.brate)}e.mode==v.NOT_SET&&(e.mode=v.JOINT_STEREO),e.highpassfreq>0?a():(t.highpass1=0,t.highpass2=0),e.lowpassfreq>0?(t.lowpass2=2*e.lowpassfreq,e.lowpasswidth>=0?a():t.lowpass1=2*e.lowpassfreq,t.lowpass1/=e.out_samplerate,t.lowpass2/=e.out_samplerate):a(),O(e),t.samplerate_index=k(e.out_samplerate,e),t.samplerate_index<0&&a(),e.VBR==d.vbr_off?e.free_format?t.bitrate_index=0:(e.brate=y(e.brate,e.version,e.out_samplerate),t.bitrate_index=E(e.brate,e.version,e.out_samplerate),t.bitrate_index<=0&&a()):t.bitrate_index=1,e.analysis&&(e.bWriteVbrTag=!1),null!=t.pinfo&&(e.bWriteVbrTag=!1),n.init_bit_stream_w(t);for(var f,c=t.samplerate_index+3*e.version+6*(e.out_samplerate<16e3?1:0),u=0;u<F.SBMAX_l+1;u++)t.scalefac_band.l[u]=_.sfBandIndex[c].l[u];for(u=0;u<F.PSFB21+1;u++){var h=(t.scalefac_band.l[22]-t.scalefac_band.l[21])/F.PSFB21,w=t.scalefac_band.l[21]+u*h;t.scalefac_band.psfb21[u]=w}for(t.scalefac_band.psfb21[F.PSFB21]=576,u=0;u<F.SBMAX_s+1;u++)t.scalefac_band.s[u]=_.sfBandIndex[c].s[u];for(u=0;u<F.PSFB12+1;u++)h=(t.scalefac_band.s[13]-t.scalefac_band.s[12])/F.PSFB12,w=t.scalefac_band.s[12]+u*h,t.scalefac_band.psfb12[u]=w;for(t.scalefac_band.psfb12[F.PSFB12]=192,1==e.version?t.sideinfo_len=1==t.channels_out?21:36:t.sideinfo_len=1==t.channels_out?13:21,e.error_protection&&(t.sideinfo_len+=2),N(e),t.Class_ID=M,f=0;f<19;f++)t.nsPsy.pefirbuf[f]=700*t.mode_gr*t.channels_out;switch(-1==e.ATHtype&&(e.ATHtype=4),e.VBR){case d.vbr_mt:e.VBR=d.vbr_mtrh;case d.vbr_mtrh:null==e.useTemporal&&(e.useTemporal=!1),r.apply_preset(e,500-10*e.VBR_q,0),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),e.quality<5&&(e.quality=0),e.quality>5&&(e.quality=5),t.PSY.mask_adjust=e.maskingadjust,t.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?t.sfb21_extra=!1:t.sfb21_extra=e.out_samplerate>44e3,t.iteration_loop=new VBRNewIterationLoop(o);break;case d.vbr_rh:r.apply_preset(e,500-10*e.VBR_q,0),t.PSY.mask_adjust=e.maskingadjust,t.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?t.sfb21_extra=!1:t.sfb21_extra=e.out_samplerate>44e3,e.quality>6&&(e.quality=6),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),t.iteration_loop=new VBROldIterationLoop(o);break;default:var S;t.sfb21_extra=!1,e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),(S=e.VBR)==d.vbr_off&&(e.VBR_mean_bitrate_kbps=e.brate),r.apply_preset(e,e.VBR_mean_bitrate_kbps,0),e.VBR=S,t.PSY.mask_adjust=e.maskingadjust,t.PSY.mask_adjust_short=e.maskingadjust_short,S==d.vbr_off?t.iteration_loop=new I(o):a()}return e.VBR!=d.vbr_off&&a(),e.tune&&a(),V(e),e.athaa_type<0?t.ATH.useAdjust=3:t.ATH.useAdjust=e.athaa_type,t.ATH.aaSensitivityP=Math.pow(10,e.athaa_sensitivity/-10),null==e.short_blocks&&(e.short_blocks=p.short_block_allowed),e.short_blocks!=p.short_block_allowed||e.mode!=v.JOINT_STEREO&&e.mode!=v.STEREO||(e.short_blocks=p.short_block_coupled),e.quant_comp<0&&(e.quant_comp=1),e.quant_comp_short<0&&(e.quant_comp_short=0),e.msfix<0&&(e.msfix=0),e.exp_nspsytune=1|e.exp_nspsytune,e.internal_flags.nsPsy.attackthre<0&&(e.internal_flags.nsPsy.attackthre=Q.NSATTACKTHRE),e.internal_flags.nsPsy.attackthre_s<0&&(e.internal_flags.nsPsy.attackthre_s=Q.NSATTACKTHRE_S),e.scale<0&&(e.scale=1),e.ATHtype<0&&(e.ATHtype=4),e.ATHcurve<0&&(e.ATHcurve=4),e.athaa_loudapprox<0&&(e.athaa_loudapprox=2),e.interChRatio<0&&(e.interChRatio=0),null==e.useTemporal&&(e.useTemporal=!0),t.slot_lag=t.frac_SpF=0,e.VBR==d.vbr_off&&(t.slot_lag=t.frac_SpF=72e3*(e.version+1)*e.brate%e.out_samplerate|0),_.iteration_init(e),b.psymodel_init(e),0},this.lame_encode_flush=function(e,t,s,r){var i,_,o,l,c=e.internal_flags,u=f([2,1152]),h=0,b=c.mf_samples_to_encode-F.POSTDELAY,p=Y(e);if(c.mf_samples_to_encode<1)return 0;for(i=0,e.in_samplerate!=e.out_samplerate&&a(),(o=e.framesize-b%e.framesize)<576&&(o+=e.framesize),e.encoder_padding=o,l=(b+o)/e.framesize;l>0&&h>=0;){var m=p-c.mf_size,d=e.frameNum;m*=e.in_samplerate,(m/=e.out_samplerate)>1152&&(m=1152),m<1&&(m=1),_=r-i,0==r&&(_=0),s+=h=this.lame_encode_buffer(e,u[0],u[1],m,t,s,_),i+=h,l-=d!=e.frameNum?1:0}return c.mf_samples_to_encode=0,h<0?h:(_=r-i,0==r&&(_=0),n.flush_bitstream(e),(h=n.copy_buffer(c,t,s,_,1))<0?h:(s+=h,_=r-(i+=h),0==r&&(_=0),e.write_id3tag_automatic&&a(),i))},this.lame_encode_buffer=function(e,t,a,s,n,r,i){var _=e.internal_flags,o=[null,null];if(_.Class_ID!=M)return-3;if(0==s)return 0;D(_,s),o[0]=_.in_buffer_0,o[1]=_.in_buffer_1;for(var l=0;l<s;l++)o[0][l]=t[l],_.channels_in>1&&(o[1][l]=a[l]);return C(e,o[0],o[1],s,n,r,i)}}function J(){this.setModules=function(e,t){}}function $(){this.setModules=function(e,t,a){}}function ee(){}function te(){this.setModules=function(e,t){}}function ae(e,t,n){1!=e&&a("fix cc: only supports mono");var r=new W,i=new J,_=new S,o=new R,l=new M,f=new V,c=new C,u=new B,h=new g,b=new te,p=new A,m=new w,d=new $,k=new ee;r.setModules(_,o,l,f,c,u,h,b,k),o.setModules(_,k,h,u),b.setModules(o,h),l.setModules(r),c.setModules(o,p,f,m),f.setModules(m,p,r.enc.psy),p.setModules(o),m.setModules(f),u.setModules(r,o,h),i.setModules(d,k),d.setModules(h,b,l);var T=r.lame_init();T.num_channels=e,T.in_samplerate=t,T.out_samplerate=t,T.brate=n,T.mode=v.STEREO,T.quality=3,T.bWriteVbrTag=!1,T.disable_reservoir=!0,T.write_id3tag_automatic=!1,r.lame_init_params(T);var y=1152,E=0|1.25*y+7200,x=s(E);this.encodeBuffer=function(t,a){1==e&&(a=t),t.length>y&&(y=t.length,x=s(E=0|1.25*y+7200));var n=r.lame_encode_buffer(T,t,a,t.length,x,0,E);return new Int8Array(x.subarray(0,n))},this.flush=function(){var e=r.lame_encode_flush(T,x,0,E);return new Int8Array(x.subarray(0,e))}}Y.SFBMAX=3*F.SBMAX_s,F.ENCDELAY=576,F.POSTDELAY=1152,F.MDCTDELAY=48,F.FFTOFFSET=224+F.MDCTDELAY,F.DECDELAY=528,F.SBLIMIT=32,F.CBANDS=64,F.SBPSY_l=21,F.SBPSY_s=12,F.SBMAX_l=22,F.SBMAX_s=13,F.PSFB21=6,F.PSFB12=6,F.BLKSIZE=1024,F.HBLKSIZE=F.BLKSIZE/2+1,F.BLKSIZE_s=256,F.HBLKSIZE_s=F.BLKSIZE_s/2+1,F.NORM_TYPE=0,F.START_TYPE=1,F.SHORT_TYPE=2,F.STOP_TYPE=3,F.MPG_MD_LR_LR=0,F.MPG_MD_LR_I=1,F.MPG_MD_MS_LR=2,F.MPG_MD_MS_I=3,F.fircoef=[-.1039435,-.1892065,5*-.0432472,-.155915,3898045e-23,.0467745*5,.50455,.756825,.187098*5],G.MFSIZE=3456+F.ENCDELAY-F.MDCTDELAY,G.MAX_HEADER_BUF=256,G.MAX_BITS_PER_CHANNEL=4095,G.MAX_BITS_PER_GRANULE=7680,G.BPC=320,Y.SFBMAX=3*F.SBMAX_s,t.Mp3Encoder=ae}t(),e.lamejs=t}(("object"==typeof window&&window.document?window:Object).Recorder);