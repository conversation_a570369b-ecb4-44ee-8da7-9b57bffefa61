/*
錄音
https://github.com/xiangyuecn/Recorder
src: engine/beta-amr.js,engine/beta-amr-engine.js,engine/wav.js
*/
!function(){var A="object"==typeof window&&!!window.document,e=(A?window:Object).Recorder,f=e.i18n;!function(A,e,f,i){"use strict";var r="4.75, 5.15, 5.9, 6.7, 7.4, 7.95, 10.2, 12.2";A.prototype.enc_amr={stable:!0,takeEC:"full",getTestMsg:function(){return f("b2mN::AMR-NB(NarrowBand)，采样率设置无效（只提供8000hz），比特率范围：{1}（默认12.2kbps），一帧20ms、{2}字节；浏览器一般不支持播放amr格式，可用Recorder.amr2wav()转码成wav播放",0,r,"Math.ceil(bitRate/8*20)+1")}};var n,t=function(e){var i=e.bitRate,r=A.AMR.BitRate(i),n=e.sampleRate,t=8e3;i==r&&n==t||A.CLog(f("tQBv::AMR Info: 和设置的不匹配{1}，已更新成{2}",0,"set:"+i+"kbps "+n+"hz","set:"+r+"kbps "+t+"hz"),3),e.bitRate=r,e.sampleRate=t},w=function(){return f.G("NeedImport-2",["beta-amr.js","src/engine/beta-amr-engine.js"])},l=i&&"function"==typeof Worker;A.amr2wav=function(e,i,r){if(A.AMR)if(A.prototype.wav){var n=function(e,f){var n=new Uint8Array(e);A.AMR.decode(n,function(e){var n=A({type:"wav"});f&&(n.dataType="arraybuffer"),n.mock(e,8e3).stop(function(A,e,f){i(A,e,f)},r)},r)};if(e instanceof ArrayBuffer)n(e,1);else{var t=new FileReader;t.onloadend=function(){n(t.result)},t.readAsArrayBuffer(e)}}else r(f.G("NeedImport-2",["amr2wav","src/engine/wav.js"]));else r(w())},A.prototype.amr=function(e,i,r){var n=this,B=n.set,a=B.sampleRate,s=8e3;if(A.AMR){if(t(B),a>s)e=A.SampleData([e],a,s).data;else if(a<s)return void r(f("q12D::数据采样率低于{1}",0,s));if(l){var o=n.amr_start(B);if(o){if(o.isW)return n.amr_encode(o,e),void n.amr_complete(o,i,r,1);n.amr_stop(o)}}A.AMR.encode(e,function(A){i(A.buffer,"audio/amr")},r,B.bitRate)}else r(w())},A.BindDestroy("amrWorker",function(){n&&(A.CLog("amrWorker Destroy"),n.terminate(),n=null)}),A.prototype.amr_envCheck=function(e,i){var r="";return i.takeoffEncodeChunk&&(a()||(r=f("TxjV::当前浏览器版本太低，无法实时处理"))),r||A.AMR||(r=w()),r},A.prototype.amr_start=function(A){return a(A)};var B={id:0},a=function(e,i){var r,w=function(A){var e=A.data,f=r.wkScope.wk_ctxs,i=r.wkScope.wk_AMR,n=f[e.id];if("init"==e.action)f[e.id]={takeoff:e.takeoff,memory:new Uint8Array(5e5),mOffset:0,encObj:i.GetEncoder(e.bitRate)};else if(!n)return;var t=function(A){var e=A.length;if(n.mOffset+e>n.memory.length){var f=new Uint8Array(n.memory.length+Math.max(5e5,e));f.set(n.memory.subarray(0,n.mOffset)),n.memory=f}n.memory.set(A,n.mOffset),n.mOffset+=e};switch(e.action){case"stop":if(!n.isCp)try{n.encObj.flush()}catch(A){console.error(A)}n.encObj=null,delete f[e.id];break;case"encode":if(n.isCp)break;try{var w=n.encObj.encode(e.pcm)}catch(A){n.err=A,console.error(A);break}if(!n._h){n._h=1;var l=i.GetHeader(),B=new Uint8Array(l.length+w.length);B.set(l),B.set(w,l.length),w=B}w.length>0&&(n.takeoff?Q.onmessage({action:"takeoff",id:e.id,chunk:w}):t(w));break;case"complete":n.isCp=1;try{n.encObj.flush()}catch(A){console.error(A)}if(n.err){Q.onmessage({action:e.action,id:e.id,err:"AMR Encoder: "+n.err.message});break}Q.onmessage({action:e.action,id:e.id,blob:n.memory.buffer.slice(0,n.mOffset)})}},s=function(A){Q.onmessage=function(e){var f=e;A&&(f=e.data);var i=B[f.id];i&&("takeoff"==f.action?i.set.takeoffEncodeChunk(new Uint8Array(f.chunk.buffer)):(i.call&&i.call(f),i.call=null))}},o=function(){var i={worker:Q,set:e};if(e){i.id=++B.id,B[i.id]=i,t(e);var r=!!e.takeoffEncodeChunk;r&&A.CLog(f("Q7p7::takeoffEncodeChunk接管AMR编码器输出的二进制数据，只有首次回调数据（首帧）包含AMR头；在合并成AMR文件时，如果没有把首帧数据包含进去，则必须在文件开头添加上AMR头：Recorder.AMR.AMR_HEADER（转成二进制），否则无法播放"),3),Q.postMessage({action:"init",id:i.id,sampleRate:e.sampleRate,bitRate:e.bitRate,takeoff:r,x:new Int16Array(5)})}else Q.postMessage({x:new Int16Array(5)});return i},Q=n;if(i||!l)return A.CLog(f("6o9Z::当前环境不支持Web Worker，amr实时编码器运行在主线程中"),3),Q={postMessage:function(A){w({data:A})}},r={wkScope:{wk_ctxs:{},wk_AMR:A.AMR}},s(),o();try{if(!Q){var g=(w+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),v=");self.onmessage="+(g=g.replace(/[\w\$]+\.wkScope/g,"wkScope"));v+=";var wkScope={ wk_ctxs:{},wk_AMR:Create() }";var u=A.AMR.Create.toString(),c=(window.URL||webkitURL).createObjectURL(new Blob(["var Create=(",u,v],{type:"text/javascript"}));Q=new Worker(c),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(c)},1e4),s(1)}var C=o();return C.isW=1,n=Q,C}catch(A){return Q&&Q.terminate(),console.error(A),a(e,1)}};A.prototype.amr_stop=function(e){if(e&&e.worker){e.worker.postMessage({action:"stop",id:e.id}),e.worker=null,delete B[e.id];var i=-1;for(var r in B)i++;i&&A.CLog(f("yYWs::amr worker剩{1}个未stop",0,i),3)}},A.prototype.amr_encode=function(A,e){A&&A.worker&&A.worker.postMessage({action:"encode",id:A.id,pcm:e})},A.prototype.amr_complete=function(A,e,i,r){var n=this;A&&A.worker?(A.call=function(f){r&&n.amr_stop(A),f.err?i(f.err):e(f.blob,"audio/amr")},A.worker.postMessage({action:"complete",id:A.id})):i(f("jOi8::amr编码器未start"))}}(e,0,f.$T,A)}(),function(A){"use strict";function e(){var A=function(){var A=this,e=i.Decoder_Interface_init(),f=r._malloc(i.AMR_BUFFER_COUNT),n=new Uint8Array(r.HEAPU8.buffer,f,i.AMR_BUFFER_COUNT);f=r._malloc(2*i.PCM_BUFFER_COUNT);var t=new Int16Array(r.HEAPU8.buffer,f,i.PCM_BUFFER_COUNT),w=[];A.decode=function(A){if(w.length){var f=new Uint8Array(w.length+A.length);f.set(w),f.set(A,w.length),A=f}for(var r=null,l=0,B=0;l+1<A.length;){var a=i.SIZES[A[l]>>3&15];if(null==a)throw new Error("Invalid amr frame type: "+A[l]);if(a+=1,r||(r=new Int16Array(Math.floor(A.length/Math.max(13,a)*i.PCM_BUFFER_COUNT))),l+a>A.length)break;if(n.set(A.subarray(l,l+a)),i.Decoder_Interface_Decode(e,n.byteOffset,t.byteOffset,0),B+i.PCM_BUFFER_COUNT>r.length){var s=new Int16Array(2*r.length);s.set(r.subarray(0,B)),r=s}l+=a,r.set(t,B),B+=i.PCM_BUFFER_COUNT}return w=A.subarray(l),new Int16Array(null==r?0:r.subarray(0,B))},A.flush=function(){return r._free(n.byteOffset),r._free(t.byteOffset),i.Decoder_Interface_exit(e),new Int16Array(0)}},f=function(A){var e=this,f=i.Mode[i.BitRate(A)],n=i.Encoder_Interface_init(),t=r._malloc(2*i.PCM_BUFFER_COUNT),w=new Int16Array(r.HEAPU8.buffer,t,i.PCM_BUFFER_COUNT);t=r._malloc(i.AMR_BUFFER_COUNT);var l=new Uint8Array(r.HEAPU8.buffer,t,i.AMR_BUFFER_COUNT),B=[];e.encode=function(A){if(B.length){var e=new Int16Array(B.length+A.length);e.set(B),e.set(A,B.length),A=e}for(var r=i.SIZES[f]+1,t=new Uint8Array(Math.ceil(A.length/i.PCM_BUFFER_COUNT*r)),a=0,s=0;a+i.PCM_BUFFER_COUNT<A.length;){w.set(A.subarray(a,a+i.PCM_BUFFER_COUNT));var o=i.Encoder_Interface_Encode(n,f,w.byteOffset,l.byteOffset,0);if(o!=r){console.error([o,r]);break}a+=i.PCM_BUFFER_COUNT,t.set(l.subarray(0,o),s),s+=o}return B=A.subarray(a),new Uint8Array(t.subarray(0,s))},e.flush=function(){return r._free(w.byteOffset),r._free(l.byteOffset),i.Encoder_Interface_exit(n),new Uint8Array(0)}},i={GetDecoder:function(){return e().DecG_()},GetEncoder:function(A){return e().EncG_(A)},decode:function(A,f,i){e().dec__(A,f,i)},encode:function(A,f,i,r){e().enc__(A,f,i,r)},DecG_:function(){return new A},dec__:function(e,f,r){var n=new A,t=[],w=0,l=function(){try{n.flush()}catch(A){console.error(A)}},B=0,a=0;String.fromCharCode.apply(null,e.subarray(0,this.AMR_HEADER.length))==this.AMR_HEADER&&(B+=this.AMR_HEADER.length,a=1);var s=(i.SIZES[e[B]>>3&15]||31)+1;s=1e3*Math.max(13,s)/20*5;var o=function(){try{var A=B;if(A<e.length){B+=s;var i=n.decode(e.subarray(A,B));t.push(i),w+=i.length}if(B<e.length)return void setTimeout(o)}catch(A){return console.error(A),l(),void r("AMR Decoder: "+(a?A.message:"Not an amr audio file"))}l();for(var Q=new Int16Array(w),g=0,v=0;g<t.length;g++)i=t[g],Q.set(i,v),v+=i.length;f(Q)};o()},EncG_:function(A){return new f(A)},enc__:function(A,e,i,r){var n=this,t=new f(r),w=[n.GetHeader()],l=w[0].length,B=function(){try{t.flush()}catch(A){console.error(A)}},a=4e4;a-=a%n.PCM_BUFFER_COUNT;var s=0,o=function(){try{var f=s;if(f<A.length){s+=a;var r=t.encode(A.subarray(f,s));w.push(r),l+=r.length}if(s<A.length)return void setTimeout(o)}catch(A){return console.error(A),B(),void i("AMR Encoder: "+A.message)}B();for(var n=new Uint8Array(l),Q=0,g=0;Q<w.length;Q++)r=w[Q],n.set(r,g),g+=r.length;e(n)};o()},GetHeader:function(){for(var A=this.AMR_HEADER,e=new Uint8Array(A.length),f=0;f<A.length;f++)e[f]=A.charCodeAt(f);return e},BitRate:function(A){var e=this.Mode;if(A){if(null!=e[A])return A;var f=[];for(var i in e)f.push({v:+i,s:Math.abs(i-A||i-12.2)});return f.sort(function(A,e){return A.s-e.s}),f[0].v}return 12.2},Mode:{4.75:0,5.15:1,5.9:2,6.7:3,7.4:4,7.95:5,10.2:6,12.2:7},SIZES:[12,13,15,17,19,20,26,31,5,6,5,5,0,0,0,0],AMR_BUFFER_COUNT:32,PCM_BUFFER_COUNT:160,AMR_HEADER:"#!AMR\n",WAV_HEADER_SIZE:44},r={_main:function(){return i.Decoder_Interface_init=r._Decoder_Interface_init,i.Decoder_Interface_exit=r._Decoder_Interface_exit,i.Decoder_Interface_Decode=r._Decoder_Interface_Decode,i.Encoder_Interface_init=r._Encoder_Interface_init,i.Encoder_Interface_exit=r._Encoder_Interface_exit,i.Encoder_Interface_Encode=r._Encoder_Interface_Encode,0}};i.Module=r;var n={staticAlloc:function(A){var e=P;return P=15+(P=P+A|0)&-16,e},dynamicAlloc:function(A){var e=d;return(d=15+(d=d+A|0)&-16)>=H&&!I()?(d=e,0):e},alignMemory:function(A,e){return A=Math.ceil(A/(e||16))*(e||16)},GLOBAL_BASE:8};r.Runtime=n;var t=!1;function w(A,e){A||Y("Assertion failed: "+e)}var l=2,B=4;function a(A,e,f,i){var r,t;"number"==typeof A?(r=!0,t=A):(r=!1,t=A.length);var a,s="string"==typeof e?e:null;if(f==B?a=i:(f!=l&&Y("fix !ALLOC_STATIC"),a=n.staticAlloc(Math.max(t,s?1:e.length))),r){var g;for(i=a,w(!(3&a)),g=a+(-4&t);i<g;i+=4)u[i>>2]=0;for(g=a+t;i<g;)o[0|i++]=0;return a}if("i8"===s)return A.subarray||A.slice?Q.set(A,a):Q.set(new Uint8Array(A),a),a;Y("fix allocate")}r.ALLOC_STATIC=l,r.ALLOC_NONE=B,r.allocate=a;var s,o,Q,g,v,u,c,C,D,E=4096;function h(A){return A%4096>0&&(A+=4096-A%4096),A}var P=0,k=0,b=0,d=0;function I(){Y("enlargeMemory")}for(var M,F=r.TOTAL_STACK||65536,H=r.TOTAL_MEMORY||524288,U=65536;U<H||U<2*F;)U<16777216?U*=2:U+=16777216;U!==H&&Y("fix t!==T"),M=new ArrayBuffer(H),o=new Int8Array(M),g=new Int16Array(M),u=new Int32Array(M),Q=new Uint8Array(M),v=new Uint16Array(M),c=new Uint32Array(M),C=new Float32Array(M),D=new Float64Array(M),u[0]=255,w(255===Q[0]&&0===Q[3],"fix !LE"),r.HEAP=s,r.buffer=M,r.HEAP8=o,r.HEAP16=g,r.HEAP32=u,r.HEAPU8=Q,r.HEAPU16=v,r.HEAPU32=c,r.HEAPF32=C,r.HEAPF64=D,Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(A,e){var f=65535&A,i=65535&e;return f*i+((A>>>16)*i+f*(e>>>16)<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(A){A>>>=0;for(var e=0;e<32;e++)if(A&1<<31-e)return e;return 32}),Math.clz32=Math.clz32,Math.abs,Math.cos,Math.sin,Math.tan,Math.acos,Math.asin,Math.atan,Math.atan2,Math.exp,Math.log,Math.sqrt,Math.ceil,Math.floor,Math.pow,Math.imul,Math.fround,Math.min,Math.clz32,P=31784,r.b64Atob=function(A){for(var e,f,i=0,r=0,n="";(f=A.charCodeAt(r++))&&61!=f;){if(f>64&&f<91)f-=65;else if(f>96&&f<123)f-=71;else if(f>47&&f<58)f+=4;else if(43==f)f=62;else{if(47!=f)continue;f=63}e=i%4?64*e+f:f,i++%4&&(n+=String.fromCharCode(255&e>>(-2*i&6)))}return n},r.b64Dec=function(A){for(var e="function"==typeof atob?atob(A):r.b64Atob(A),f=new Uint8Array(e.length),i=0;i<e.length;i++)f[i]=e.charCodeAt(i);return f},a(r.b64Dec("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"),"i8",B,n.GLOBAL_BASE),a(r.b64Dec("YgJIA6gDBgMt/dT6E/ub/v/7lPq4+6D6k/54+qf4oP36+EH5Xv3f/Wv7Qf2mAhIDlACF/7gCCAWEAl4B9v+eAWYCDwCJAFgBLf/S/Bj6zfx5/l78tP0vALH9fvxz/Lf7Xf8I+3H7Y/9I+gv6e/4G+1z7kP2fAtUAxgF8AO7+8/0n/RD+aP/A+noAhwCn9LP9dv5A+bkBzv/EBYgDEwM8AewASP6l/tkAnQFx/Gv8eQA5/lz8ygCk/y/+if7oAYYB2gFsA9kCPAHp+OD6Y/1XAMIDsAEzAgf/3vv6AB0BUQR1BKsBuALy+4D50vlM/FoBoADL/vD+pvyeAnAC4gRQ/Gj+Zv2i/cD+gP4U/uYAQQBOAc7/8P/w/079i/r/BrQGdwWuCQ8IfAXdBL8FkgWC/vP7/v+tAHL+eQTTBegHCQcEA/oE4gWVBccG0Qc3BMIE+QR++8X4z/rY/JP7uPs9/vf7RvlBAEICrP88+n72DvkD/ar6Ev4m/679XfxRART/MgI1CWYKkgfRBfwEagO9AGYBdgER+hf31vY5/An70feM/Vz7+vl9BhMEIgI1AiUE3ALA/7z8Tv5M/qD/ywA2BMAEZAaLA/4F2gNGAcUDTQOOAKz/xQDWAUsJIgZtBNYBvgSLAWAFsARlBBIEXAHh/S77iP4p/0v/4QFl+Kv5Lv8S/V/7hgEdAHH+GwA0A9QE8wK3AtMDmQFS/60ECwSQA0wFNgcg/GP65AEz+lz50ABk/rQEmAXx/oADeARg/vEGmgVg+az1NP8D+fH5CQSI+en5FwUb+8v5OQRj/bn7vv9W/UABp/6TAjEBLQT0+tz87f+d+fX6HQBt+Q/+RwDh/vn/nP8S/j78E/9UA1kHzAY/+5X64wBs/S4BdQFgAD8E6QTO+3v5oADlARwIBgda/CT/KAKs/Zz97QBQAbgGbwODAnUCsgHzBAoCYQYPAPQAR/7DBc0CuAAbBzYGrQbcAwUBqQMtCCkJ8AVbCEIHRga//b39TftE/IcAGP4w/jMArv6L/aT+Lf16BBkIogWQCLoFjwNc+tz5Gvd4BcYCEQU3BXkCoAOaBZIIIgp2CZwIWQfWA8IIPgd8ARgDeQTB/+X9ngEE/zz8xv4T+1X99PzB/PL9E/x++ZH7WP61+Tz+1f70BBgEggJ7BFUDWANd/bD+iwDcCD8FigUdAAADHQM4+6cBNALa+sb79QDq+tT8PQLu+q/5hgI4/EID0wLhA3QG6wdB/8/8sAGWAjwAxgByAuUDMgVwBqsHCQXD+aP/0//A+yUArP91Bi8KIQkpBPgHSQlzBBYJRghbA2UB5gWYAssESwTfAVAFkANpB9oG4weQBHUH+AaPASIAAAGv/dD+4/sjAp4GfwWHAp3/q/7UBW8GpgQmAHz9LP+LAU4D3gBA/QP9NP0s/VT4DPVq/yMBrv/RBLMF7wN0/2X/mQC3ASkBIAYH+mb+hP0ABscBE//Q+nX//P4TAioCZAMNAfAEXgIX/3MDzwHmAlgCiP+3/6UB1ABJ/sb/JAP6+if72AImARb+MgCx/Xf8GvsqAFH9kwDn/xEBVALJ/r0EWQIO/VEDSAKtAV8CSwKm/Vr/zQHk/Mn8CQNkBY4D2wZ3AIkFzAMl/5D8xPnn+w78tgE3/Z36TgAAAEH+ZfuQ+9n62/nICOcGHQWyAy8GmAV+BOIBtAEr/qz7agJB/jr8QAQc+xX6jv+w+yj4vf3SAGUC8QFJ+GP6ggIL+6j88wOS+V/7JwTt+WD9tARk+ab7bwIt/NL6A/sbAm3/fgO2+n/8Tv54A9sBrAGZAID+UgEs+gH+ZwEy/KX7Kv5pANr9pQJX/If7bQN8Afz+0gCVBpwD6ATvBqYERwSLBXcCFQJzAisBpf5l/ur9hwJ2/R0Arf2G/qn6GwZ6BWEEuQVBBIIFiALQ9777+v83AT7/m/yB/cH8oAF2+zj7RQUh+1P8FQdv9z34xQGV/an6RPy6+Uz4Hfpp+9/7sPuH/lkCyQBUBzkDdgFS/tX6HQCL+h/7zfoR/CD6wAOH+if4xQCd+mP4FP/L+3sApgFnAvUEIgLO/vYFiAOqBPwGmQSO/Yz8CvrHAP4C4AXX+14DxQD2+xP5ifzg/JEAVwKS+/n9cgJL+3r49AFy/O/7jfo8+uH5N/z1/UoDIgACB4YCXgNJ+6D4Fvyy//f/YP0UBPcCUACo/XME+wM5ANAHjgW//IYFYQRO+14G7AQz/owF3AQBBs8D/QDl/kQBmf5XAj3/agBMAj4Atf0L/YUCzQAzALEE9gJH+6ECev6Q/S0GrQNp//8D3wIECxUFsgLS/gwCnf98/Mz5W/s8BPsA7gDeBwAH8gPdBGEGzQY1+/z5SPuT/cgBk/8oAL//FAPb/EX9uvq5/YgDQAPf/BQCUgK0B4AFRwVn+6j4vvf7/DgCtAMJ/DcE7ASp++IBfv/yBhQEDAMt+vUAkAPE/ov7a/zo/V761vbv/Pb5PPgt+AEBjQPH+If9R/v++YL44vtGBr8IKAbJ/ST6+PkB+8MAWQXP/CUBwwTz/XYCrQRe+Yf20PjR/tsC6wJv+wX/DQFK/LX/lAZi+zv+7QPB+UkCegHl98X9Vf7v/Xn7bfvl/jP/zP3k/N4EzQLlCJ8DGwI6/i8CuAEz/bQFTwb6+xwESgZv+3b/TwPiACcAnP0d+5b/J/11/cgDFgU2/YT9vwZhAS0EmgHi/GT/SwTC/ZYDvgHi+vQD0gGABTcG/QKVBWQF3QadB6QHSgkqBv8HZAiUA2IA+f9lB4oFXQhcAX0FKwaYAG4ECQf1/poAcwVy+9UBHgSK+2v+z/vD+ij30/mU/mUDqgZ2+yUCDgY3+3T4fvkz+kf4+fdB+Xb8nv+X+OkA1AV8A2wAtf5A+W77XPnc+7wH/gbS+zP5i/j1/wMGJQXA+V4A8QGlAbsBO//W+aP+Hvyp/eX9dAQ7/HX6f//D+q8AQf6J/h8FBwiN/nb9zf7P+10CbQH394//rgGMApIDxwMM/M757fbhBeAELwIGARr+b/5B+T4FCgYyADgAsAG2/ncApP0T+sj71vyyA2cEHwSI+ln5UPkK+0D92/onAx0HdwTICkYGewhgBJkBav9t/5QBvwOHCXcHjQh2/HP/nvx4/HL/Nv7T/Tz9cfnC/Gn60fnO+L76wvu8+fD+kwNU+wQDIASC/S77l/gM/q//yvz3+rP5If2L/xEDqAC++20EmgO4+xb9aAUfAd392fug+mf3TPuA9975I/kZ+j/3/fw3+UsEPgPM+dQC2wT6+bUCJQNm+RD/gQZc+fz/ZP1lCDADEgTO/M/4FgAE/QX+wQGB+5f9IQG1/MT5EP/yARb/b/0Q/eABjgbB/h/+wQDV/KsAif+w9zb/sPy1BnQEpAZDAO//QgD0/2b5u/2Y//D+zPte+8v4iP6M+2L8XP7G//3+cP2S+9f9/AbLBMcBgQDOAbkBEP/w/UgDAgKCALX/WgRvApkA2AAsBDQC+v/s/l8E1wK+ALz/wAIyAXcA+P5JAT0AnP+cAGwBewC3ADD/Vf+F/9wAv//O/sL/kgERAGz9Vvz2/gAAgQHrABQBHQFAAQwBsP44/yz9EQCs/30B4P2tAe4BBwKL/yABMAFJAYMCnQC9AvwBsARxAhwDYALmA6UB7AF4ArQEDAO+AYQA6QRMAyMCwQE9A5ICHQLWAWwE6gSWA38CIwIzAKcBFwEJAIgBUwBeAB4CHwLlAG3/Ov+BAMIAR/+h/Nf60v4eAKv9i/3t/3IAfPzH+9IBYQE1+tv5DwBx/1T59fcR/cQArPjt9379/v7h9kL6Pv7M/bj9Rv+Y/GL+8/gk/Jv74voq/Zf7HACpAPH7oPwy/Qr/5AEkABf/z/8JAUMAIQHTAbIAHwIqAxwCVAAaAaACvwIx/Pf8gQAfAVb8Hf+7A1MCr/nf/kQDiQLJ+Cn/UgTOAg74w/uKArgBy/fv/YsDPwIl+LD+ngLMAKv2TP1oAYkAlPdk9/f/GAH2/ncAJwDBAE4Axf+I/+IAMfym/PP8ufuV/WP+Pf62/ED71/rT/I38oPqx+Xb+VP4f/af72/3q/JD8Mfw5/H79J/yK/Af7r/on/tz8h/qB+qAA9/5p/O3+CP8G/zL9hP5hAJn/if4b/2H+P/95/9X9dAJpAXcA2ABDAmwBhwHRAHoCCgJm/2z/DgKFAaoAIQBpAAsBQAB8ASH6GPzi/4/+0vs6AIcC3wAQ+t3+bQIzAQX6nAD6ApQBE/iNAN4C8wHH+Hb9MgEAAkX/mP/F/7YBhgAa/5wARv/D//z+8P8KAMf9/f9b/tf+Q/n3/ab+sgCu+sX/1P+dAJ73M/4q/qP+hvf//4/+h//V+Yv+fPwJ/KP7sf2b/fD8z/16ALX/P/78/1X/hf+M/sAAqAC0/3z//ACV/1QB0gCIAf0BEAG1AJP/kQDaAHcAYP75/uUBCQFL//j/4v7iAAz/Jv9FAN7+Yv+/AP//wP+w/ab/1QCg//8AswGyALD/j/7u/9//sP/W/58BjAAi/3cEiwKJAkkB/wIsAvkA6wC0A50BugEXAY0AUwFkAS0CKv5W/2MA7QDH/eD8YAE1AhoB2QHWAUwBOf9O/fz6a/w//1b+4Pye++b/jf4W/j//fQJTAgcCSgGYAY3/TwAMAN0BVwCZ/4j+Zv2l/uv+3f4C/h/+qQApAcP8Hv0z/1X/wP7k/UgBGwGl/EL8ugH+/ywCrgKCADgAZwX0A/MCqwFkAuUCdAIpAq3+5PyGABUBh/3D+/7/Cv+Q/PX7ufnY+x78Jv6O+xj+Yv7l/EkAMv/4/3X/twHMAFD/vv0XAIMA8/4L/UH/9QCT/67+cAA8AXgAav6K/2MCTP9G/3v9cwBT/yIA+v0X/mn/PQC5/bT83AB2/1f9BPyHAe//qv2//p0A2f6BAJsAYvyV/CX8HQHxAK3/g/+D/2wCVQKwAVwAiQFOAJkBPQB3/h39Y/4U/VMANgBpARsAxPuCAK/+Sv3j+SkBPgHt/6/4JAAzAMP+7fYK/+cArP/+9vH8KABN/0f3Xvwm/jL+Dv2q/+D+jv2V9jn+wf+rALX7uvvm/3H/V/s5/mr+g/6j/S7/oP/N/7z9JP7s/vH/VfuG/U37j/yG/iP/Y/1I/FICsgBt/lz9+wJHAVkCIgGsACwBywCdAMj/sP5kARgAHP/Y/v3+4/9G/wcBoAEOAJ/+dQH0/yj/AQFgAK4AOQAK+pj9RvwN/g/+aP+z/n0AaQDIALMAn/+1/iD//QK5AvgCAAEtATsAxwGr/8wAIAH+/fAA+wCT/wABoQHe/2P+ZQCuAYABnADh//b/zgCqAU0CkQCPAEcAKAOKA00BXQHaA6oDTQJLARQFOAO7AP0BJgSNAnsB0gG2BakDkQESARMDXQMJAQIAYQIpAhwAMQGeA1QBagCCAfEA9f5t/+EATv/q/VsB9gF9/YP+jQEeAHX9I/1N/o4Baf4q/Rz+CP/r/G78Sv4k/g7+ev5LANn+PPyy/aL9lgCH/8//Zf+y/6cDJgKFASYAv/5/AKgBOwHj/o//GwEDAZICywBCAeYBhwP5AewCoQFjAqcBKwIAAu8Arf++/e3/rf4l/V0BDQBa/In6jv+Y/msAtAK2AFoAJfv++fH5K/3I/Xn8rfrz/fv9q/yj/BT8WP9O/b38PwB3/9T93f2QAOL+z/zlAT8Bbf9o/g4C9gCl/k7+KQHk/97+Kf6q+/v6NP6Z/iT85vxDBRMFsgILAsAELARGBPUCOQN0BPAC7gHkBFUFqwSCAwkCHQQUArABsv4o/8f++f5g/zQAKP5l/38AiACE/iwAUwOaAV7/F/57AAH/5Pxl/UIElQMVA+0BdQWtBC4CygDN/4r/qv5D/VMAbADW/0f+PQBfAB8BAAHl/1kADAITAl8B4wBQAiECuQKbAFz/MwF+AhIBF/7O//IC8ABa/4T/jP+9/UT7wf++ANn+8Pvw+pMAiP5P/4//SQPZBBsEnAICACUBJwIwAbj7R/wI/3gBEv07/FcABALt/vz9sQKHAYX+ff1sA1ICev4L/Hv9PQKV/8j9T/3G/P/75f+4/jX/XQPtAiQC6QCE+e37wwFsAGz9lP2uAewAFQB0/nr7if1cBRIFxwNBAmUEZQRNAsYBvf6f/C3+mQAs/kX93PwD/nj+Mv00/93/pf27+8n9Xv8H/hT8mv9eAdsA4ACnAfwAiwFPAmACawEW/aD/dQGsAKsAJwHKAlMB6QBNAGsAFQGdAJkADf6c/gsGMQRAAu4B3P6t/gj+sP15/Lj/lf0f/sb5o/vJ/QL/5/wF/sz93f4U/uz99gEwAoL+qwFYAuYAHf/dAfsASwAdAUoDLQPcAeL6y/q6AHkBtf1r/IMCfQFe+9f9mwFSAJn7zPxS/+T9pP13AB8CzQCE/pECjQM3AnAA1v6K/nIAp/wF/zgAnwCRAVkB3v90/5H/of0pAGYCYwGO/7P/2gFCAjgAqgWcA0oEjAXlApAB9gAWAEwCOQGH/0cBPwPYAY77oP1YAygCJ/vQ+34CWAKa/v4As/7R/nr94wJmAUoAygSHBsUEUQPBCFgG1wN8AjEHxQXtAoABXgEHAVcAgACS+FP8cP/A/zr5Af8gAeH/rPUq+24B6wCV+bz7wPrQ/uP9zfuk+3sAZvsE/9D8TP8I/BUCNQLpABn+Uv5E/04BYwPUBBYCqwDK+bn5ewJ2Amz3NgGcA5wByva4/rwDEQKx9Yf+dgIWAdb1PQEfAysBmvaFAFQBHwCU90T6gwB9AGD7Fv51/y4AGP2/AXsDNAJDAD3+hgJcAtf9U/6U/IwBogC+/xkFkwPfAUMCQAQaA8IBFgE2AkQB3/tm/5QAT/8P9qgALgRQAtH21v8zA1kB2PY9/dEC+gCB9yf6y/56ALL/t/94AK0A/P8GAfn++/5R/sD/a/4k/c/1dACt/z//C/pQ/CP+K/0E/jMBqgCsAEADoQFAA64CH/+xAH4DMgMe/nv+/wQPBIH+yQCi/igA2gJ7AuIADgL3Ac4BUgGOARcCygIoAOb+ygW/BT0E2wIZBjAEjQO1AosFAgV5A28DgQTYAqIESAMe/2oEtQOxAhL+Jvzs+YD/yP0v/Tf95v89AQwCRgCHAGv+n/wa+XT9Uv/f/HUDBQNn/6X/SwTvAgb+g/tVA4YC8QAOAwcCGwI9B6QGTQSsAh/7Mvow/rwAg/x/+uD6q/55/7YBUf8SAFcE0AM/AdAAavoY+VMAygHu/Rj8MwGBALj88f/j/5z+cfxk/IX7Dv+J//D9fwB7/wf9A/2+AK3/xf5/AwoC5wAi/2YAwf9U/jwBuwJ7AUYAGQDMAjoBlP/7AWoDNgLuAGwArQMHAsMAqQHE/1X+AQGLAJn/iv2+AU4BcgGcATAAVP9O/eX+LQK7AOL+ngDjAYwADgGo/on9nANDAoz/hACOANIBvP/A/+YAb//S/uL93fxw/PoD4QL7/PcDdgIpAdz1XwC9AVABtvfrAf4BvwAb+6EA/v+8/lb6h/04/Zf/tvwC/2X+ZACA/d7+8gP7Anb9OQGRBNoCjAD5AQYE/gIEAx8BKwQ3A+8B7QIxAUMBXP/OAU4AjwGq/pb8RQBVAvD/bAJtAlEBdv9E/vf+2gBUAD7+uQOaAiL/3fwdAlwCZ/yg+vQAdAC3/C39dgJMAowAlwImAXABpwMWBHED9wLSBrgFlAN0ArQBwwMZAQEAif9KAB4C1QABAMn9LQHxAAQBswHeAIwBqAO9A1QEvwL+AfoBKAPeAVkCtgLAA2wCzAPlAtQDWAJCA80C/wKsAoMCzAOnA34C9QGVAtACUwOX/4j90f6L/1P+ggAVA7oB9v1E/8ACdQEJ/SoALgMLAu39j/t1AUICVv1N+zn+HQF1+9f5tvssAFEArv/IAmsB3QH2ALoDbgJEBlYG/QR7A4EFWwOcA3wDBgMRBLMDdgQoAN79tf8gAZj9lv9H/eb/V/9g/4X8Hf3p/oD++/ui/vUGHAUWBDADLAb9BcAFmgThBTQEwASDA3oDiAM0Ao4CmAO0Av0DWAMT/oQAsQD5AUcAwwDk/2EAyAFfAVz/WAC3ARYB2P9eAXMFtQPqAKH/2/wo/iYAXf9vAZ7/6QELAgEEmgS8BIoDPwEiBS4DzQGF/+H93Py/ART9vP5//Jn7H/0L/uv8N/3LAgkD1wSaA50HkwdYBWED2gJwA/YChAGZ/MYBEQAF/4P+1vzR+e8A9/06/Oj8AwGG/LL6/vy3/6YAXQEs/0MBuPzT/tkBswFZ/TD+2AJfAWT/Of+bArABHQAE/58B4AEl/YX+kQAvAvD9if16+2H/vQERAXsAfwJ1AYL/IAM4AlQAXv/QAsgCwvzo/Uf/3gCYAcQB9QEDA3/8tfq9/7oB6PyC+jYCWgKnALr+/QFKAaH/jv0m/aj+hAbBBAsDxwEkBTwDSALPApQB4f/1AxUDWQBrAHsDJQJnAy0GlQOfAmIDxwUJBVYDhwEsBGIELANOAM79WQEzAq0Bmf+hARMDhv9L/psBFANv/F/+WgLyAh7/8P+XAPgCRP12AJj/8v+Y+zAAHAGJAXr+Xf6B/Yz/cvwyATwB8/+3BNgDNQOdAlX7S/2MACv/jPxg/jn/0v2Y/fUAbP5o/QYBOACX/Sz9q/8V/sD+cP3G/cH8f//w/R76wf+R/n/+mv6//gQAMwCS/ir/PwH/AZIAnwLv/93+kv/QAXX/EP42/9wAyP6J/Wz9t/9x/cz8av1z/fj6p/xS/kf8Qfz4/s//LP64/4P+ov7N/T//af43AGj+3fwLAMv+iQK8ADr/AP7NAbH/Nv7a+vn+ev/1/Yf5Tf4R/gP9OQCl/mL+sgGL+w7/aP2n/CIAvP89/a7+owGjAOL/+v45/uv8avov/e4DmAINARkAawIEAbcAYAA4/LL6fP6HAEv9QwPIAZoAUQS/AjkCawFZBi4F2QPsAiT/2wBMADD/UfqC+TEAlQA8/FT/EP2w/nEC0QAG/77/B/y6/P7/PQGI9zP6dv97AKz4zff3/VUAOfyS/kn9j/xn/A38Bf1L/IT/AP+g/mz9sgDPAWIBMAEw+bH95v5PADf3rwBjA/MBdv9M/0v/6/8N9yf7NP74/f38wwH2/8z+DwG//wQA1gDp/k3+1f+k/mL9IwC//y3/JgMXAlUAKQE5AO8A0gLtAeEAlQJIAyMC5P2I/g4AXQHVAdECSwGiAOD9EP3C//b/jgGo/9QCvQLt/+v9ov9ZAogAuf9X/RX9Wv+o/gUBzv+hAMz/5QFRAXX5MgC+AKP/FvcZ/z7/rv+h/639Zv+AAH4D9QFMAskBp/7OAHoAbgCJ/R3/x/0DAJgB7wCNAeIAO//+/4AA6wEBBYgDJAHXABoCMgEDAf0BW/3p+w0AQQFZ/bT9mv4s/9L98wCGAt8B5gFWAXoCFAJrACIDSwGIAJD/cv75++L+uv4//SABEAETBXgEmgRcA1n+eQB//mz/2f7S/r78zfwQAOj/N/8k/isCWwAL/yYB2v+F/j78O/tZ+xL67/51/nr+C/x7/T0Czfj6+/kB1AHoArMDYQLtAU/9bPuM/Xn//vvDAJsBxAAuBnsEPwJRASn79/x4/XL/UwI5A8cD3wJK+zb8r/+q/hf9DQC4/3cBxgETAH8FmQOR+VT/XQMyAqADAQYnBOQCWPZI/AgBUgAK/jv8yvp7AGMD1AQWAqsA8PY0/gwDawFa+5f9/ADD/1L/IgDzAxQD4/b3AKcBmQDw/53+BgHBAdj5z/vg/Y3+mf3P/hsEJQOvAhACBgBK/6cDawPqAykDxwABAX4ATAC4/Y77VwIsAq/7kfrJ+fn9L/zT+mwAWwEu/TH8bQFlAG//qQL5AGf/AACy/sb9nwCcAR0BsP6X/Uf8Ovx3A7ECHftUAEf/cv6w/bEBFASNAlUASQHY/2kBT/4//dIBPgJm/44CUAIiAVn/SABdAa8AogIpAdED0ALTBLQE9QLoAXD+8/4aAnQBuvqV+lb7pf/uBGwDBwO8Aqn92v9S/i79uAdeBt8DYAJvABQBHv+g/038fP71//n/0f7t/bn8UgHGBq4GfQX1A/z9qfx7/dIAUP1g/gEC5gDK/IP9hvvA/kj8bv1K/bcAjv+R/TIDogJB/zT/2wJ7AjMAxQRzA0ACRvxR/joDVgKq/g39fPxp/pr7nv4y/wD+3f3W/Jv+lP1CAAMCt/9m/pj8T/xc+jX7vwDv/+D9Gf/8+eD9e/yK/Ib88vkT9s38NvyvAMb8LvsG/an96v96AtX8Jfx6/L3+ywAaAIH+Ff/z/HH+7gSKA1z8ifqc+pD9XQBXAGL+5f1N/SUAeQL+AX3+JP7O+o8BQgAHAWn+z/+x/l/+EQRJB/X8v/tg+hb9pvxAA7v9Cf2N/l/9Bv4oCND9hv1l+w8B8QAOAEr+DP9z/s8BsgQXBKL94/xi+s3/vf7hASD/uP3x/e4BcQOqAk/+zv4W/CoCkwLeAKsAYP+f/qkCBgcdBqz8Y/th+bD+Zv1yALv9DP0Y/T3/dwHxAS/+3Py2+5oAGgF9/87/Qf8x/UMB3AIGBi79zfyE+mkABv+5AE7/Cv4a/UEB/gFXBL3+yf06/H8A5AFSAWD/NACu/twCVwUSBo793vxg+eL+tv2kAkn9qf6O/hb+JwFlB4r9wv0K/LD/hQK7//r/wv6U/g4DqgUOBMf+I/2N+ngAPADdAfj+t/2F/8cC3QR5AqX/nf4I/AMD9gIFAf0AUQAm/qIDpwi4Btj8tft7+ND98vypANz8zvtE/479yf99BUr9NP1W+2z9YgFJAf79yf/h/W4BCQSeBG79Qfyz+sn/SP9dAKP94v5q/ZQBwQE7A+L+ov4R+3QCMgHjAPD/kwCR/boAmwNiCF79hvy6+UX+HP9TAY/+6vxn/ucAVgC9BUD+u/3b+1ICwgFP/4T/Vv9B/p8ChwR8BST+Zf0Z+rP/dv/MAk//jP6D/sMBpgOTAwb/UP7K/BABPAO+ARoAEwDh/7oCnAZ4CHr9L/x8+E3/J/4MAYX+F/1N/QsAfwAJBBj+a/zH/D0AQwGHAJMAb/9S/a0CEgOSBgb+sPzv+iMAWgDeAOn/pv5i/ccBTwIHBTX/r/3C+4wCYAG1AScAPwA3/kkD8QQ5CPj9jvzQ+bj+Of2NBaz9qv66/9EArQCIB1n+qv1n/KUBXQLa//7/C/+B/4ADsQdvBIX++v3V+a0AdgDxAsn/g/7M/9kD/QPxAv7/3f6F/PEC4AOnAQgBgwA8/38D4gjvCYX9wPs99u/9KvwOAgT9wvzc/Uz+PAFXAl39VPwW/cf/7AD1/zf/r//i/BAATQMWBh/9J/xE+yz+EQAiAen+uP1E/bcANgPBAvf+FP5d+6UBmADUAXr+pgD0/icADgZMB4X9Ovzd+Tv+FP6OA+T+/fu1/0v/e/88B0P+kP1q+6QBbwHP/3v+LP9X/8MCMQS4BOX9Ov1X+lMAXf/kART/4f2d/lIBlwQuAwr/y/5C/F4C+AI8AKYA+P9d/87+OQcDChX9//sJ+V3+Qv7RADL9yv3q/Qb+tQJZAzH+R/3G+0UBrwEy//H/+P8F/SEClwPuBZ398fzf+gAByf/QAFv/pP5q/UEBqAKiA7r+U/5J/OQBvgE6Ajv/SAC3/40DrwXNBs39H/1K+IT/YP7OAiL+bP7G/vD/vgFkBtn95/0S/cb/fgLWADcAR//x/nwEFQW8BB3+Yf0Q+3UAHQEfAjT/ef6R/wECAgZWA47/Qv8u/G0DUwLQAQQBBAHJ/uwC6wioCPv9T/yF97r+PP16AdT8Tf0Y/9D9rwKBBST9Tv28/Jn+hQKCAff+PgBa/ZEAbAa4BNX9JPwv+7L/DgByALn+mv4X/ogBpQK5Ajf/FP+M+7UCwQGyAA3/AAFP/mMCaQWYCZz9e/xI+s3+7//zAcX+Zf0C/wABrAG3BRr+Wv7g+48CcgESAJr/R//s/vMCKgY3BRj+pf12+rYAo/9mA7f/Nv6k/kMDXgO9A+b+s/4W/SMCRwOsAREBp/8NAKwDrAYQCl7+xPsi+dT/mv4DAQ/+ff3Q/WMALQLBA1v+Av1r/CcBRgG4AK8ADwCO/RQCbgO9B0X+AP0F+90AnAAMAScAlf4H/rcCBAN0BF7/Nf5w/MUCvAGSAhkALwHI/vQEggWzBtf+Av3U+Pn+lP8uBGr+8/9//zkAtgGuCor+Gf69/DABuAKkAGgAFf8FAEsGbAd3BRv/uv3T+pUBwAAxA6n/Sv5vAAQErwThA0QAUf9a/AkEXQTDAd4ByAAI/08IiAr6B7381fqt9+H8TP3SAdT8+Psr/pL9IAGYA0P9t/zS+2X+AAIIAHr+pf8Y/eL/EwSJBMr8hPt8+9r+0v9uAGX+iv5a/dYAEwKcAmr+XP5W++cB6AAvAcL+WwAo/nsA0ASNCS79SPwp+h79Xf00BeH9ov0t/6H/nv/kBdv9/v1X+9kB0wBJAOD+kP97/hkCNAXqBMn9Df33+UcA5f54Alb/H/4T/qkC6gMxA5z+tf6T/KMBwgJaAfEA3v+6/nkBngdbByn9zfun+Rf/4f10APT92vy3/bf/3gHZAuD+Y/yJ+60AvwHM/0QAG/+i/cEBEQIFB7H9lfyt+rcAcP9EAZn/PP5m/W8C6AGYBBL/Af4U/EYBKALKAYgAbADB/nICPwVbBxb+ev0++Ub/P/7YAx79tP9W/9r98wIAChD+Av5N/NIAtgLM/1QAvv45/0IEWQbIBIj+pf2M+lcBSgB4AlH/Cv7g/8wDNAXeAjQA2f6n+ykElgOgAIkBawBz/r4EWQrNBoj9T/ud+DH96/5hAXX9kPyG/y3/0QA6Bc79Nv3d+zD/hAGfAMD+w//Z/SUBRASjBXj9n/wb+8//cf8xAW/+Hf+3/TECFAKfA4v/Rf5c+/sBtAEkAbH/6QA2/p8CAQRcCYf9tvwL+sz+4v6AAov+k/1p/qIB/QAZBcX+u/2P+zwCrQLn/j0AvP+N/t8DTQTaBRP+Vf2u+tH/pADAAgD/xv70/ncCtQMcBIr/pP6//EQAnAQ4ApgAdQAiAFkEbge/CKf9QfxW+XH/F/7gAbT+cf3C/TYAYQGoBDL+dP3k/JYAJQJwAMMAkf/9/acCVARvBtL9E/0/+/f/EAFVAcv/9/7p/ekBSwMSBYj/Hv74+3gCHwKYAbMAMgHy/WQEuAXECF/+7vzm+SD/gP5UBYf+Nf7n/4EB6QF+CLT+df3D/CACKQI9ABYAj/+n/2gEvQb0BSj/i/6L+aEAPAGMA1v/Iv+9/1IFlwQVA0kABP8B/eICpANoAmoB9gCC/xMDXgrTC039rvty98v9tP0MArL9LfwW/vn+jQHWA7/9u/xP/Or/swHP/0L/iv+L/aj/2ATpBYT95fsF/N3+vQADAf/+Kv6L/ZEAsQN+A7r+lP66+x8CBAF2Ajb/vQAv/2UBYwUrCMf9zftX+jb9Ef+XA1z+P/2s/5P/jv9nCWP+7/1n++IBcAGDAEb/uP99/10D5wTEBJ39bv3D+uMAh/93AlD/F/4m/+kClwS9A7/+bP9Y/J8CxgPYAFQB/f9x/9UBOAeFCSf9P/xt+Sv/Av9BAQH+Sv73/YL/1QKHA6z+U/34+zwB4AEUABcAp//Z/WEBGwT9BuD9C/2s+ioB5/+0AZz/eP75/dMB8gI2BC7/cv7K+2wCkgJ2Ag=="),"i8",B,n.GLOBAL_BASE+10240),a(r.b64Dec("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"),"i8",B,n.GLOBAL_BASE+20480),a(r.b64Dec("anwhe6d5/Hcidhh033F6b+dsKWpBZy9k9WCVXQ9aZVaZUqtOnkp0Ri1CzD1SOcE0GzBiK5cmvSHVHOIX5hLiDdgIywM9CkAKSQpYCmwKhwqnCs0K+QorC2MLoAvjCywMegzPDCgNiA3tDVcOxw48D7cPNxC9EEcR1xFsEgYTpRNJFPIUnxVSFgkXxBeFGEkZEhrgGrEbhxxhHT4eIB8FIO4g2yHLIr8jtiSwJa4mriexKLgpwSrMK9os6y3+LhMwKjFDMl4zezSaNbo22zf+OCI6RzttPJQ9vD7kPw1BNkJgQ4pEtEXdRgdIMElZSoJLqUzQTfZOG1A/UWJShFOkVMJV31b6VxNZK1pAW1NcY11xXn1fhmCMYY9ikGONZIdlfmZyZ2JoT2k4ah5r/2vdbLdtjG5ebytw9HC5cXlyNXPsc550THX1dZl2N3fRd2Z49niBeQZ6hnoBe3Z75ntRfLZ8FX1vfcN9EX5afp1+234Sf0R/cH+Wf7d/0X/mf/R//X//f/9/9H/Qf5V/Qn/XflV+vH0MfUV8aHt1emx5Tngcd9V1enQNc4xx+m9XbqJs3moLaShnOWU8YzNhHl//XNdapVhsVixU5VGaT0pN90qhSEpG80OcQUc/9DykOlg4EjbRM5gxZy8+LR8rCykCJwUlFSMzIV8fmx3nG0MasRgxF8MVaRQiE+8R0RDJD9YO+Q0yDYIM6AtmC/wKqQptCkkKPQo9Cj8KQwpKClQKYApvCoEKlgquCsgK5QoFCycLTQt1C58LzQv9CzAMZQydDNgMFg1WDZkN3g0mDnEOvg4ND2APtQ8MEGYQwhAhEYIR5hFMErQSHxOME/wTbhTiFFgV0RVMFskWSBfKF00Y0xhbGeUZcRr+Go4bIBy0HEod4R17HhYfsx9SIPIglSE5It4ihSMuJNgkhCUyJuAmkSdCKPUoqSlfKhYrziuHLEIt/S26LngvNjD2MLcxeDI7M/4zwjSHNU02EzfaN6E4ajkyOvw6xTuQPFo9JT7wPrw/iEBUQSBC7EK5Q4VEUkUeRutGt0eESFBJHErnSrNLfkxJTRNO3U6mT29QOFEAUsdSjlNUVBlV3VWhVmRXJljnWKdZZ1olW+JbnlxZXRNezF6DXzlg7mCiYVRiBWO1Y2NkD2W6ZWRmDGeyZ1do+mibaTtq2Wp1axBsqGw/bdNtZm73boZvE3CecCdxrnEycrVyNXOzcy90qXQhdZZ1CXZ6duh2VHe+dyV4injseEx5qnkFel56tHoHe1h7p3vyezx8gnzGfAh9R32Dfbx9830nfll+iH60ft1+BH8of0l/Z3+Df5x/sn/Ff9Z/5H/vf/d//X//f/9/YX2gdQ9pMFi1Q3QsYhNEZWNvZGVyAGVuY29kZXIA"),"i8",B,n.GLOBAL_BASE+30720);var G=n.alignMemory(a(12,"i8",l),8);function L(A){var e=L;e.called||(d=h(d),e.called=!0,w(n.dynamicAlloc),e.alloc=n.dynamicAlloc,n.dynamicAlloc=function(){Y("cannot dynamically allocate, sbrk now has control")});var f=d;return 0==A||e.alloc(A)?f:-1>>>0}function R(A){return r.___errno_location&&(u[r.___errno_location()>>2]=A),A}function T(A){if(30==A)return E;Y("fix _sysconf")}function y(A,e,f){return Q.set(Q.subarray(e,e+f),A),A}function Y(A){throw new Error("abort("+A+")")}function z(A){r.abort(A)}function X(A,e){}function J(A,e,f,i,r){}function O(A){var e=Date.now()/1e3|0;return A&&(u[A>>2]=e),e}function m(){return 0}w(G%8==0),r._memcpy=S,r._memmove=K,r._memset=x,r.abort=Y,b=(k=n.alignMemory(P))+F,w((d=n.alignMemory(b))<H,"TOTAL_MEMORY not big enough for stack"),r.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0},r.asmLibraryArg={abort:Y,assert:w,_sysconf:T,_pthread_self:m,_abort:z,___setErrNo:R,_sbrk:L,_time:O,_emscripten_set_main_loop_timing:X,_emscripten_memcpy_big:y,_emscripten_set_main_loop:J,STACKTOP:k,STACK_MAX:b,tempDoublePtr:G,ABORT:t};var N=function(A,e,f){"use asm";var i=new A.Int8Array(f);var r=new A.Int16Array(f);var n=new A.Int32Array(f);var t=new A.Uint8Array(f);var w=new A.Uint16Array(f);var l=new A.Uint32Array(f);var B=new A.Float32Array(f);var a=new A.Float64Array(f);var s=e.STACKTOP|0;var o=e.STACK_MAX|0;var Q=e.tempDoublePtr|0;var g=e.ABORT|0;var v=0;var u=0;var c=0;var C=0;var D=A.NaN,E=A.Infinity;var h=0,P=0,k=0,b=0,d=0.0,I=0,M=0,F=0,H=0.0;var U=0;var G=0;var L=0;var R=0;var T=0;var y=0;var Y=0;var z=0;var X=0;var J=0;var O=A.Math.floor;var m=A.Math.abs;var N=A.Math.sqrt;var K=A.Math.pow;var x=A.Math.cos;var S=A.Math.sin;var j=A.Math.tan;var p=A.Math.acos;var W=A.Math.asin;var V=A.Math.atan;var Z=A.Math.atan2;var _=A.Math.exp;var q=A.Math.log;var $=A.Math.ceil;var AA=A.Math.imul;var eA=A.Math.min;var fA=A.Math.clz32;var iA=e.abort;var rA=e.assert;var nA=e._sysconf;var tA=e._pthread_self;var wA=e._abort;var lA=e._abort;var BA=e.___setErrNo;var aA=e._sbrk;var sA=e._time;var oA=e._emscripten_set_main_loop_timing;var QA=e._emscripten_memcpy_big;var gA=e._emscripten_set_main_loop;var vA=0.0;function uA(){var A=0,e=0;e=s;s=s+16|0;A=e;n[A>>2]=0;ue(A,31756)|0;s=e;return n[A>>2]|0}function cA(A){A=A|0;var e=0,f=0;e=s;s=s+16|0;f=e;n[f>>2]=A;ce(f);s=e;return}function CA(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;dA(A,(i|0)==0?(t[e>>0]|0)>>>3&15:15,e+1|0,f,2)|0;return}function DA(A){A=A|0;var e=0;e=lr(8)|0;Ee(e,e+4|0,A)|0;return e|0}function EA(A){A=A|0;he(A,A+4|0);Br(A);return}function hA(A,e,f,r,w){A=A|0;e=e|0;f=f|0;r=r|0;w=w|0;var l=0;w=s;s=s+16|0;l=w;n[l>>2]=e;f=(Pe(n[A>>2]|0,n[A+4>>2]|0,e,f,r,l,3)|0)<<16>>16;i[r>>0]=t[r>>0]|0|4;s=w;return f|0}function PA(A){A=A|0;if(!A)A=-1;else{r[A>>1]=4096;A=0}return A|0}function kA(A,e,f,i,t,w){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0;o=n[w>>2]|0;u=t<<16>>16>0;if(u){l=0;B=0;do{s=r[f+(l<<1)>>1]|0;s=AA(s,s)|0;if((s|0)!=1073741824){a=(s<<1)+B|0;if((s^B|0)>0&(a^B|0)<0){n[w>>2]=1;B=(B>>>31)+2147483647|0}else B=a}else{n[w>>2]=1;B=2147483647}l=l+1|0}while((l&65535)<<16>>16!=t<<16>>16);if((B|0)==2147483647){n[w>>2]=o;s=0;a=0;do{B=r[f+(s<<1)>>1]>>2;B=AA(B,B)|0;if((B|0)!=1073741824){l=(B<<1)+a|0;if((B^a|0)>0&(l^a|0)<0){n[w>>2]=1;a=(a>>>31)+2147483647|0}else a=l}else{n[w>>2]=1;a=2147483647}s=s+1|0}while((s&65535)<<16>>16!=t<<16>>16)}else v=8}else{B=0;v=8}if((v|0)==8)a=B>>4;if(!a){r[A>>1]=0;return}g=((Ni(a)|0)&65535)+65535|0;B=g<<16>>16;if((g&65535)<<16>>16>0){l=a<<B;if((l>>B|0)==(a|0))a=l;else a=a>>31^2147483647}else{B=0-B<<16;if((B|0)<2031616)a=a>>(B>>16);else a=0}Q=er(a,w)|0;l=n[w>>2]|0;if(u){B=0;a=0;do{o=r[e+(B<<1)>>1]|0;o=AA(o,o)|0;if((o|0)!=1073741824){s=(o<<1)+a|0;if((o^a|0)>0&(s^a|0)<0){n[w>>2]=1;a=(a>>>31)+2147483647|0}else a=s}else{n[w>>2]=1;a=2147483647}B=B+1|0}while((B&65535)<<16>>16!=t<<16>>16);if((a|0)==2147483647){n[w>>2]=l;o=0;a=0;do{s=r[e+(o<<1)>>1]>>2;s=AA(s,s)|0;if((s|0)!=1073741824){B=(s<<1)+a|0;if((s^a|0)>0&(B^a|0)<0){n[w>>2]=1;a=(a>>>31)+2147483647|0}else a=B}else{n[w>>2]=1;a=2147483647}o=o+1|0}while((o&65535)<<16>>16!=t<<16>>16)}else v=29}else{a=0;v=29}if((v|0)==29)a=a>>4;if(!a)s=0;else{B=(Ni(a)|0)<<16>>16;l=g-B|0;s=l&65535;a=(Ei(Q,er(a<<B,w)|0)|0)<<16>>16;B=a<<7;l=l<<16>>16;if(s<<16>>16>0)l=s<<16>>16<31?B>>l:0;else{v=0-l<<16>>16;l=B<<v;l=(l>>v|0)==(B|0)?l:a>>24^2147483647}s=(AA(((Ui(l,w)|0)<<9)+32768>>16,32767-(i&65535)<<16>>16)|0)>>>15<<16>>16}l=r[A>>1]|0;if(u){a=i<<16>>16;B=0;while(1){i=((AA(l<<16>>16,a)|0)>>>15&65535)+s|0;l=i&65535;r[f>>1]=(AA(r[f>>1]|0,i<<16>>16)|0)>>>12;B=B+1<<16>>16;if(B<<16>>16>=t<<16>>16)break;else f=f+2|0}}r[A>>1]=l;return}function bA(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0;l=n[i>>2]|0;t=f<<16>>16>0;if(t){B=0;w=0;do{s=r[e+(B<<1)>>1]|0;s=AA(s,s)|0;if((s|0)!=1073741824){a=(s<<1)+w|0;if((s^w|0)>0&(a^w|0)<0){n[i>>2]=1;w=(w>>>31)+2147483647|0}else w=a}else{n[i>>2]=1;w=2147483647}B=B+1|0}while((B&65535)<<16>>16!=f<<16>>16);if((w|0)==2147483647){n[i>>2]=l;s=0;l=0;do{a=r[e+(s<<1)>>1]>>2;a=AA(a,a)|0;if((a|0)!=1073741824){B=(a<<1)+l|0;if((a^l|0)>0&(B^l|0)<0){n[i>>2]=1;l=(l>>>31)+2147483647|0}else l=B}else{n[i>>2]=1;l=2147483647}s=s+1|0}while((s&65535)<<16>>16!=f<<16>>16)}else g=8}else{w=0;g=8}if((g|0)==8)l=w>>4;if(!l)return;Q=((Ni(l)|0)&65535)+65535|0;a=Q<<16>>16;if((Q&65535)<<16>>16>0){B=l<<a;if((B>>a|0)==(l|0))l=B;else l=l>>31^2147483647}else{a=0-a<<16;if((a|0)<2031616)l=l>>(a>>16);else l=0}o=er(l,i)|0;l=n[i>>2]|0;if(t){B=0;w=0;do{s=r[A+(B<<1)>>1]|0;s=AA(s,s)|0;if((s|0)!=1073741824){a=(s<<1)+w|0;if((s^w|0)>0&(a^w|0)<0){n[i>>2]=1;w=(w>>>31)+2147483647|0}else w=a}else{n[i>>2]=1;w=2147483647}B=B+1|0}while((B&65535)<<16>>16!=f<<16>>16);if((w|0)==2147483647){n[i>>2]=l;l=0;B=0;do{s=r[A+(l<<1)>>1]>>2;s=AA(s,s)|0;if((s|0)!=1073741824){a=(s<<1)+B|0;if((s^B|0)>0&(a^B|0)<0){n[i>>2]=1;B=(B>>>31)+2147483647|0}else B=a}else{n[i>>2]=1;B=2147483647}l=l+1|0}while((l&65535)<<16>>16!=f<<16>>16)}else g=28}else{w=0;g=28}if((g|0)==28)B=w>>4;if(!B)t=0;else{s=Ni(B)|0;a=s<<16>>16;if(s<<16>>16>0){l=B<<a;if((l>>a|0)==(B|0))B=l;else B=B>>31^2147483647}else{a=0-a<<16;if((a|0)<2031616)B=B>>(a>>16);else B=0}l=Q-(s&65535)|0;a=l&65535;w=(Ei(o,er(B,i)|0)|0)<<16>>16;t=w<<7;l=l<<16>>16;if(a<<16>>16>0)t=a<<16>>16<31?t>>l:0;else{Q=0-l<<16>>16;A=t<<Q;t=(A>>Q|0)==(t|0)?A:w>>24^2147483647}t=Ui(t,i)|0;if((t|0)>4194303)t=2147483647;else t=(t|0)<-4194304?-2147483648:t<<9;t=er(t,i)|0}w=(f&65535)+65535&65535;if(w<<16>>16<=-1)return;s=t<<16>>16;a=f+-1<<16>>16<<16>>16;while(1){l=e+(a<<1)|0;t=AA(r[l>>1]|0,s)|0;do{if((t|0)!=1073741824){B=t<<1;if((B|0)<=268435455)if((B|0)<-268435456){r[l>>1]=-32768;break}else{r[l>>1]=t>>>12;break}else g=52}else{n[i>>2]=1;g=52}}while(0);if((g|0)==52){g=0;r[l>>1]=32767}w=w+-1<<16>>16;if(w<<16>>16<=-1)break;else a=a+-1|0}return}function dA(A,e,f,i,t){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;var w=0,l=0,B=0,a=0;a=s;s=s+496|0;B=a;l=(t|0)==2;do{if(!(l&1|(t|0)==4)){lA()}else{w=A+1168|0;if(l){De(e,f,B,w);w=604}else{lA()}t=r[w+(e<<1)>>1]|0;do{if(e>>>0>=8){lA()}else w=0}while(0);if(t<<16>>16==-1){A=-1;s=a;return A|0}}}while(0);Ce(A,e,B,w,i);n[A+1760>>2]=e;A=t;s=a;return A|0}function IA(A){A=A|0;var e=0;if(!A){e=-1;return e|0}e=A+122|0;do{r[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function MA(A,e,f,i,t){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0;a=159;B=0;while(1){o=r[f+(a<<1)>>1]|0;o=AA(o,o)|0;o=(o|0)==1073741824?2147483647:o<<1;l=o+B|0;if((o^B|0)>-1&(l^B|0)<0){n[t>>2]=1;B=(B>>>31)+2147483647|0}else B=l;if((a|0)>0)a=a+-1|0;else{a=B;break}}t=a>>>14&65535;B=32767;l=59;while(1){o=r[A+(l<<1)>>1]|0;B=o<<16>>16<B<<16>>16?o:B;if((l|0)>0)l=l+-1|0;else break}o=(a|0)>536870911?32767:t;t=B<<16>>16;l=t<<20>>16;a=B<<16>>16>0?32767:-32768;f=55;B=r[A>>1]|0;while(1){s=r[A+(f<<1)>>1]|0;B=B<<16>>16<s<<16>>16?s:B;if((f|0)>1)f=f+-1|0;else break}f=r[A+80>>1]|0;s=r[A+82>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+84>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+86>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+88>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+90>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+92>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+94>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+96>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+98>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+100>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+102>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+104>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+106>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+108>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+110>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+112>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+114>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=r[A+116>>1]|0;f=f<<16>>16<s<<16>>16?s:f;s=A+118|0;g=r[s>>1]|0;do{if((o+-21&65535)<17557&B<<16>>16>20?(o<<16>>16|0)<(((t<<4|0)==(l|0)?l:a)|0)?1:(f<<16>>16<g<<16>>16?g:f)<<16>>16<1953:0){B=A+120|0;l=r[B>>1]|0;if(l<<16>>16>29){r[B>>1]=30;f=B;a=1;break}else{a=(l&65535)+1&65535;r[B>>1]=a;f=B;a=a<<16>>16>1&1;break}}else Q=14}while(0);if((Q|0)==14){f=A+120|0;r[f>>1]=0;a=0}B=0;do{g=B;B=B+1|0;r[A+(g<<1)>>1]=r[A+(B<<1)>>1]|0}while((B|0)!=59);r[s>>1]=o;B=r[f>>1]|0;B=B<<16>>16>15?16383:B<<16>>16>8?15565:13926;l=di(e+8|0,5)|0;if((r[f>>1]|0)>20){if(((di(e,9)|0)<<16>>16|0)>(B|0))Q=20}else if((l<<16>>16|0)>(B|0))Q=20;if((Q|0)==20){r[i>>1]=0;return a|0}l=(w[i>>1]|0)+1&65535;if(l<<16>>16>10){r[i>>1]=10;return a|0}else{r[i>>1]=l;return a|0}return 0}function FA(A){A=A|0;var e=0;if(!A){e=-1;return e|0}e=A+18|0;do{r[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function HA(A,e,f,i,t,l,B,a,s,o,Q,g){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;Q=Q|0;g=g|0;var v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0;b=A+2|0;r[A>>1]=r[b>>1]|0;d=A+4|0;r[b>>1]=r[d>>1]|0;I=A+6|0;r[d>>1]=r[I>>1]|0;M=A+8|0;r[I>>1]=r[M>>1]|0;F=A+10|0;r[M>>1]=r[F>>1]|0;H=A+12|0;r[F>>1]=r[H>>1]|0;r[H>>1]=f;D=0;k=0;do{v=t+(k<<1)|0;c=nr(r[v>>1]|0,r[i+(k<<1)>>1]|0,g)|0;c=(c&65535)-((c&65535)>>>15&65535)|0;c=c<<16>>31^c;P=((Ki(c&65535)|0)&65535)+65535|0;u=P<<16>>16;if((P&65535)<<16>>16<0){C=0-u<<16;if((C|0)<983040)E=c<<16>>16>>(C>>16)&65535;else E=0}else{C=c<<16>>16;c=C<<u;if((c<<16>>16>>u|0)==(C|0))E=c&65535;else E=(C>>>15^32767)&65535}h=Ki(r[v>>1]|0)|0;c=r[v>>1]|0;u=h<<16>>16;if(h<<16>>16<0){lA()}else{C=c<<16>>16;c=C<<u;if((c<<16>>16>>u|0)==(C|0))C=c&65535;else C=(C>>>15^32767)&65535}u=Ei(E,C)|0;C=(P&65535)+2-(h&65535)|0;c=C&65535;do{if(C&32768){if(c<<16>>16!=-32768){P=0-C|0;C=P<<16>>16;if((P&65535)<<16>>16<0){lA()}}else C=32767;c=u<<16>>16;u=c<<C;if((u<<16>>16>>C|0)==(c|0))C=u&65535;else C=(c>>>15^32767)&65535}else C=fr(u,c,g)|0}while(0);D=Ci(D,C,g)|0;k=k+1|0}while((k|0)!=10);C=D&65535;c=D<<16>>16>5325;D=A+14|0;if(c){t=(w[D>>1]|0)+1&65535;r[D>>1]=t;if(t<<16>>16>10)r[A+16>>1]=0}else r[D>>1]=0;switch(e|0){case 0:case 1:case 2:case 3:case 6:break;default:{H=A+16|0;g=f;f=r[H>>1]|0;f=f&65535;f=f+1|0;f=f&65535;r[H>>1]=f;return g|0}}E=(B|l)<<16>>16==0;h=o<<16>>16==0;P=e>>>0<3;D=C+(P&((h|(E&(a<<16>>16==0|s<<16>>16==0)|Q<<16>>16<2))^1)?61030:62259)&65535;D=D<<16>>16>0?D:0;if(D<<16>>16<=2048){D=D<<16>>16;if((D<<18>>18|0)==(D|0))s=D<<2;else s=D>>>15^32767}else s=8192;a=A+16|0;Q=c|(r[a>>1]|0)<40;D=r[d>>1]|0;if((D*6554|0)==1073741824){n[g>>2]=1;c=2147483647}else c=D*13108|0;D=r[I>>1]|0;C=D*6554|0;if((C|0)!=1073741824){D=(D*13108|0)+c|0;if((C^c|0)>0&(D^c|0)<0){n[g>>2]=1;D=(c>>>31)+2147483647|0}}else{n[g>>2]=1;D=2147483647}C=r[M>>1]|0;c=C*6554|0;if((c|0)!=1073741824){C=(C*13108|0)+D|0;if((c^D|0)>0&(C^D|0)<0){n[g>>2]=1;C=(D>>>31)+2147483647|0}}else{n[g>>2]=1;C=2147483647}D=r[F>>1]|0;c=D*6554|0;if((c|0)!=1073741824){D=(D*13108|0)+C|0;if((c^C|0)>0&(D^C|0)<0){n[g>>2]=1;c=(C>>>31)+2147483647|0}else c=D}else{n[g>>2]=1;c=2147483647}D=r[H>>1]|0;C=D*6554|0;if((C|0)!=1073741824){D=(D*13108|0)+c|0;if((C^c|0)>0&(D^c|0)<0){n[g>>2]=1;D=(c>>>31)+2147483647|0}}else{n[g>>2]=1;D=2147483647}c=er(D,g)|0;if(P&((E|h)^1)){lA()}D=Q?8192:s<<16>>16;v=AA(D,f<<16>>16)|0;if((v|0)==1073741824){n[g>>2]=1;C=2147483647}else C=v<<1;c=c<<16>>16;u=c<<13;if((u|0)!=1073741824){v=C+(c<<14)|0;if((C^u|0)>0&(v^C|0)<0){n[g>>2]=1;C=(C>>>31)+2147483647|0}else C=v}else{n[g>>2]=1;C=2147483647}v=AA(c,D)|0;if((v|0)==1073741824){n[g>>2]=1;u=2147483647}else u=v<<1;v=C-u|0;if(((v^C)&(u^C)|0)<0){n[g>>2]=1;v=(C>>>31)+2147483647|0}H=v<<2;f=a;g=er((H>>2|0)==(v|0)?H:v>>31^2147483647,g)|0;H=r[f>>1]|0;H=H&65535;H=H+1|0;H=H&65535;r[f>>1]=H;return g|0}function UA(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,n=0,t=0,l=0;i=e;n=i+80|0;do{r[i>>1]=0;i=i+2|0}while((i|0)<(n|0));i=0;do{l=r[A+(i<<1)>>1]|0;n=((l&8)<<10&65535^8192)+-4096<<16>>16;t=i<<16;l=((r[f+((l&7)<<1)>>1]|0)*327680|0)+t>>16;r[e+(l<<1)>>1]=n;t=((r[f+((w[A+(i+5<<1)>>1]&7)<<1)>>1]|0)*327680|0)+t>>16;if((t|0)<(l|0))n=0-(n&65535)&65535;l=e+(t<<1)|0;r[l>>1]=(w[l>>1]|0)+(n&65535);i=i+1|0}while((i|0)!=5);return}function GA(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,n=0,t=0;n=e<<16>>16;i=(n<<1&2|1)+((n>>>1&7)*5|0)|0;e=n>>>4&3;e=((n>>>6&7)*5|0)+((e|0)==3?4:e)|0;n=f;t=n+80|0;do{r[n>>1]=0;n=n+2|0}while((n|0)<(t|0));A=A<<16>>16;r[f+(i<<1)>>1]=(0-(A&1)&16383)+57344;r[f+(e<<1)>>1]=(0-(A>>>1&1)&16383)+57344;return}function LA(A,e,f,i,n,t){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;var l=0,B=0;t=f<<16>>16;B=t>>>3;A=A<<16>>16;A=((A<<17>>17|0)==(A|0)?A<<1:A>>>15^32767)+(B&8)<<16;B=(w[i+(A+65536>>16<<1)>>1]|0)+((B&7)*5|0)|0;f=e<<16>>16;l=(0-(f&1)&16383)+57344&65535;A=n+((w[i+(A>>16<<1)>>1]|0)+((t&7)*5|0)<<16>>16<<1)|0;e=n;t=e+80|0;do{r[e>>1]=0;e=e+2|0}while((e|0)<(t|0));r[A>>1]=l;r[n+(B<<16>>16<<1)>>1]=(0-(f>>>1&1)&16383)+57344;return}function RA(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,n=0,t=0,w=0;e=e<<16>>16;i=(e&7)*5|0;n=(e>>>2&2|1)+((e>>>4&7)*5|0)|0;e=(e>>>6&2)+2+((e>>>8&7)*5|0)|0;t=f;w=t+80|0;do{r[t>>1]=0;t=t+2|0}while((t|0)<(w|0));A=A<<16>>16;r[f+(i<<1)>>1]=(0-(A&1)&16383)+57344;r[f+(n<<1)>>1]=(0-(A>>>1&1)&16383)+57344;r[f+(e<<1)>>1]=(0-(A>>>2&1)&16383)+57344;return}function TA(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0;e=e<<16>>16;w=r[f+((e&7)<<1)>>1]|0;l=r[f+((e>>>3&7)<<1)>>1]|0;t=r[f+((e>>>6&7)<<1)>>1]|0;f=(e>>>9&1)+3+((r[f+((e>>>10&7)<<1)>>1]|0)*5|0)|0;e=i;n=e+80|0;do{r[e>>1]=0;e=e+2|0}while((e|0)<(n|0));A=A<<16>>16;r[i+(w*327680>>16<<1)>>1]=(0-(A&1)&16383)+57344;r[i+((l*327680|0)+65536>>16<<1)>>1]=(0-(A>>>1&1)&16383)+57344;r[i+((t*327680|0)+131072>>16<<1)>>1]=(0-(A>>>2&1)&16383)+57344;r[i+(f<<16>>16<<1)>>1]=(0-(A>>>3&1)&16383)+57344;return}function yA(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,t=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0;u=s;s=s+32|0;v=u+16|0;g=u;l=e;t=l+80|0;do{r[l>>1]=0;l=l+2|0}while((l|0)<(t|0));t=r[A>>1]|0;r[v>>1]=t;r[v+2>>1]=r[A+2>>1]|0;r[v+4>>1]=r[A+4>>1]|0;r[v+6>>1]=r[A+6>>1]|0;o=r[A+8>>1]|0;YA(o>>>3&65535,o&7,0,4,1,g,f);o=r[A+10>>1]|0;YA(o>>>3&65535,o&7,2,6,5,g,f);o=r[A+12>>1]|0;i=o>>2;do{if((i*25|0)!=1073741824){l=(AA(i,1638400)|0)+786432>>21;i=l*6554>>15;if((i|0)>32767){lA()}A=(i<<16>>16)*5|0;B=i&1;if((A|0)==1073741824){n[f>>2]=1;a=0;A=65535}else{a=0;Q=6}}else{lA()}}while(0);if((Q|0)==6)A=A&65535;Q=l-A|0;B=B<<16>>16==0?Q:4-Q|0;Q=B<<16>>16;r[g+6>>1]=Ci(((B<<17>>17|0)==(Q|0)?B<<1:Q>>>15^32767)&65535,o&1,f)|0;if(a){n[f>>2]=1;i=32767}Q=i<<16>>16;r[g+14>>1]=((i<<17>>17|0)==(Q|0)?i<<1:Q>>>15^32767)+(o>>>1&1);i=0;while(1){t=t<<16>>16==0?8191:-8191;Q=(r[g+(i<<1)>>1]<<2)+i<<16;l=Q>>16;if((Q|0)<2621440)r[e+(l<<1)>>1]=t;B=(r[g+(i+4<<1)>>1]<<2)+i<<16;A=B>>16;if((A|0)<(l|0))t=0-(t&65535)&65535;if((B|0)<2621440){Q=e+(A<<1)|0;r[Q>>1]=(w[Q>>1]|0)+(t&65535)}i=i+1|0;if((i|0)==4)break;t=r[v+(i<<1)>>1]|0}s=u;return}function YA(A,e,f,i,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,s=0,o=0,Q=0,g=0,v=0;a=A<<16>>16>124?124:A;A=(a<<16>>16)*1311>>15;v=(A|0)>32767;if(!v){B=A<<16>>16;if((B*25|0)==1073741824){n[l>>2]=1;B=1073741823}else g=4}else{n[l>>2]=1;B=32767;g=4}if((g|0)==4)B=(B*50|0)>>>1;o=(a&65535)-B|0;B=(o<<16>>16)*6554>>15;Q=(B|0)>32767;if(!Q){a=B<<16>>16;if((a*5|0)==1073741824){n[l>>2]=1;s=1073741823}else g=9}else{n[l>>2]=1;a=32767;g=9}if((g|0)==9)s=(a*10|0)>>>1;o=o-s|0;g=o<<16>>16;a=e<<16>>16;s=a>>2;a=a-(s<<2)|0;r[w+(f<<16>>16<<1)>>1]=((o<<17>>17|0)==(g|0)?o<<1:g>>>15^32767)+(a&1);if(Q){n[l>>2]=1;B=32767}f=B<<16>>16;r[w+(i<<16>>16<<1)>>1]=((B<<17>>17|0)==(f|0)?B<<1:f>>>15^32767)+(a<<16>>17);if(v){n[l>>2]=1;A=32767}i=A<<16>>16;r[w+(t<<16>>16<<1)>>1]=Ci(s&65535,((A<<17>>17|0)==(i|0)?A<<1:i>>>15^32767)&65535,l)|0;return}function zA(A){A=A|0;var e=0,f=0,i=0,t=0;if(!A){t=-1;return t|0}bi(A+1168|0);r[A+460>>1]=40;n[A+1164>>2]=0;e=A+646|0;f=A+1216|0;i=A+462|0;t=i+22|0;do{r[i>>1]=0;i=i+2|0}while((i|0)<(t|0));pA(e,n[f>>2]|0)|0;ee(A+686|0)|0;_A(A+700|0)|0;FA(A+608|0)|0;re(A+626|0,n[f>>2]|0)|0;IA(A+484|0)|0;te(A+730|0)|0;WA(A+748|0)|0;hi(A+714|0)|0;XA(A,0)|0;t=0;return t|0}function XA(A,e){A=A|0;e=e|0;var f=0,i=0;if(!A){A=-1;return A|0}n[A+388>>2]=A+308;gr(A|0,0,308)|0;e=(e|0)!=8;if(e){f=A+412|0;i=f+20|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));r[A+392>>1]=3e4;r[A+394>>1]=26e3;r[A+396>>1]=21e3;r[A+398>>1]=15e3;r[A+400>>1]=8e3;r[A+402>>1]=0;r[A+404>>1]=-8e3;r[A+406>>1]=-15e3;r[A+408>>1]=-21e3;r[A+410>>1]=-26e3}r[A+432>>1]=0;r[A+434>>1]=40;n[A+1164>>2]=0;r[A+436>>1]=0;r[A+438>>1]=0;r[A+440>>1]=0;r[A+460>>1]=40;r[A+462>>1]=0;r[A+464>>1]=0;if(e){f=A+442|0;i=f+18|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));f=A+466|0;i=f+18|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));FA(A+608|0)|0;i=A+1216|0;re(A+626|0,n[i>>2]|0)|0;pA(A+646|0,n[i>>2]|0)|0;ee(A+686|0)|0;_A(A+700|0)|0;hi(A+714|0)|0}else{lA()}IA(A+484|0)|0;r[A+606>>1]=21845;te(A+730|0)|0;if(!e){A=0;return A|0}WA(A+748|0)|0;A=0;return A|0}function JA(A,e,f,t,w,l){A=A|0;e=e|0;f=f|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0,wA=0,BA=0,aA=0,sA=0,oA=0,QA=0,gA=0,vA=0,uA=0,cA=0,CA=0,DA=0,EA=0,hA=0,PA=0,kA=0,dA=0,IA=0,FA=0,YA=0,zA=0,XA=0,JA=0,pA=0,WA=0,_A=0,ee=0,re=0,te=0,ae=0,se=0,oe=0,Qe=0,ge=0,ve=0,ue=0,ce=0,Ce=0,De=0,Ee=0,he=0,Pe=0,ke=0;ke=s;s=s+336|0;u=ke+236|0;v=ke+216|0;he=ke+112|0;Ee=ke+12|0;ve=ke+256|0;ce=ke+136|0;ue=ke+32|0;Qe=ke+8|0;ge=ke+6|0;De=ke+4|0;Ce=ke+2|0;Pe=ke;re=A+1164|0;te=A+748|0;ae=ZA(te,t,re)|0;if(ae){lA()}switch(t|0){case 1:{B=1;P=6;break}case 2:case 7:{lA();break}case 3:{P=9;break}default:{B=0;P=6}}do{if((P|0)==6){t=A+440|0;if((r[t>>1]|0)==6){lA()}else{r[t>>1]=0;_A=0;ee=0;break}}else if((P|0)==9){lA()}}while(0);XA=A+1156|0;switch(n[XA>>2]|0){case 1:{r[t>>1]=5;r[A+436>>1]=0;break}case 2:{r[t>>1]=5;r[A+436>>1]=1;break}default:{}}o=A+646|0;JA=A+666|0;a=he;Q=JA;g=a+20|0;do{i[a>>0]=i[Q>>0]|0;a=a+1|0;Q=Q+1|0}while((a|0)<(g|0));pA=(e|0)!=7;WA=A+1168|0;if(pA){SA(o,e,ee,f,WA,u,re);a=A+392|0;Fi(a,u,l,re);f=f+6|0}else{jA(o,ee,f,WA,v,u,re);a=A+392|0;Ii(a,v,u,l,re);f=f+10|0}Q=u;g=a+20|0;do{r[a>>1]=r[Q>>1]|0;a=a+2|0;Q=Q+2|0}while((a|0)<(g|0));zA=e>>>0>1;I=e>>>0<4&1;YA=(e|0)==5;FA=YA?10:5;YA=YA?19:9;H=A+434|0;U=143-YA&65535;G=A+460|0;L=A+462|0;R=A+464|0;M=e>>>0>2;T=A+388|0;y=(e|0)==0;Y=e>>>0<2;z=A+1244|0;X=A+432|0;J=e>>>0<6;O=A+1168|0;m=(e|0)==6;N=ee<<16>>16==0;K=A+714|0;x=A+686|0;S=A+436|0;j=A+700|0;p=(e|0)==7;W=A+482|0;V=e>>>0<3;Z=A+608|0;_=A+626|0;q=A+438|0;$=e>>>0<7;eA=A+730|0;F=_A^1;fA=B<<16>>16!=0;IA=fA?ee^1:0;iA=A+442|0;rA=A+458|0;nA=A+412|0;tA=A+80|0;wA=A+1236|0;BA=A+1240|0;aA=A+468|0;sA=A+466|0;oA=A+470|0;QA=A+472|0;gA=A+474|0;vA=A+476|0;uA=A+478|0;cA=A+480|0;CA=A+444|0;DA=A+446|0;EA=A+448|0;hA=A+450|0;PA=A+452|0;kA=A+454|0;dA=A+456|0;k=0;b=0;c=0;C=0;d=-1;while(1){d=(d<<16>>16)+1|0;g=d&65535;b=1-(b<<16>>16)|0;E=b&65535;v=zA&c<<16>>16==80?0:c;D=f+2|0;u=r[f>>1]|0;A:do{if(pA){h=r[H>>1]|0;a=(h&65535)-FA&65535;a=a<<16>>16<20?20:a;Q=(a&65535)+YA&65535;o=Q<<16>>16>143;mA(u,o?U:a,o?143:Q,v,h,Qe,ge,I,re);v=r[Qe>>1]|0;r[G>>1]=v;if(_A){lA()}else{u=v;v=r[ge>>1]|0}Si(n[T>>2]|0,u,v,40,1,re);if(Y){v=f+6|0;LA(g,r[f+4>>1]|0,r[D>>1]|0,n[z>>2]|0,ve,re);f=r[X>>1]|0;h=f<<16>>16;u=h<<1;if((u|0)==(h<<17>>16|0)){Q=y;break}Q=y;u=f<<16>>16>0?32767:-32768;break}switch(e|0){case 2:{v=f+6|0;GA(r[f+4>>1]|0,r[D>>1]|0,ve);f=r[X>>1]|0;h=f<<16>>16;u=h<<1;if((u|0)==(h<<17>>16|0)){Q=y;break A}Q=y;u=f<<16>>16>0?32767:-32768;break A}case 3:{v=f+6|0;RA(r[f+4>>1]|0,r[D>>1]|0,ve);f=r[X>>1]|0;h=f<<16>>16;u=h<<1;if((u|0)==(h<<17>>16|0)){Q=y;break A}Q=y;u=f<<16>>16>0?32767:-32768;break A}default:{if(J){v=f+6|0;TA(r[f+4>>1]|0,r[D>>1]|0,n[O>>2]|0,ve);f=r[X>>1]|0;h=f<<16>>16;u=h<<1;if((u|0)==(h<<17>>16|0)){Q=y;break A}Q=y;u=f<<16>>16>0?32767:-32768;break A}if(!m){Q=y;P=44;break A}yA(D,ve,re);u=f+16|0;f=r[X>>1]|0;h=f<<16>>16;g=h<<1;if((g|0)==(h<<17>>16|0)){v=u;Q=y;u=g;break A}v=u;Q=y;u=f<<16>>16>0?32767:-32768;break A}}}else{NA(u,18,143,v,Qe,ge,re);if(N?v<<16>>16==0|u<<16>>16<61:0){u=r[Qe>>1]|0;v=r[ge>>1]|0}else{r[G>>1]=r[Qe>>1]|0;u=r[H>>1]|0;r[Qe>>1]=u;r[ge>>1]=0;v=0}Si(n[T>>2]|0,u,v,40,0,re);Q=0;P=44}}while(0);if((P|0)==44){P=0;if(_A)Ae(x,r[t>>1]|0,De,re);else r[De>>1]=xA(e,r[D>>1]|0,n[BA>>2]|0)|0;fe(x,ee,r[S>>1]|0,De,re);UA(f+4|0,ve,n[O>>2]|0);u=f+24|0;f=r[De>>1]|0;h=f<<16>>16;g=h<<1;if((g|0)==(h<<17>>16|0)){v=u;u=g}else{v=u;u=f<<16>>16>0?32767:-32768}}f=r[Qe>>1]|0;A:do{if(f<<16>>16<40){a=u<<16>>16;o=f;u=f<<16>>16;while(1){g=ve+(u<<1)|0;f=(AA(r[ve+(u-(o<<16>>16)<<1)>>1]|0,a)|0)>>15;if((f|0)>32767){n[re>>2]=1;f=32767}h=f&65535;r[Pe>>1]=h;r[g>>1]=Ci(r[g>>1]|0,h,re)|0;u=u+1|0;if((u&65535)<<16>>16==40)break A;o=r[Qe>>1]|0}}}while(0);A:do{if(Q){Q=(b&65535|0)==0;if(Q){f=v;g=C}else{f=v+2|0;g=r[v>>1]|0}if(N)OA(K,e,g,ve,E,De,Ce,WA,re);else{lA()}fe(x,ee,r[S>>1]|0,De,re);$A(j,ee,r[S>>1]|0,Ce,re);v=r[De>>1]|0;u=v<<16>>16>13017?13017:v;if(Q)P=80;else h=g}else{f=v+2|0;u=r[v>>1]|0;switch(e|0){case 1:case 2:case 3:case 4:case 6:{if(N)OA(K,e,u,ve,E,De,Ce,WA,re);else{lA()}fe(x,ee,r[S>>1]|0,De,re);$A(j,ee,r[S>>1]|0,Ce,re);v=r[De>>1]|0;u=v<<16>>16>13017?13017:v;if(!m){g=C;P=80;break A}if((r[H>>1]|0)<=45){g=C;P=80;break A}g=C;u=u<<16>>16>>>2&65535;P=80;break A}case 5:{if(_A)Ae(x,r[t>>1]|0,De,re);else r[De>>1]=xA(5,u,n[BA>>2]|0)|0;fe(x,ee,r[S>>1]|0,De,re);if(N)KA(K,5,r[f>>1]|0,ve,n[wA>>2]|0,Ce,re);else qA(j,K,r[t>>1]|0,Ce,re);$A(j,ee,r[S>>1]|0,Ce,re);u=r[De>>1]|0;f=v+4|0;v=u;g=C;u=u<<16>>16>13017?13017:u;P=80;break A}default:{if(N)KA(K,e,u,ve,n[wA>>2]|0,Ce,re);else qA(j,K,r[t>>1]|0,Ce,re);$A(j,ee,r[S>>1]|0,Ce,re);u=r[De>>1]|0;v=u;g=C;P=80;break A}}}}while(0);if((P|0)==80){P=0;r[X>>1]=v<<16>>16>13017?13017:v;h=g}u=u<<16>>16;u=(u<<17>>17|0)==(u|0)?u<<1:u>>>15^32767;E=(u&65535)<<16>>16>16384;A:do{if(E){D=u<<16>>16;if(p)v=0;else{v=0;while(1){u=(AA(r[(n[T>>2]|0)+(v<<1)>>1]|0,D)|0)>>15;if((u|0)>32767){n[re>>2]=1;u=32767}r[Pe>>1]=u;u=AA(r[De>>1]|0,u<<16>>16)|0;if((u|0)==1073741824){lA()}else u=u<<1;r[ce+(v<<1)>>1]=er(u,re)|0;v=v+1|0;if((v|0)==40)break A}}do{u=(AA(r[(n[T>>2]|0)+(v<<1)>>1]|0,D)|0)>>15;if((u|0)>32767){n[re>>2]=1;u=32767}r[Pe>>1]=u;u=AA(r[De>>1]|0,u<<16>>16)|0;if((u|0)!=1073741824){u=u<<1;if((u|0)<0)u=~((u^-2)>>1);else P=88}else{lA()}if((P|0)==88){P=0;u=u>>1}r[ce+(v<<1)>>1]=er(u,re)|0;v=v+1|0}while((v|0)!=40)}}while(0);if(N){r[sA>>1]=r[aA>>1]|0;r[aA>>1]=r[oA>>1]|0;r[oA>>1]=r[QA>>1]|0;r[QA>>1]=r[gA>>1]|0;r[gA>>1]=r[vA>>1]|0;r[vA>>1]=r[uA>>1]|0;r[uA>>1]=r[cA>>1]|0;r[cA>>1]=r[W>>1]|0;r[W>>1]=r[De>>1]|0}if((_A|(r[S>>1]|0)!=0?V&(r[L>>1]|0)!=0:0)?(se=r[De>>1]|0,se<<16>>16>12288):0){lA()}ie(he,JA,c,Ee,re);u=HA(Z,e,r[Ce>>1]|0,Ee,_,ee,r[S>>1]|0,B,r[q>>1]|0,r[L>>1]|0,r[R>>1]|0,re)|0;switch(e|0){case 0:case 1:case 2:case 3:case 6:{g=r[De>>1]|0;D=1;break}default:{u=r[Ce>>1]|0;g=r[De>>1]|0;if($)D=1;else{v=g<<16>>16;if(g<<16>>16<0)v=~((v^-2)>>1);else v=v>>>1;g=v&65535;D=2}}}a=g<<16>>16;c=D&65535;v=n[T>>2]|0;C=0;do{v=v+(C<<1)|0;r[ue+(C<<1)>>1]=r[v>>1]|0;v=AA(r[v>>1]|0,a)|0;if((v|0)==1073741824){n[re>>2]=1;o=2147483647}else o=v<<1;Q=AA(r[Ce>>1]|0,r[ve+(C<<1)>>1]|0)|0;if((Q|0)!=1073741824){v=(Q<<1)+o|0;if((Q^o|0)>0&(v^o|0)<0){lA()}}else{n[re>>2]=1;v=2147483647}P=v<<c;P=er((P>>c|0)==(v|0)?P:v>>31^2147483647,re)|0;v=n[T>>2]|0;r[v+(C<<1)>>1]=P;C=C+1|0}while((C|0)!=40);le(eA);if((V?(r[R>>1]|0)>3:0)?!((r[L>>1]|0)==0|F):0)we(eA);Be(eA,e,ue,u,r[De>>1]|0,ve,g,D,WA,re);u=0;Q=0;do{v=r[ue+(Q<<1)>>1]|0;v=AA(v,v)|0;if((v|0)!=1073741824){g=(v<<1)+u|0;if((v^u|0)>0&(g^u|0)<0){n[re>>2]=1;u=(u>>>31)+2147483647|0}else u=g}else{n[re>>2]=1;u=2147483647}Q=Q+1|0}while((Q|0)!=40);if((u|0)<0)u=~((u^-2)>>1);else u=u>>1;u=rr(u,Pe,re)|0;g=((r[Pe>>1]|0)>>>1)+15|0;v=g&65535;g=g<<16>>16;if(v<<16>>16>0)if(v<<16>>16<31){u=u>>g;P=135}else{u=0;P=137}else{lA()}if((P|0)==135){P=0;if((u|0)<0)u=~((u^-4)>>2);else P=137}if((P|0)==137){P=0;u=u>>>2}u=u&65535;do{if(V?(oe=r[R>>1]|0,oe<<16>>16>5):0)if(r[L>>1]|0)if((r[t>>1]|0)<4){if(fA){if(!(_A|(r[q>>1]|0)!=0))P=145}else if(!_A)P=145;if((P|0)==145?(0,(r[S>>1]|0)==0):0){P=147;break}lA()}else P=147;else P=151;else P=147}while(0);do{if((P|0)==147){P=0;if(r[L>>1]|0){if(!_A?(r[S>>1]|0)==0:0){P=151;break}if((r[t>>1]|0)>=4)P=151}else P=151}}while(0);if((P|0)==151){P=0;r[iA>>1]=r[CA>>1]|0;r[CA>>1]=r[DA>>1]|0;r[DA>>1]=r[EA>>1]|0;r[EA>>1]=r[hA>>1]|0;r[hA>>1]=r[PA>>1]|0;r[PA>>1]=r[kA>>1]|0;r[kA>>1]=r[dA>>1]|0;r[dA>>1]=r[rA>>1]|0;r[rA>>1]=u}if(E){u=0;do{E=ce+(u<<1)|0;r[E>>1]=Ci(r[E>>1]|0,r[ue+(u<<1)>>1]|0,re)|0;u=u+1|0}while((u|0)!=40);bA(ue,ce,40,re);n[re>>2]=0;tr(l,ce,w+(k<<1)|0,40,nA,0)}else{n[re>>2]=0;tr(l,ue,w+(k<<1)|0,40,nA,0)}if(!(n[re>>2]|0))Qr(nA|0,w+(k+30<<1)|0,20)|0;else{lA()}Qr(A|0,tA|0,308)|0;r[H>>1]=r[Qe>>1]|0;u=k+40|0;c=u&65535;if(c<<16>>16>=160)break;else{k=u<<16>>16;l=l+22|0;C=h}}r[L>>1]=MA(A+484|0,A+466|0,w,R,re)|0;VA(te,JA,w,re);r[S>>1]=ee;r[q>>1]=B;ne(A+626|0,JA,re);Pe=XA;n[Pe>>2]=ae;s=ke;return}function OA(A,e,f,i,t,l,B,a,o){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;var Q=0,g=0,v=0,u=0,c=0;c=s;s=s+16|0;v=c+2|0;u=c;f=f<<16>>16;f=(f<<18>>18|0)==(f|0)?f<<2:f>>>15^32767;switch(e|0){case 3:case 4:case 6:{g=f<<16>>16;f=n[a+84>>2]|0;r[l>>1]=r[f+(g<<1)>>1]|0;a=r[f+(g+1<<1)>>1]|0;Q=r[f+(g+3<<1)>>1]|0;l=r[f+(g+2<<1)>>1]|0;break}case 0:{a=(f&65535)+(t<<16>>16<<1^2)|0;a=(a&65535)<<16>>16>1022?1022:a<<16>>16;r[l>>1]=r[782+(a<<1)>>1]|0;l=r[782+(a+1<<1)>>1]|0;Gi(l<<16>>16,u,v,o);r[u>>1]=(w[u>>1]|0)+65524;a=ir(r[v>>1]|0,5,o)|0;g=r[u>>1]|0;g=Ci(a,((g<<26>>26|0)==(g|0)?g<<10:g>>>15^32767)&65535,o)|0;a=r[v>>1]|0;f=r[u>>1]|0;if((f*24660|0)==1073741824){n[o>>2]=1;t=2147483647}else t=f*49320|0;Q=(a<<16>>16)*24660>>15;f=t+(Q<<1)|0;if((t^Q|0)>0&(f^t|0)<0){n[o>>2]=1;f=(t>>>31)+2147483647|0}Q=f<<13;a=l;Q=er((Q>>13|0)==(f|0)?Q:f>>31^2147483647,o)|0;l=g;break}default:{g=f<<16>>16;f=n[a+80>>2]|0;r[l>>1]=r[f+(g<<1)>>1]|0;a=r[f+(g+1<<1)>>1]|0;Q=r[f+(g+3<<1)>>1]|0;l=r[f+(g+2<<1)>>1]|0}}Pi(A,e,i,u,v,0,0,o);t=AA((xi(14,r[v>>1]|0,o)|0)<<16>>16,a<<16>>16)|0;if((t|0)==1073741824){n[o>>2]=1;f=2147483647}else f=t<<1;a=10-(w[u>>1]|0)|0;t=a&65535;a=a<<16>>16;if(t<<16>>16>0){u=t<<16>>16<31?f>>a:0;u=u>>>16;u=u&65535;r[B>>1]=u;ki(A,l,Q);s=c;return}else{o=0-a<<16>>16;u=f<<o;u=(u>>o|0)==(f|0)?u:f>>31^2147483647;u=u>>>16;u=u&65535;r[B>>1]=u;ki(A,l,Q);s=c;return}}function mA(A,e,f,i,t,w,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;if(!(i<<16>>16)){B=A<<16>>16;if(A<<16>>16>=197){r[w>>1]=B+65424;r[l>>1]=0;return}t=((B<<16)+131072>>16)*10923>>15;if((t|0)>32767){n[a>>2]=1;t=32767}A=(t&65535)+19|0;r[w>>1]=A;r[l>>1]=B+58-((A*196608|0)>>>16);return}if(!(B<<16>>16)){a=A<<16>>16<<16;A=((a+131072>>16)*21846|0)+-65536>>16;r[w>>1]=A+(e&65535);r[l>>1]=((a+-131072|0)>>>16)-((A*196608|0)>>>16);return}if((nr(t,e,a)|0)<<16>>16>5)t=(e&65535)+5&65535;B=f<<16>>16;B=(B-(t&65535)&65535)<<16>>16>4?B+65532&65535:t;t=A<<16>>16;if(A<<16>>16<4){r[w>>1]=((((B&65535)<<16)+-327680|0)>>>16)+t;r[l>>1]=0;return}t=t<<16;if(A<<16>>16<12){a=(((t+-327680>>16)*10923|0)>>>15<<16)+-65536|0;A=a>>16;r[w>>1]=(B&65535)+A;r[l>>1]=((t+-589824|0)>>>16)-(a>>>15)-A;return}else{r[w>>1]=((t+-786432+((B&65535)<<16)|0)>>>16)+1;r[l>>1]=0;return}}function NA(A,e,f,i,n,t,l){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;l=l|0;if(i<<16>>16){l=(w[n>>1]|0)+65531|0;l=(l<<16>>16|0)<(e<<16>>16|0)?e:l&65535;f=f<<16>>16;e=A<<16>>16<<16;A=((e+327680>>16)*10924|0)+-65536>>16;r[n>>1]=(((((l&65535)<<16)+589824>>16|0)>(f|0)?f+65527&65535:l)&65535)+A;r[t>>1]=((e+-196608|0)>>>16)-((A*393216|0)>>>16);return}i=A<<16>>16;if(A<<16>>16<463){A=((((i<<16)+327680>>16)*10924|0)>>>16)+17|0;r[n>>1]=A;r[t>>1]=i+105-((A*393216|0)>>>16);return}else{r[n>>1]=i+65168;r[t>>1]=0;return}}function KA(A,e,f,i,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,o=0,Q=0;Q=s;s=s+16|0;a=Q+6|0;B=Q+4|0;Pi(A,e,i,a,B,Q+2|0,Q,l);o=(f&31)*3|0;i=t+(o<<1)|0;if(!((nr(e&65535,7,l)|0)<<16>>16)){a=xi(r[a>>1]|0,r[B>>1]|0,l)|0;B=a<<16>>16;B=(AA(((a<<20>>20|0)==(B|0)?a<<4:B>>>15^32767)<<16>>16,r[i>>1]|0)|0)>>15;if((B|0)>32767){n[l>>2]=1;B=32767}i=B<<16;f=i>>16;if((B<<17>>17|0)==(f|0))B=i>>15;else B=f>>>15^32767}else{f=xi(14,r[B>>1]|0,l)|0;f=AA(f<<16>>16,r[i>>1]|0)|0;if((f|0)==1073741824){n[l>>2]=1;i=2147483647}else i=f<<1;f=nr(9,r[a>>1]|0,l)|0;B=f<<16>>16;if(f<<16>>16>0)B=f<<16>>16<31?i>>B:0;else{l=0-B<<16>>16;B=i<<l;B=(B>>l|0)==(i|0)?B:i>>31^2147483647}B=B>>>16}r[w>>1]=B;ki(A,r[t+(o+1<<1)>>1]|0,r[t+(o+2<<1)>>1]|0);s=Q;return}function xA(A,e,f){A=A|0;e=e|0;f=f|0;e=r[f+(e<<16>>16<<1)>>1]|0;if((A|0)!=7){A=e;return A|0}A=e&65532;return A|0}function SA(A,e,f,t,w,l,B){A=A|0;e=e|0;f=f|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0;h=s;s=s+48|0;c=h+20|0;E=h;D=n[w+44>>2]|0;C=n[w+64>>2]|0;a=n[w+4>>2]|0;u=n[w+12>>2]|0;Q=n[w+20>>2]|0;o=n[w+56>>2]|0;if(!(f<<16>>16)){g=e>>>0<2;if(g){f=765;v=508;Q=n[w+52>>2]|0}else{w=(e|0)==5;f=w?1533:765;v=2044;a=w?o:a}o=r[t>>1]|0;f=((o*196608>>16|0)>(f&65535|0)?f:o*3&65535)<<16>>16;o=r[a+(f<<1)>>1]|0;r[c>>1]=o;r[c+2>>1]=r[a+(f+1<<1)>>1]|0;r[c+4>>1]=r[a+(f+2<<1)>>1]|0;f=r[t+2>>1]|0;if(g)f=f<<16>>16<<1&65535;g=(f<<16>>16)*196608|0;g=(g|0)>100466688?1533:g>>16;r[c+6>>1]=r[u+(g<<1)>>1]|0;r[c+8>>1]=r[u+(g+1<<1)>>1]|0;r[c+10>>1]=r[u+(g+2<<1)>>1]|0;t=r[t+4>>1]|0;t=((t<<18>>16|0)>(v&65535|0)?v:t<<2&65535)<<16>>16;r[c+12>>1]=r[Q+(t<<1)>>1]|0;r[c+14>>1]=r[Q+((t|1)<<1)>>1]|0;r[c+16>>1]=r[Q+((t|2)<<1)>>1]|0;r[c+18>>1]=r[Q+((t|3)<<1)>>1]|0;if((e|0)==8){lA()}else a=0;do{o=A+(a<<1)|0;f=(AA(r[C+(a<<1)>>1]|0,r[o>>1]|0)|0)>>15;if((f|0)>32767){n[B>>2]=1;f=32767}t=Ci(r[D+(a<<1)>>1]|0,f&65535,B)|0;e=r[c+(a<<1)>>1]|0;r[E+(a<<1)>>1]=Ci(e,t,B)|0;r[o>>1]=e;a=a+1|0}while((a|0)!=10);$i(E,205,10,B);a=A+20|0;o=E;f=a+20|0;do{i[a>>0]=i[o>>0]|0;a=a+1|0;o=o+1|0}while((a|0)<(f|0));Oi(E,l,10,B);s=h;return}else{lA();return}}function jA(A,e,f,t,w,l,B){A=A|0;e=e|0;f=f|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0;h=s;s=s+80|0;u=h+60|0;c=h+40|0;D=h+20|0;E=h;C=n[t+48>>2]|0;Q=n[t+24>>2]|0;g=n[t+28>>2]|0;v=n[t+32>>2]|0;if(e<<16>>16){lA()}e=n[t+16>>2]|0;t=n[t+8>>2]|0;o=r[f>>1]|0;o=((o<<18>>18|0)==(o|0)?o<<2:o>>>15^32767)<<16>>16;r[u>>1]=r[t+(o<<1)>>1]|0;r[u+2>>1]=r[t+(o+1<<1)>>1]|0;r[c>>1]=r[t+(o+2<<1)>>1]|0;r[c+2>>1]=r[t+(o+3<<1)>>1]|0;o=r[f+2>>1]|0;o=((o<<18>>18|0)==(o|0)?o<<2:o>>>15^32767)<<16>>16;r[u+4>>1]=r[e+(o<<1)>>1]|0;r[u+6>>1]=r[e+(o+1<<1)>>1]|0;r[c+4>>1]=r[e+(o+2<<1)>>1]|0;r[c+6>>1]=r[e+(o+3<<1)>>1]|0;o=r[f+4>>1]|0;t=o<<16>>16;if(o<<16>>16<0)e=~((t^-2)>>1);else e=t>>>1;o=e<<16>>16;o=((e<<18>>18|0)==(o|0)?e<<2:o>>>15^32767)<<16>>16;a=Q+(o+1<<1)|0;e=r[Q+(o<<1)>>1]|0;if(!(t&1)){r[u+8>>1]=e;r[u+10>>1]=r[a>>1]|0;r[c+8>>1]=r[Q+(o+2<<1)>>1]|0;r[c+10>>1]=r[Q+(o+3<<1)>>1]|0}else{if(e<<16>>16==-32768)e=32767;else e=0-(e&65535)&65535;r[u+8>>1]=e;e=r[a>>1]|0;if(e<<16>>16==-32768)e=32767;else e=0-(e&65535)&65535;r[u+10>>1]=e;e=r[Q+(o+2<<1)>>1]|0;if(e<<16>>16==-32768)e=32767;else e=0-(e&65535)&65535;r[c+8>>1]=e;e=r[Q+(o+3<<1)>>1]|0;if(e<<16>>16==-32768)e=32767;else e=0-(e&65535)&65535;r[c+10>>1]=e}a=r[f+6>>1]|0;a=((a<<18>>18|0)==(a|0)?a<<2:a>>>15^32767)<<16>>16;r[u+12>>1]=r[g+(a<<1)>>1]|0;r[u+14>>1]=r[g+(a+1<<1)>>1]|0;r[c+12>>1]=r[g+(a+2<<1)>>1]|0;r[c+14>>1]=r[g+(a+3<<1)>>1]|0;a=r[f+8>>1]|0;a=((a<<18>>18|0)==(a|0)?a<<2:a>>>15^32767)<<16>>16;r[u+16>>1]=r[v+(a<<1)>>1]|0;r[u+18>>1]=r[v+(a+1<<1)>>1]|0;r[c+16>>1]=r[v+(a+2<<1)>>1]|0;r[c+18>>1]=r[v+(a+3<<1)>>1]|0;a=0;do{t=A+(a<<1)|0;e=(r[t>>1]|0)*21299>>15;if((e|0)>32767){n[B>>2]=1;e=32767}v=Ci(r[C+(a<<1)>>1]|0,e&65535,B)|0;r[D+(a<<1)>>1]=Ci(r[u+(a<<1)>>1]|0,v,B)|0;f=r[c+(a<<1)>>1]|0;r[E+(a<<1)>>1]=Ci(f,v,B)|0;r[t>>1]=f;a=a+1|0}while((a|0)!=10);$i(D,205,10,B);$i(E,205,10,B);a=A+20|0;t=E;e=a+20|0;do{i[a>>0]=i[t>>0]|0;a=a+1|0;t=t+1|0}while((a|0)<(e|0));Oi(D,w,10,B);Oi(E,l,10,B);s=h;return}function pA(A,e){A=A|0;e=e|0;var f=0,i=0;if(!A){i=-1;return i|0}f=A;i=f+20|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));Qr(A+20|0,e|0,20)|0;i=0;return i|0}function WA(A){A=A|0;var e=0,f=0,t=0,w=0,l=0;if(!A){l=-1;return l|0}r[A>>1]=0;r[A+2>>1]=8192;e=A+4|0;r[e>>1]=3500;r[A+6>>1]=3500;n[A+8>>2]=1887529304;r[A+12>>1]=3e4;r[A+14>>1]=26e3;r[A+16>>1]=21e3;r[A+18>>1]=15e3;r[A+20>>1]=8e3;r[A+22>>1]=0;r[A+24>>1]=-8e3;r[A+26>>1]=-15e3;r[A+28>>1]=-21e3;r[A+30>>1]=-26e3;r[A+32>>1]=3e4;r[A+34>>1]=26e3;r[A+36>>1]=21e3;r[A+38>>1]=15e3;r[A+40>>1]=8e3;r[A+42>>1]=0;r[A+44>>1]=-8e3;r[A+46>>1]=-15e3;r[A+48>>1]=-21e3;r[A+50>>1]=-26e3;r[A+212>>1]=0;r[A+374>>1]=0;r[A+392>>1]=0;f=A+52|0;r[f>>1]=1384;r[A+54>>1]=2077;r[A+56>>1]=3420;r[A+58>>1]=5108;r[A+60>>1]=6742;r[A+62>>1]=8122;r[A+64>>1]=9863;r[A+66>>1]=11092;r[A+68>>1]=12714;r[A+70>>1]=13701;t=A+72|0;w=f;l=t+20|0;do{i[t>>0]=i[w>>0]|0;t=t+1|0;w=w+1|0}while((t|0)<(l|0));t=A+92|0;w=f;l=t+20|0;do{i[t>>0]=i[w>>0]|0;t=t+1|0;w=w+1|0}while((t|0)<(l|0));t=A+112|0;w=f;l=t+20|0;do{i[t>>0]=i[w>>0]|0;t=t+1|0;w=w+1|0}while((t|0)<(l|0));t=A+132|0;w=f;l=t+20|0;do{i[t>>0]=i[w>>0]|0;t=t+1|0;w=w+1|0}while((t|0)<(l|0));t=A+152|0;w=f;l=t+20|0;do{i[t>>0]=i[w>>0]|0;t=t+1|0;w=w+1|0}while((t|0)<(l|0));t=A+172|0;w=f;l=t+20|0;do{i[t>>0]=i[w>>0]|0;t=t+1|0;w=w+1|0}while((t|0)<(l|0));t=A+192|0;w=f;l=t+20|0;do{i[t>>0]=i[w>>0]|0;t=t+1|0;w=w+1|0}while((t|0)<(l|0));gr(A+214|0,0,160)|0;r[A+376>>1]=3500;r[A+378>>1]=3500;l=r[e>>1]|0;r[A+380>>1]=l;r[A+382>>1]=l;r[A+384>>1]=l;r[A+386>>1]=l;r[A+388>>1]=l;r[A+390>>1]=l;r[A+394>>1]=0;r[A+396>>1]=7;r[A+398>>1]=32767;r[A+400>>1]=0;r[A+402>>1]=0;r[A+404>>1]=0;n[A+408>>2]=1;r[A+412>>1]=0;l=0;return l|0}function VA(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var t=0,l=0,B=0,a=0,o=0,Q=0;o=s;s=s+16|0;B=o+2|0;a=o;r[a>>1]=0;l=A+212|0;t=(w[l>>1]|0)+10|0;t=(t&65535|0)==80?0:t&65535;r[l>>1]=t;Qr(A+52+(t<<16>>16<<1)|0,e|0,20)|0;t=0;l=159;while(1){Q=r[f+(l<<1)>>1]|0;Q=AA(Q,Q)|0;Q=(Q|0)==1073741824?2147483647:Q<<1;e=Q+t|0;if((Q^t|0)>-1&(e^t|0)<0){n[i>>2]=1;t=(t>>>31)+2147483647|0}else t=e;if((l|0)>0)l=l+-1|0;else break}Gi(t,B,a,i);t=r[B>>1]|0;Q=t<<16>>16;e=Q<<10;if((e|0)!=(Q<<26>>16|0)){n[i>>2]=1;e=t<<16>>16>0?32767:-32768}r[B>>1]=e;Q=r[a>>1]|0;t=Q<<16>>16;if(Q<<16>>16<0)t=~((t^-32)>>5);else t=t>>>5;a=A+392|0;Q=(w[a>>1]|0)+1|0;Q=(Q&65535|0)==8?0:Q&65535;r[a>>1]=Q;r[A+376+(Q<<16>>16<<1)>>1]=t+57015+e;s=o;return}function ZA(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0;a=(e|0)==4;s=(e|0)==5;o=(e|0)==6;i=n[A+408>>2]|0;A:do{if((e+-4|0)>>>0<3)B=4;else{if((i+-1|0)>>>0<2)switch(e|0){case 2:case 3:case 7:{B=4;break A}default:{}}r[A>>1]=0;l=0}}while(0);if((B|0)==4){lA()}w=A+398|0;if(s&(r[A+412>>1]|0)==0){r[w>>1]=0;t=0}else t=r[w>>1]|0;t=Ci(t,1,f)|0;r[w>>1]=t;f=A+404|0;r[f>>1]=0;A:do{switch(e|0){case 2:case 4:case 5:case 6:case 7:{if(!((e|0)==7&(l|0)==0)){lA()}else B=14;break}default:B=14}}while(0);if((B|0)==14)r[A+396>>1]=7;if(!l)return l|0;t=A+400|0;r[t>>1]=0;i=A+402|0;r[i>>1]=0;if(a){r[t>>1]=1;return l|0}if(s){r[t>>1]=1;r[i>>1]=1;return l|0}if(!o)return l|0;r[t>>1]=1;r[f>>1]=0;return l|0}function _A(A){A=A|0;if(!A){A=-1;return A|0}r[A>>1]=1;r[A+2>>1]=1;r[A+4>>1]=1;r[A+6>>1]=1;r[A+8>>1]=1;r[A+10>>1]=0;r[A+12>>1]=1;A=0;return A|0}function qA(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;lA();return}function $A(A,e,f,i,n){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;if(!(e<<16>>16)){if(f<<16>>16){lA()}else e=A+12|0;r[e>>1]=r[i>>1]|0}r[A+10>>1]=r[i>>1]|0;n=A+2|0;r[A>>1]=r[n>>1]|0;f=A+4|0;r[n>>1]=r[f>>1]|0;n=A+6|0;r[f>>1]=r[n>>1]|0;A=A+8|0;r[n>>1]=r[A>>1]|0;r[A>>1]=r[i>>1]|0;return}function Ae(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;lA();return}function ee(A){A=A|0;if(!A){A=-1;return A|0}r[A>>1]=1640;r[A+2>>1]=1640;r[A+4>>1]=1640;r[A+6>>1]=1640;r[A+8>>1]=1640;r[A+10>>1]=0;r[A+12>>1]=16384;A=0;return A|0}function fe(A,e,f,i,n){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;if(!(e<<16>>16)){if(f<<16>>16){lA()}else e=A+12|0;r[e>>1]=r[i>>1]|0}i=r[i>>1]|0;e=A+10|0;r[e>>1]=i;if((nr(i,16384,n)|0)<<16>>16>0){r[e>>1]=16384;e=16384}else e=r[e>>1]|0;n=A+2|0;r[A>>1]=r[n>>1]|0;i=A+4|0;r[n>>1]=r[i>>1]|0;n=A+6|0;r[i>>1]=r[n>>1]|0;A=A+8|0;r[n>>1]=r[A>>1]|0;r[A>>1]=e;return}function ie(A,e,f,i,n){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;var t=0,w=0,l=0,B=0;switch(f<<16>>16){case 0:{B=9;while(1){l=r[A+(B<<1)>>1]|0;f=l<<16>>16;if(l<<16>>16<0)f=~((f^-4)>>2);else f=f>>>2;w=r[e+(B<<1)>>1]|0;t=w<<16>>16;if(w<<16>>16<0)w=~((t^-4)>>2);else w=t>>>2;r[i+(B<<1)>>1]=Ci((l&65535)-f&65535,w&65535,n)|0;if((B|0)>0)B=B+-1|0;else break}return}case 40:{w=9;while(1){n=r[A+(w<<1)>>1]|0;f=n<<16>>16;if(n<<16>>16<0)t=~((f^-2)>>1);else t=f>>>1;n=r[e+(w<<1)>>1]|0;f=n<<16>>16;if(n<<16>>16<0)f=~((f^-2)>>1);else f=f>>>1;r[i+(w<<1)>>1]=f+t;if((w|0)>0)w=w+-1|0;else break}return}case 80:{B=9;while(1){l=r[A+(B<<1)>>1]|0;f=l<<16>>16;if(l<<16>>16<0)l=~((f^-4)>>2);else l=f>>>2;f=r[e+(B<<1)>>1]|0;t=f<<16>>16;if(f<<16>>16<0)w=~((t^-4)>>2);else w=t>>>2;r[i+(B<<1)>>1]=Ci(l&65535,(f&65535)-w&65535,n)|0;if((B|0)>0)B=B+-1|0;else break}return}case 120:{r[i+18>>1]=r[e+18>>1]|0;r[i+16>>1]=r[e+16>>1]|0;r[i+14>>1]=r[e+14>>1]|0;r[i+12>>1]=r[e+12>>1]|0;r[i+10>>1]=r[e+10>>1]|0;r[i+8>>1]=r[e+8>>1]|0;r[i+6>>1]=r[e+6>>1]|0;r[i+4>>1]=r[e+4>>1]|0;r[i+2>>1]=r[e+2>>1]|0;r[i>>1]=r[e>>1]|0;return}default:return}}function re(A,e){A=A|0;e=e|0;if(!A){A=-1;return A|0}Qr(A|0,e|0,20)|0;A=0;return A|0}function ne(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,t=0,w=0,l=0,B=0,a=0,s=0;s=0;do{a=A+(s<<1)|0;i=r[a>>1]|0;l=i&65535;B=l<<16;i=i<<16>>16;if((i*5243|0)==1073741824){n[f>>2]=1;w=2147483647}else w=i*10486|0;t=B-w|0;if(((t^B)&(w^B)|0)<0){n[f>>2]=1;w=(l>>>15)+2147483647|0}else w=t;i=r[e+(s<<1)>>1]|0;t=i*5243|0;if((t|0)!=1073741824){i=(i*10486|0)+w|0;if((t^w|0)>0&(i^w|0)<0){n[f>>2]=1;i=(w>>>31)+2147483647|0}}else{n[f>>2]=1;i=2147483647}r[a>>1]=er(i,f)|0;s=s+1|0}while((s|0)!=10);return}function te(A){A=A|0;var e=0;if(!A){e=-1;return e|0}e=A+18|0;do{r[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function we(A){A=A|0;r[A+14>>1]=1;return}function le(A){A=A|0;r[A+14>>1]=0;return}function Be(A,e,f,i,t,w,l,B,a,o){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;o=o|0;var Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0;F=s;s=s+160|0;d=F+80|0;I=F;h=n[a+120>>2]|0;P=n[a+124>>2]|0;k=n[a+128>>2]|0;E=n[a+132>>2]|0;g=A+6|0;D=A+8|0;r[D>>1]=r[g>>1]|0;c=A+4|0;r[g>>1]=r[c>>1]|0;C=A+2|0;r[c>>1]=r[C>>1]|0;r[C>>1]=r[A>>1]|0;r[A>>1]=t;a=t<<16>>16<14746?t<<16>>16>9830&1:2;Q=A+12|0;t=r[Q>>1]|0;v=t<<15;do{if((v|0)<=536870911)if((v|0)<-536870912){n[o>>2]=1;t=-2147483648;break}else{t=t<<17;break}else{n[o>>2]=1;t=2147483647}}while(0);b=i<<16>>16;u=A+16|0;if((er(t,o)|0)<<16>>16>=i<<16>>16){v=r[u>>1]|0;if(v<<16>>16>0){v=(v&65535)+65535&65535;r[u>>1]=v}if(!(v<<16>>16)){t=(r[A>>1]|0)<9830;t=(r[C>>1]|0)<9830?t?2:1:t&1;if((r[c>>1]|0)<9830)t=(t&65535)+1&65535;if((r[g>>1]|0)<9830)t=(t&65535)+1&65535;if((r[D>>1]|0)<9830)t=(t&65535)+1&65535;v=0;a=t<<16>>16>2?0:a}}else{r[u>>1]=2;v=2}C=a<<16>>16;D=A+10|0;C=(v<<16>>16==0?(C|0)>((r[D>>1]|0)+1|0):0)?C+65535&65535:a;A=(r[A+14>>1]|0)==1?0:i<<16>>16<10?2:C<<16>>16<2&v<<16>>16>0?(C&65535)+1&65535:C;r[D>>1]=A;r[Q>>1]=i;switch(e|0){case 4:case 6:case 7:break;default:if(A<<16>>16<2){v=0;a=0;g=w;Q=d;while(1){if(!(r[g>>1]|0))t=0;else{a=a<<16>>16;r[I+(a<<1)>>1]=v;t=r[g>>1]|0;a=a+1&65535}r[Q>>1]=t;r[g>>1]=0;v=v+1<<16>>16;if(v<<16>>16>=40){D=a;break}else{g=g+2|0;Q=Q+2|0}}C=A<<16>>16==0;C=(e|0)==5?C?h:P:C?k:E;if(D<<16>>16>0){c=0;do{u=r[I+(c<<1)>>1]|0;a=u<<16>>16;A=r[d+(a<<1)>>1]|0;if(u<<16>>16<40){v=A<<16>>16;g=39-u&65535;Q=u;a=w+(a<<1)|0;t=C;while(1){e=(AA(r[t>>1]|0,v)|0)>>>15&65535;r[a>>1]=Ci(r[a>>1]|0,e,o)|0;Q=Q+1<<16>>16;if(Q<<16>>16>=40)break;else{a=a+2|0;t=t+2|0}}if(u<<16>>16>0){a=C+(g+1<<1)|0;M=36}}else{a=C;M=36}if((M|0)==36){M=0;t=A<<16>>16;v=0;g=w;while(1){e=(AA(r[a>>1]|0,t)|0)>>>15&65535;r[g>>1]=Ci(r[g>>1]|0,e,o)|0;v=v+1<<16>>16;if(v<<16>>16>=u<<16>>16)break;else{g=g+2|0;a=a+2|0}}}c=c+1|0}while((c&65535)<<16>>16!=D<<16>>16)}}}c=l<<16>>16;C=b<<1;t=B<<16>>16;Q=0-t<<16;a=Q>>16;if(B<<16>>16>0){v=0;g=f;while(1){A=AA(r[f+(v<<1)>>1]|0,c)|0;if((A|0)==1073741824){n[o>>2]=1;Q=2147483647}else Q=A<<1;B=AA(C,r[w>>1]|0)|0;A=B+Q|0;if((B^Q|0)>-1&(A^Q|0)<0){n[o>>2]=1;A=(Q>>>31)+2147483647|0}B=A<<t;r[g>>1]=er((B>>t|0)==(A|0)?B:A>>31^2147483647,o)|0;v=v+1|0;if((v|0)==40)break;else{w=w+2|0;g=g+2|0}}s=F;return}lA();return}function ae(A){A=A|0;if(!A){A=-1;return A|0}r[A>>1]=0;r[A+2>>1]=0;r[A+4>>1]=0;r[A+6>>1]=0;r[A+8>>1]=0;r[A+10>>1]=0;A=0;return A|0}function se(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0;if(f<<16>>16<=0)return;n=A+10|0;B=A+8|0;s=A+4|0;o=A+6|0;Q=A+2|0;t=r[s>>1]|0;w=r[o>>1]|0;l=r[A>>1]|0;a=r[Q>>1]|0;g=0;while(1){v=r[n>>1]|0;u=r[B>>1]|0;r[n>>1]=u;c=r[e>>1]|0;r[B>>1]=c;v=((c<<16>>16)*7699|0)+((AA(l<<16>>16,-7667)|0)+(((t<<16>>16)*15836|0)+((w<<16>>16)*15836>>15))+((AA(a<<16>>16,-7667)|0)>>15))+(AA(u<<16>>16,-15398)|0)+((v<<16>>16)*7699|0)|0;u=v<<3;v=(u>>3|0)==(v|0)?u:v>>31^2147483647;u=v<<1;r[e>>1]=er((u>>1|0)==(v|0)?u:v>>31^2147483647,i)|0;l=r[s>>1]|0;r[A>>1]=l;a=r[o>>1]|0;r[Q>>1]=a;t=v>>>16&65535;r[s>>1]=t;w=(v>>>1)-(v>>16<<15)&65535;r[o>>1]=w;g=g+1<<16>>16;if(g<<16>>16>=f<<16>>16)break;else e=e+2|0}return}function oe(A){A=A|0;if(!A)A=-1;else{r[A>>1]=0;A=0}return A|0}function Qe(A,e,f,i,t){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;var w=0,l=0,B=0,a=0;B=i<<16>>16;w=e+(B+-1<<1)|0;B=B+-2|0;a=r[w>>1]|0;if(i<<16>>16<2)i=f<<16>>16;else{i=f<<16>>16;l=0;e=e+(B<<1)|0;while(1){f=(AA(r[e>>1]|0,i)|0)>>15;if((f|0)>32767){n[t>>2]=1;f=32767}r[w>>1]=nr(r[w>>1]|0,f&65535,t)|0;w=w+-2|0;l=l+1<<16>>16;if((l<<16>>16|0)>(B|0))break;else e=e+-2|0}}i=(AA(r[A>>1]|0,i)|0)>>15;if((i|0)<=32767){B=i;B=B&65535;l=r[w>>1]|0;t=nr(l,B,t)|0;r[w>>1]=t;r[A>>1]=a;return}n[t>>2]=1;B=32767;B=B&65535;l=r[w>>1]|0;t=nr(l,B,t)|0;r[w>>1]=t;r[A>>1]=a;return}function ge(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}gr(A+104|0,0,340)|0;e=A+102|0;f=A;i=f+100|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));PA(e)|0;oe(A+100|0)|0;i=0;return i|0}function ve(A,e,f,t,w){A=A|0;e=e|0;f=f|0;t=t|0;w=w|0;var l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0;P=s;s=s+96|0;C=P+22|0;D=P;E=P+44|0;Qr(A+124|0,f|0,320)|0;g=E+22|0;v=A+100|0;u=A+80|0;c=A+102|0;if((e&-2|0)==6){Q=0;while(1){wr(t,702,C);wr(t,722,D);o=A+104+(Q+10<<1)|0;Ar(C,o,A,40);B=E;l=C;e=B+22|0;do{r[B>>1]=r[l>>1]|0;B=B+2|0;l=l+2|0}while((B|0)<(e|0));B=g;e=B+22|0;do{r[B>>1]=0;B=B+2|0}while((B|0)<(e|0));tr(D,E,E,22,g,0);e=0;B=21;do{l=r[E+(B<<16>>16<<1)>>1]|0;l=AA(l,l)|0;if((l|0)==1073741824){h=7;break}a=l<<1;l=a+e|0;if((a^e|0)>-1&(l^e|0)<0){lA()}else e=l;B=B+-1<<16>>16}while(B<<16>>16>-1);if((h|0)==7){h=0;n[w>>2]=1}a=e>>>16&65535;l=20;e=0;B=20;while(1){l=AA(r[E+(l+1<<1)>>1]|0,r[E+(l<<1)>>1]|0)|0;if((l|0)==1073741824){h=13;break}k=l<<1;l=k+e|0;if((k^e|0)>-1&(l^e|0)<0){lA()}else e=l;l=(B&65535)+-1<<16>>16;if(l<<16>>16>-1){l=l<<16>>16;B=B+-1|0}else break}if((h|0)==13){h=0;n[w>>2]=1}e=e>>16;if((e|0)<1)e=0;else e=Ei((e*26214|0)>>>15&65535,a)|0;Qe(v,A,e,40,w);e=f+(Q<<1)|0;tr(D,A,e,40,u,1);kA(c,o,e,29491,40,w);e=(Q<<16)+2621440|0;if((e|0)<10485760){Q=e>>16;t=t+22|0}else break}B=A+104|0;l=A+424|0;e=B+20|0;do{i[B>>0]=i[l>>0]|0;B=B+1|0;l=l+1|0}while((B|0)<(e|0));s=P;return}else{Q=0;while(1){wr(t,742,C);wr(t,762,D);o=A+104+(Q+10<<1)|0;Ar(C,o,A,40);B=E;l=C;e=B+22|0;do{r[B>>1]=r[l>>1]|0;B=B+2|0;l=l+2|0}while((B|0)<(e|0));B=g;e=B+22|0;do{r[B>>1]=0;B=B+2|0}while((B|0)<(e|0));tr(D,E,E,22,g,0);e=0;B=21;do{l=r[E+(B<<16>>16<<1)>>1]|0;l=AA(l,l)|0;if((l|0)==1073741824){h=22;break}k=l<<1;l=k+e|0;if((k^e|0)>-1&(l^e|0)<0){lA()}else e=l;B=B+-1<<16>>16}while(B<<16>>16>-1);if((h|0)==22){h=0;n[w>>2]=1}a=e>>>16&65535;l=20;e=0;B=20;while(1){l=AA(r[E+(l+1<<1)>>1]|0,r[E+(l<<1)>>1]|0)|0;if((l|0)==1073741824){h=28;break}k=l<<1;l=k+e|0;if((k^e|0)>-1&(l^e|0)<0){lA()}else e=l;l=(B&65535)+-1<<16>>16;if(l<<16>>16>-1){l=l<<16>>16;B=B+-1|0}else break}if((h|0)==28){h=0;n[w>>2]=1}e=e>>16;if((e|0)<1)e=0;else e=Ei((e*26214|0)>>>15&65535,a)|0;Qe(v,A,e,40,w);e=f+(Q<<1)|0;tr(D,A,e,40,u,1);kA(c,o,e,29491,40,w);e=(Q<<16)+2621440|0;if((e|0)<10485760){Q=e>>16;t=t+22|0}else break}B=A+104|0;l=A+424|0;e=B+20|0;do{i[B>>0]=i[l>>0]|0;B=B+1|0;l=l+1|0}while((B|0)<(e|0));s=P;return}}function ue(A,e){A=A|0;e=e|0;var f=0,i=0;if(!A){A=-1;return A|0}n[A>>2]=0;f=lr(1764)|0;if(!f){A=-1;return A|0}if((zA(f)|0)<<16>>16==0?(i=f+1748|0,(ae(i)|0)<<16>>16==0):0){XA(f,0)|0;ge(f+1304|0)|0;ae(i)|0;n[f+1760>>2]=0;n[A>>2]=f;A=0;return A|0}e=n[f>>2]|0;if(!e){A=-1;return A|0}Br(e);n[f>>2]=0;A=-1;return A|0}function ce(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function Ce(A,e,f,i,t){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;var l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0;h=s;s=s+208|0;E=h+88|0;D=h;C=A+1164|0;l=n[A+1256>>2]|0;if((i+-5|0)>>>0<2){lA()}else{u=l+(e<<1)|0;if((r[u>>1]|0)>0){c=n[(n[A+1260>>2]|0)+(e<<2)>>2]|0;g=0;l=0;while(1){v=c+(g<<1)|0;o=r[v>>1]|0;if(o<<16>>16>0){a=f;Q=0;B=0;while(1){B=w[a>>1]|B<<1&131070;Q=Q+1<<16>>16;if(Q<<16>>16>=o<<16>>16)break;else a=a+2|0}B=B&65535}else B=0;r[E+(g<<1)>>1]=B;l=l+1<<16>>16;if(l<<16>>16<(r[u>>1]|0)){f=f+(r[v>>1]<<1)|0;g=l<<16>>16}else break}}}JA(A,e,E,i,t,D);ve(A+1304|0,e,t,D,C);se(A+1748|0,t,160,C);l=0;do{A=t+(l<<1)|0;r[A>>1]=w[A>>1]&65528;l=l+1|0}while((l|0)!=160);s=h;return}function De(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var l=0,B=0,a=0;B=n[i+100>>2]|0;a=(w[(n[i+96>>2]|0)+(A<<1)>>1]|0)+65535|0;i=a&65535;l=i<<16>>16>-1;if(A>>>0<8){if(!l)return;B=n[B+(A<<2)>>2]|0;l=a<<16>>16;while(1){r[f+(r[B+(l<<1)>>1]<<1)>>1]=(t[e+(l>>3)>>0]|0)>>>(l&7^7)&1;i=i+-1<<16>>16;if(i<<16>>16>-1)l=i<<16>>16;else break}return}else{lA()}}function Ee(A,e,f){A=A|0;e=e|0;f=f|0;A=ri(A,f,31764)|0;return((ei(e)|0|A)<<16>>16!=0)<<31>>31|0}function he(A,e){A=A|0;e=e|0;ni(A);fi(e);return}function Pe(A,e,f,i,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,o=0,Q=0,g=0;g=s;s=s+512|0;B=g+8|0;a=g+4|0;o=g;n[o>>2]=0;Q=l<<16>>16==3;if(!((l&65535)<2|Q&1)){lA()}ti(A,f,i,B,o);ii(e,n[o>>2]|0,a);i=n[a>>2]|0;if((i|0)!=3){e=n[o>>2]|0;n[w>>2]=e;if((e|0)==8){lA()}}else{n[w>>2]=15;e=15}if(Q){ff(e,B,t,(n[A+4>>2]|0)+2392|0);t=r[3404+(n[w>>2]<<16>>16<<1)>>1]|0;s=g;return t|0}switch(l<<16>>16){case 0:{lA()}case 1:{lA()}default:{t=-1;s=g;return t|0}}return 0}function ke(A,e,f,i,n,t){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0;b=s;s=s+480|0;k=b;t=240;a=n;B=A;l=k;w=0;while(1){P=((AA(r[a>>1]|0,r[B>>1]|0)|0)+16384|0)>>>15;r[l>>1]=P;P=P<<16;w=(AA(P>>15,P>>16)|0)+w|0;if((w|0)<0){o=4;break}t=t+-1|0;if(!((t&65535)<<16>>16)){t=0;break}else{a=a+2|0;B=B+2|0;l=l+2|0}}if((o|0)==4){w=t&65535;l=240-t|0;if(!(w<<16>>16))t=0;else{a=w;B=n+(l<<1)|0;t=A+(l<<1)|0;w=k+(l<<1)|0;while(1){r[w>>1]=((AA(r[B>>1]|0,r[t>>1]|0)|0)+16384|0)>>>15;a=a+-1<<16>>16;if(!(a<<16>>16)){t=0;break}else{B=B+2|0;t=t+2|0;w=w+2|0}}}do{B=t&65535;t=120;l=k;w=0;while(1){P=(r[l>>1]|0)>>>2;E=l+2|0;r[l>>1]=P;P=P<<16>>16;P=AA(P,P)|0;h=(r[E>>1]|0)>>>2;r[E>>1]=h;h=h<<16>>16;w=((AA(h,h)|0)+P<<1)+w|0;t=t+-1<<16>>16;if(!(t<<16>>16))break;else l=l+4|0}t=B+4|0}while((w|0)<1)}P=w+1|0;h=(Ni(P)|0)<<16>>16;P=P<<h;r[f>>1]=P>>>16;r[i>>1]=(P>>>1)-(P>>16<<15);P=k+478|0;a=e<<16>>16;if(e<<16>>16<=0){lA()}c=k+476|0;C=h+1|0;D=239-a|0;E=k+(236-a<<1)|0;e=a;f=f+(a<<1)|0;i=i+(a<<1)|0;while(1){o=AA((D>>>1)+65535&65535,-2)|0;B=k+(o+236<<1)|0;o=E+(o<<1)|0;n=240-e|0;u=n+-1|0;l=k+(u<<1)|0;A=u>>>1&65535;n=k+(n+-2<<1)|0;a=AA(r[P>>1]|0,r[l>>1]|0)|0;if(!(A<<16>>16)){o=n;B=c}else{v=c;g=P;while(1){w=l+-4|0;Q=g+-4|0;a=(AA(r[v>>1]|0,r[n>>1]|0)|0)+a|0;A=A+-1<<16>>16;a=(AA(r[Q>>1]|0,r[w>>1]|0)|0)+a|0;if(!(A<<16>>16))break;else{n=l+-6|0;v=g+-6|0;l=w;g=Q}}}if(u&1)a=(AA(r[B>>1]|0,r[o>>1]|0)|0)+a|0;u=a<<C;r[f>>1]=u>>>16;r[i>>1]=(u>>>1)-(u>>16<<15);if((e&65535)+-1<<16>>16<<16>>16>0){D=D+1|0;E=E+2|0;e=e+-1|0;f=f+-2|0;i=i+-2|0}else break}k=h-t|0;k=k&65535;s=b;return k|0}function be(A,e,f,i,n,t,l,B){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;l=l|0;B=B|0;var a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0;U=s;s=s+3440|0;H=U+3420|0;d=U+3400|0;I=U+3224|0;F=U;k=U+3320|0;M=U+3240|0;b=U+24|0;We(f,A,k,2,B);Ai(k,e,M,I,5,d,5,B);je(f,M,b,B);qf(10,5,5,k,b,d,I,F,B);e=i;B=e+80|0;do{r[e>>1]=0;e=e+2|0}while((e|0)<(B|0));r[t>>1]=65535;r[t+2>>1]=65535;r[t+4>>1]=65535;r[t+6>>1]=65535;r[t+8>>1]=65535;v=0;u=F;c=H;do{A=r[u>>1]|0;u=u+2|0;a=(A*6554|0)>>>15;o=a<<16>>16;e=i+(A<<1)|0;B=r[e>>1]|0;if((r[M+(A<<1)>>1]|0)>0){r[e>>1]=B+4096;r[c>>1]=8192;Q=a}else{r[e>>1]=B+61440;r[c>>1]=-8192;Q=o+8|0}c=c+2|0;g=Q&65535;e=A-(a<<2)-o<<16>>16;a=t+(e<<1)|0;B=r[a>>1]|0;A=B<<16>>16;do{if(B<<16>>16>=0){o=Q<<16>>16;if(!((o^A)&8)){e=t+(e+5<<1)|0;if((A|0)>(o|0)){r[e>>1]=B;r[a>>1]=g;break}else{r[e>>1]=g;break}}else{e=t+(e+5<<1)|0;if((A&7)>>>0>(o&7)>>>0){r[e>>1]=g;break}else{r[e>>1]=B;r[a>>1]=g;break}}}else r[a>>1]=g}while(0);v=v+1<<16>>16}while(v<<16>>16<10);c=H+2|0;v=H+4|0;Q=H+6|0;o=H+8|0;a=H+10|0;e=H+12|0;B=H+14|0;A=H+16|0;C=H+18|0;D=40;E=f+(0-(r[F>>1]|0)<<1)|0;h=f+(0-(r[F+2>>1]|0)<<1)|0;P=f+(0-(r[F+4>>1]|0)<<1)|0;k=f+(0-(r[F+6>>1]|0)<<1)|0;b=f+(0-(r[F+8>>1]|0)<<1)|0;d=f+(0-(r[F+10>>1]|0)<<1)|0;I=f+(0-(r[F+12>>1]|0)<<1)|0;M=f+(0-(r[F+14>>1]|0)<<1)|0;i=f+(0-(r[F+16>>1]|0)<<1)|0;u=f+(0-(r[F+18>>1]|0)<<1)|0;g=n;while(1){Y=(AA(r[H>>1]|0,r[E>>1]|0)|0)>>7;y=(AA(r[c>>1]|0,r[h>>1]|0)|0)>>7;T=(AA(r[v>>1]|0,r[P>>1]|0)|0)>>7;R=(AA(r[Q>>1]|0,r[k>>1]|0)|0)>>7;L=(AA(r[o>>1]|0,r[b>>1]|0)|0)>>7;G=(AA(r[a>>1]|0,r[d>>1]|0)|0)>>7;F=(AA(r[e>>1]|0,r[I>>1]|0)|0)>>7;f=(AA(r[B>>1]|0,r[M>>1]|0)|0)>>>7;n=(AA(r[A>>1]|0,r[i>>1]|0)|0)>>>7;r[g>>1]=(Y+128+y+T+R+L+G+F+f+n+((AA(r[C>>1]|0,r[u>>1]|0)|0)>>>7)|0)>>>8;D=D+-1<<16>>16;if(!(D<<16>>16))break;else{E=E+2|0;h=h+2|0;P=P+2|0;k=k+2|0;b=b+2|0;d=d+2|0;I=I+2|0;M=M+2|0;i=i+2|0;u=u+2|0;g=g+2|0}}e=0;do{B=t+(e<<1)|0;A=r[B>>1]|0;if((e|0)<5)A=(w[l+((A&7)<<1)>>1]|A&8)&65535;else A=r[l+((A&7)<<1)>>1]|0;r[B>>1]=A;e=e+1|0}while((e|0)!=10);s=U;return}function de(A,e,f,i,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0;J=s;s=s+3456|0;T=J+3448|0;L=J+3360|0;U=J+3368|0;v=J+3280|0;R=J+3200|0;G=J;Y=(i&65535)<<17;X=f<<16>>16;y=f<<16>>16<40;if(y){i=Y>>16;f=X;do{o=(AA(r[e+(f-X<<1)>>1]|0,i)|0)>>15;if((o|0)>32767){n[B>>2]=1;o=32767}H=e+(f<<1)|0;r[H>>1]=Ci(r[H>>1]|0,o&65535,B)|0;f=f+1|0}while((f&65535)<<16>>16!=40)}We(e,A,U,1,B);$f(U,R,v,8);je(e,R,G,B);H=L+2|0;r[L>>1]=0;r[H>>1]=1;A=1;o=0;g=1;v=0;Q=-1;do{M=r[2830+(v<<1)>>1]|0;F=M<<16>>16;I=0;do{b=r[2834+(I<<1)>>1]|0;d=b<<16>>16;k=A;h=F;E=g;P=M;D=Q;while(1){a=r[U+(h<<1)>>1]|0;c=r[G+(h*80|0)+(h<<1)>>1]|0;f=d;g=1;C=b;A=b;Q=-1;while(1){i=Ci(a,r[U+(f<<1)>>1]|0,B)|0;i=i<<16>>16;i=(AA(i,i)|0)>>>15;u=(r[G+(h*80|0)+(f<<1)>>1]<<15)+32768+((r[G+(f*80|0)+(f<<1)>>1]|0)+c<<14)|0;if(((AA(i<<16>>16,g<<16>>16)|0)-(AA(u>>16,Q<<16>>16)|0)<<1|0)>0){g=u>>>16&65535;A=C;Q=i&65535}u=f+5|0;C=u&65535;if(C<<16>>16>=40)break;else f=u<<16>>16}if(((AA(Q<<16>>16,E<<16>>16)|0)-(AA(g<<16>>16,D<<16>>16)|0)<<1|0)>0){r[L>>1]=P;r[H>>1]=A;o=P}else{A=k;g=E;Q=D}u=h+5|0;P=u&65535;if(P<<16>>16>=40)break;else{k=A;h=u<<16>>16;E=g;D=Q}}I=I+1|0}while((I|0)!=4);v=v+1|0}while((v|0)!=2);c=A;C=o;i=t;f=i+80|0;do{r[i>>1]=0;i=i+2|0}while((i|0)<(f|0));g=C;f=0;u=0;i=0;while(1){o=g<<16>>16;a=r[R+(o<<1)>>1]|0;A=(o*6554|0)>>>15;g=A<<16;v=g>>15;Q=o-(v+(A<<3)<<16>>17)|0;switch(Q<<16>>16|0){case 0:{v=g>>10;A=1;break}case 1:{if(!((f&65535)<<16>>16))A=0;else{v=A<<22>>16|16;A=1}break}case 2:{v=A<<22>>16|32;A=1;break}case 3:{v=A<<17>>16|1;A=0;break}case 4:{v=A<<22>>16|48;A=1;break}default:{v=A;A=Q&65535}}v=v&65535;Q=t+(o<<1)|0;if(a<<16>>16>0){r[Q>>1]=8191;r[T+(f<<1)>>1]=32767;o=A<<16>>16;if(A<<16>>16<0){lA()}else{G=1<<o;o=(G<<16>>16>>o|0)==1?G&65535:32767}i=Ci(i,o,B)|0}else{r[Q>>1]=-8192;r[T+(f<<1)>>1]=-32768}o=Ci(u,v,B)|0;f=f+1|0;if((f|0)==2){u=o;break}g=r[L+(f<<1)>>1]|0;u=o}r[l>>1]=i;v=T+2|0;g=r[T>>1]|0;A=0;Q=e+(0-(C<<16>>16)<<1)|0;o=e+(0-(c<<16>>16)<<1)|0;do{i=AA(r[Q>>1]|0,g)|0;Q=Q+2|0;if((i|0)!=1073741824?(z=i<<1,!((i|0)>0&(z|0)<0)):0)a=z;else{n[B>>2]=1;a=2147483647}f=AA(r[v>>1]|0,r[o>>1]|0)|0;o=o+2|0;if((f|0)!=1073741824){i=(f<<1)+a|0;if((f^a|0)>0&(i^a|0)<0){n[B>>2]=1;i=(a>>>31)+2147483647|0}}else{n[B>>2]=1;i=2147483647}r[w+(A<<1)>>1]=er(i,B)|0;A=A+1|0}while((A|0)!=40);if(!y){s=J;return u|0}f=Y>>16;i=X;do{a=(AA(r[t+(i-X<<1)>>1]|0,f)|0)>>15;if((a|0)>32767){n[B>>2]=1;a=32767}w=t+(i<<1)|0;r[w>>1]=Ci(r[w>>1]|0,a&65535,B)|0;i=i+1|0}while((i&65535)<<16>>16!=40);s=J;return u|0}function Ie(A,e,f,i,t,w,l,B,a,o){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;o=o|0;var Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0;k=s;s=s+3456|0;c=k+3360|0;C=k+3368|0;D=k+3280|0;E=k+3200|0;h=k;P=t<<16>>16;v=P<<1;if((v|0)==(P<<17>>16|0))u=v;else{n[o>>2]=1;u=t<<16>>16>0?32767:-32768}P=i<<16>>16;Q=i<<16>>16<40;if(Q){t=u<<16>>16;g=P;do{i=f+(g<<1)|0;v=(AA(r[f+(g-P<<1)>>1]|0,t)|0)>>15;if((v|0)>32767){n[o>>2]=1;v=32767}r[i>>1]=Ci(r[i>>1]|0,v&65535,o)|0;g=g+1|0}while((g&65535)<<16>>16!=40)}We(f,e,C,1,o);$f(C,E,D,8);je(f,E,h,o);Me(A,C,h,a,c);v=Fe(A,c,E,w,f,l,B,o)|0;if(!Q){s=k;return v|0}g=u<<16>>16;t=P;do{i=w+(t<<1)|0;Q=(AA(r[w+(t-P<<1)>>1]|0,g)|0)>>15;if((Q|0)>32767){n[o>>2]=1;Q=32767}r[i>>1]=Ci(r[i>>1]|0,Q&65535,o)|0;t=t+1|0}while((t&65535)<<16>>16!=40);s=k;return v|0}function Me(A,e,f,i,n){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;var t=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0;k=n+2|0;r[n>>1]=0;r[k>>1]=1;h=A<<16>>16<<1;t=1;P=0;A=-1;do{E=(P<<3)+h<<16>>16;a=r[i+(E<<1)>>1]|0;E=r[i+((E|1)<<1)>>1]|0;l=a<<16>>16;A:do{if(a<<16>>16<40){D=E<<16>>16;if(E<<16>>16<40)C=t;else while(1){lA()}while(1){u=r[f+(l*80|0)+(l<<1)>>1]|0;v=w[e+(l<<1)>>1]|0;g=D;t=1;c=E;B=E;s=-1;while(1){Q=(w[e+(g<<1)>>1]|0)+v<<16>>16;Q=(AA(Q,Q)|0)>>>15;o=(r[f+(l*80|0)+(g<<1)>>1]<<15)+32768+((r[f+(g*80|0)+(g<<1)>>1]|0)+u<<14)|0;if(((AA(Q<<16>>16,t<<16>>16)|0)-(AA(o>>16,s<<16>>16)|0)<<1|0)>0){t=o>>>16&65535;B=c;s=Q&65535}o=g+5|0;c=o&65535;if(c<<16>>16>=40)break;else g=o<<16>>16}if(((AA(s<<16>>16,C<<16>>16)|0)-(AA(t<<16>>16,A<<16>>16)|0)<<1|0)>0){r[n>>1]=a;r[k>>1]=B;A=s}else t=C;l=l+5|0;a=l&65535;if(a<<16>>16>=40)break;else{l=l<<16>>16;C=t}}}}while(0);P=P+1|0}while((P|0)!=2);return}function Fe(A,e,f,i,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,s=0,o=0,Q=0,g=0,v=0;a=i;s=a+80|0;do{r[a>>1]=0;a=a+2|0}while((a|0)<(s|0));a=r[e>>1]|0;g=(a*6554|0)>>>15;s=g<<16>>16;Q=(748250>>>((a+(AA(s,-5)|0)<<16>>16)+((A<<16>>16)*5|0)|0)&1|0)==0;o=(r[f+(a<<1)>>1]|0)>0;v=o?32767:-32768;r[i+(a<<1)>>1]=o?8191:-8192;a=e+2|0;A=r[a>>1]|0;i=i+(A<<1)|0;if((r[f+(A<<1)>>1]|0)>0){r[i>>1]=8191;f=32767;i=(o&1|2)&65535}else{r[i>>1]=-8192;f=-32768;i=o&1}g=((A*6554|0)>>>15<<3)+(Q?g:s+64|0)&65535;r[l>>1]=i;Q=0;o=t+(0-(r[e>>1]|0)<<1)|0;i=t+(0-(r[a>>1]|0)<<1)|0;do{a=AA(v,r[o>>1]|0)|0;o=o+2|0;if((a|0)==1073741824){n[B>>2]=1;A=2147483647}else A=a<<1;s=AA(f,r[i>>1]|0)|0;i=i+2|0;if((s|0)!=1073741824){a=(s<<1)+A|0;if((s^A|0)>0&(a^A|0)<0){n[B>>2]=1;a=(A>>>31)+2147483647|0}}else{n[B>>2]=1;a=2147483647}r[w+(Q<<1)>>1]=er(a,B)|0;Q=Q+1|0}while((Q|0)!=40);return g|0}function He(A,e,f,i,t,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;var o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0;j=s;s=s+3440|0;X=j+3360|0;J=j+3280|0;m=j+3200|0;O=j;K=(i&65535)<<17;S=f<<16>>16;N=f<<16>>16<40;if(N){f=K>>16;o=S;do{i=(AA(r[e+(o-S<<1)>>1]|0,f)|0)>>15;if((i|0)>32767){n[a>>2]=1;i=32767}z=e+(o<<1)|0;r[z>>1]=Ci(r[z>>1]|0,i&65535,a)|0;o=o+1|0}while((o&65535)<<16>>16!=40)}We(e,A,X,1,a);$f(X,m,J,6);je(e,m,O,a);z=1;Q=2;g=1;i=0;o=1;A=-1;v=1;while(1){Y=2;C=2;while(1){R=0;T=0;y=v;L=C;while(1){if(T<<16>>16<40){F=y<<16>>16;H=y<<16>>16<40;U=L<<16>>16;G=L<<16>>16<40;I=T<<16>>16;M=T;while(1){if((r[J+(I<<1)>>1]|0)>-1){k=r[O+(I*80|0)+(I<<1)>>1]|0;if(H){b=w[X+(I<<1)>>1]|0;P=F;c=1;d=y;f=y;C=0;u=-1;while(1){E=(w[X+(P<<1)>>1]|0)+b|0;h=E<<16>>16;h=(AA(h,h)|0)>>>15;D=(r[O+(I*80|0)+(P<<1)>>1]<<15)+32768+((r[O+(P*80|0)+(P<<1)>>1]|0)+k<<14)|0;if(((AA(h<<16>>16,c<<16>>16)|0)-(AA(D>>16,u<<16>>16)|0)<<1|0)>0){c=D>>>16&65535;f=d;C=E&65535;u=h&65535}D=P+5|0;d=D&65535;if(d<<16>>16>=40)break;else P=D<<16>>16}}else{c=1;f=y;C=0}if(G){b=C&65535;d=f<<16>>16;P=(c<<16>>16<<14)+32768|0;h=U;C=1;k=L;u=L;c=-1;while(1){E=(w[X+(h<<1)>>1]|0)+b<<16>>16;E=(AA(E,E)|0)>>>15;D=P+(r[O+(h*80|0)+(h<<1)>>1]<<12)+((r[O+(I*80|0)+(h<<1)>>1]|0)+(r[O+(d*80|0)+(h<<1)>>1]|0)<<13)|0;if(((AA(E<<16>>16,C<<16>>16)|0)-(AA(D>>16,c<<16>>16)|0)<<1|0)>0){C=D>>>16&65535;u=k;c=E&65535}D=h+5|0;k=D&65535;if(k<<16>>16>=40){P=C;h=c;break}else h=D<<16>>16}}else{P=1;u=L;h=-1}C=AA(h<<16>>16,o<<16>>16)|0;if((C|0)==1073741824){lA()}else D=C<<1;C=AA(P<<16>>16,A<<16>>16)|0;if((C|0)==1073741824){lA()}else c=C<<1;C=D-c|0;if(((C^D)&(c^D)|0)<0){lA()}d=(C|0)>0;Q=d?u:Q;g=d?f:g;i=d?M:i;o=d?P:o;A=d?h:A}C=I+5|0;M=C&65535;if(M<<16>>16>=40)break;else I=C<<16>>16}}R=R+1<<16>>16;if(R<<16>>16>=3)break;else{G=L;L=y;y=T;T=G}}f=Y+2|0;C=f&65535;if(C<<16>>16>=5)break;else Y=f&65535}f=z+2|0;v=f&65535;if(v<<16>>16<4)z=f&65535;else{C=Q;Q=g;break}}f=t;o=f+80|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(o|0));h=i<<16>>16;A=r[m+(h<<1)>>1]|0;i=(h*6554|0)>>>15;f=i<<16;o=h-(((f>>16)*327680|0)>>>16)|0;switch(o<<16>>16|0){case 1:{i=f>>12;break}case 2:{i=f>>8;o=2;break}case 3:{i=i<<20>>16|8;o=1;break}case 4:{i=i<<24>>16|128;o=2;break}default:{}}f=t+(h<<1)|0;if(A<<16>>16>0){r[f>>1]=8191;d=32767;g=65536<<(o<<16>>16)>>>16&65535}else{r[f>>1]=-8192;d=-32768;g=0}D=Q<<16>>16;Q=r[m+(D<<1)>>1]|0;f=(D*6554|0)>>>15;o=f<<16;A=D-(((o>>16)*327680|0)>>>16)|0;switch(A<<16>>16|0){case 1:{f=o>>12;break}case 2:{f=o>>8;A=2;break}case 3:{f=f<<20>>16|8;A=1;break}case 4:{f=f<<24>>16|128;A=2;break}default:{}}o=t+(D<<1)|0;if(Q<<16>>16>0){r[o>>1]=8191;E=32767;g=(65536<<(A<<16>>16)>>>16)+(g&65535)&65535}else{r[o>>1]=-8192;E=-32768}v=f+i|0;c=C<<16>>16;Q=r[m+(c<<1)>>1]|0;i=(c*6554|0)>>>15;f=i<<16;o=c-(((f>>16)*327680|0)>>>16)|0;switch(o<<16>>16|0){case 1:{f=f>>12;break}case 2:{f=f>>8;o=2;break}case 3:{f=i<<20>>16|8;o=1;break}case 4:{f=i<<24>>16|128;o=2;break}default:f=i}i=t+(c<<1)|0;if(Q<<16>>16>0){r[i>>1]=8191;C=32767;i=(65536<<(o<<16>>16)>>>16)+(g&65535)&65535}else{r[i>>1]=-8192;C=-32768;i=g}u=v+f|0;r[B>>1]=i;g=0;v=e+(0-h<<1)|0;A=e+(0-D<<1)|0;Q=e+(0-c<<1)|0;do{i=AA(r[v>>1]|0,d)|0;v=v+2|0;if((i|0)!=1073741824?(x=i<<1,!((i|0)>0&(x|0)<0)):0)o=x;else{n[a>>2]=1;o=2147483647}i=AA(r[A>>1]|0,E)|0;A=A+2|0;if((i|0)!=1073741824){f=(i<<1)+o|0;if((i^o|0)>0&(f^o|0)<0){n[a>>2]=1;f=(o>>>31)+2147483647|0}}else{n[a>>2]=1;f=2147483647}o=AA(r[Q>>1]|0,C)|0;Q=Q+2|0;if((o|0)!=1073741824){i=(o<<1)+f|0;if((o^f|0)>0&(i^f|0)<0){n[a>>2]=1;i=(f>>>31)+2147483647|0}}else{n[a>>2]=1;i=2147483647}r[l+(g<<1)>>1]=er(i,a)|0;g=g+1|0}while((g|0)!=40);i=u&65535;if(!N){s=j;return i|0}o=K>>16;f=S;do{A=(AA(r[t+(f-S<<1)>>1]|0,o)|0)>>15;if((A|0)>32767){n[a>>2]=1;A=32767}l=t+(f<<1)|0;r[l>>1]=Ci(r[l>>1]|0,A&65535,a)|0;f=f+1|0}while((f&65535)<<16>>16!=40);s=j;return i|0}function Ue(A,e,f,i,t,l,B,a,o){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;var Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,eA=0,fA=0,iA=0;iA=s;s=s+3456|0;_=iA+3448|0;V=iA+3360|0;j=iA+3368|0;p=iA+3280|0;Z=iA+3200|0;W=iA;$=(i&65535)<<17;fA=f<<16>>16;q=f<<16>>16<40;if(q){f=$>>16;Q=fA;do{i=(AA(r[e+(Q-fA<<1)>>1]|0,f)|0)>>15;if((i|0)>32767){n[o>>2]=1;i=32767}S=e+(Q<<1)|0;r[S>>1]=Ci(r[S>>1]|0,i&65535,o)|0;Q=Q+1|0}while((Q&65535)<<16>>16!=40)}We(e,A,j,1,o);$f(j,Z,p,4);je(e,Z,W,o);K=V+2|0;r[V>>1]=0;x=V+4|0;r[K>>1]=1;S=V+6|0;r[x>>1]=2;r[S>>1]=3;c=3;v=2;g=1;i=0;f=1;Q=-1;u=3;do{X=0;J=0;O=u;m=1;N=2;while(1){if(J<<16>>16<40){L=m<<16>>16;R=m<<16>>16<40;T=N<<16>>16;y=N<<16>>16<40;Y=O<<16>>16;z=O<<16>>16<40;G=J<<16>>16;U=v;F=g;M=f;H=J;while(1){if((r[p+(G<<1)>>1]|0)>-1){D=r[W+(G*80|0)+(G<<1)>>1]|0;if(R){C=w[j+(G<<1)>>1]|0;E=L;d=1;v=m;g=m;k=0;b=-1;while(1){P=(w[j+(E<<1)>>1]|0)+C|0;h=P<<16>>16;h=(AA(h,h)|0)>>>15;I=(r[W+(G*80|0)+(E<<1)>>1]<<15)+32768+((r[W+(E*80|0)+(E<<1)>>1]|0)+D<<14)|0;if(((AA(h<<16>>16,d<<16>>16)|0)-(AA(I>>16,b<<16>>16)|0)<<1|0)>0){d=I>>>16&65535;g=v;k=P&65535;b=h&65535}I=E+5|0;v=I&65535;if(v<<16>>16>=40)break;else E=I<<16>>16}}else{d=1;g=m;k=0}if(y){f=k&65535;A=g<<16>>16;D=(d<<16>>16<<14)+32768|0;E=T;I=1;C=N;v=N;b=0;k=-1;while(1){P=(w[j+(E<<1)>>1]|0)+f|0;h=P<<16>>16;h=(AA(h,h)|0)>>>15;d=D+(r[W+(E*80|0)+(E<<1)>>1]<<12)+((r[W+(G*80|0)+(E<<1)>>1]|0)+(r[W+(A*80|0)+(E<<1)>>1]|0)<<13)|0;if(((AA(h<<16>>16,I<<16>>16)|0)-(AA(d>>16,k<<16>>16)|0)<<1|0)>0){I=d>>>16&65535;v=C;b=P&65535;k=h&65535}d=E+5|0;C=d&65535;if(C<<16>>16>=40)break;else E=d<<16>>16}}else{I=1;v=N;b=0}if(z){D=b&65535;C=v<<16>>16;A=g<<16>>16;h=(I&65535)<<16|32768;P=Y;f=1;E=O;d=O;I=-1;while(1){k=(w[j+(P<<1)>>1]|0)+D<<16>>16;k=(AA(k,k)|0)>>>15;b=(r[W+(P*80|0)+(P<<1)>>1]<<12)+h+((r[W+(A*80|0)+(P<<1)>>1]|0)+(r[W+(C*80|0)+(P<<1)>>1]|0)+(r[W+(G*80|0)+(P<<1)>>1]|0)<<13)|0;if(((AA(k<<16>>16,f<<16>>16)|0)-(AA(b>>16,I<<16>>16)|0)<<1|0)>0){f=b>>>16&65535;d=E;I=k&65535}b=P+5|0;E=b&65535;if(E<<16>>16>=40)break;else P=b<<16>>16}}else{f=1;d=O;I=-1}if(((AA(I<<16>>16,M<<16>>16)|0)-(AA(f<<16>>16,Q<<16>>16)|0)<<1|0)>0){r[V>>1]=H;r[K>>1]=g;r[x>>1]=v;r[S>>1]=d;c=d;i=H;Q=I}else{v=U;g=F;f=M}}else{v=U;g=F;f=M}P=G+5|0;H=P&65535;if(H<<16>>16>=40)break;else{G=P<<16>>16;U=v;F=g;M=f}}}X=X+1<<16>>16;if(X<<16>>16>=4)break;else{Y=N;z=O;N=m;m=J;O=Y;J=z}}u=u+1<<16>>16}while(u<<16>>16<5);I=c;d=v;b=g;k=i;i=t;f=i+80|0;do{r[i>>1]=0;i=i+2|0}while((i|0)<(f|0));A=k;f=0;Q=0;i=0;while(1){v=A<<16>>16;u=r[Z+(v<<1)>>1]|0;A=v*13108>>16;g=v-((A*327680|0)>>>16)|0;A=r[a+(A<<1)>>1]|0;switch(g<<16>>16|0){case 1:{c=A<<16>>16<<3&65535;break}case 2:{c=A<<16>>16<<6&65535;break}case 3:{c=A<<16>>16<<10&65535;break}case 4:{c=((A&65535)<<10|512)&65535;g=3;break}default:c=A}A=t+(v<<1)|0;if(u<<16>>16>0){r[A>>1]=8191;A=32767;i=(65536<<(g<<16>>16)>>>16)+(i&65535)&65535}else{r[A>>1]=-8192;A=-32768}r[_+(f<<1)>>1]=A;Q=(c&65535)+(Q&65535)|0;f=f+1|0;if((f|0)==4){P=Q;break}A=r[V+(f<<1)>>1]|0}r[B>>1]=i;D=_+2|0;E=_+4|0;h=_+6|0;A=r[_>>1]|0;C=0;g=e+(0-(k<<16>>16)<<1)|0;v=e+(0-(b<<16>>16)<<1)|0;u=e+(0-(d<<16>>16)<<1)|0;c=e+(0-(I<<16>>16)<<1)|0;do{i=AA(r[g>>1]|0,A)|0;g=g+2|0;if((i|0)!=1073741824?(eA=i<<1,!((i|0)>0&(eA|0)<0)):0)Q=eA;else{n[o>>2]=1;Q=2147483647}i=AA(r[D>>1]|0,r[v>>1]|0)|0;v=v+2|0;if((i|0)!=1073741824){f=(i<<1)+Q|0;if((i^Q|0)>0&(f^Q|0)<0){n[o>>2]=1;f=(Q>>>31)+2147483647|0}}else{n[o>>2]=1;f=2147483647}i=AA(r[E>>1]|0,r[u>>1]|0)|0;u=u+2|0;if((i|0)!=1073741824){Q=(i<<1)+f|0;if((i^f|0)>0&(Q^f|0)<0){n[o>>2]=1;Q=(f>>>31)+2147483647|0}}else{n[o>>2]=1;Q=2147483647}f=AA(r[h>>1]|0,r[c>>1]|0)|0;c=c+2|0;if((f|0)!=1073741824){i=(f<<1)+Q|0;if((f^Q|0)>0&(i^Q|0)<0){n[o>>2]=1;i=(Q>>>31)+2147483647|0}}else{n[o>>2]=1;i=2147483647}r[l+(C<<1)>>1]=er(i,o)|0;C=C+1|0}while((C|0)!=40);i=P&65535;if(((fA<<16)+-2621440|0)>-1|q^1){s=iA;return i|0}Q=$>>16;f=fA;do{A=(AA(r[t+(f-fA<<1)>>1]|0,Q)|0)>>15;if((A|0)>32767){n[o>>2]=1;A=32767}l=t+(f<<1)|0;r[l>>1]=Ci(r[l>>1]|0,A&65535,o)|0;f=f+1|0}while((f&65535)<<16>>16!=40);s=iA;return i|0}function Ge(A,e,f,i,t,l,B){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;var a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0;z=s;s=s+3440|0;D=z+3424|0;L=z+3408|0;R=z+3240|0;E=z+3224|0;U=z+3328|0;C=z+3248|0;G=z+24|0;Y=z+16|0;y=z;pe(f,A,U,2,4,4,B);Ai(U,e,C,R,4,L,4,B);je(f,C,G,B);qf(8,4,4,U,G,L,R,E,B);e=i;A=e+80|0;do{r[e>>1]=0;e=e+2|0}while((e|0)<(A|0));r[y>>1]=-1;r[Y>>1]=-1;F=y+2|0;r[F>>1]=-1;H=Y+2|0;r[H>>1]=-1;U=y+4|0;r[U>>1]=-1;G=Y+4|0;r[G>>1]=-1;R=y+6|0;r[R>>1]=-1;L=Y+6|0;r[L>>1]=-1;u=0;do{g=r[E+(u<<1)>>1]|0;e=g>>>2;o=e&65535;A=g&3;Q=(r[C+(g<<1)>>1]|0)>0;g=i+(g<<1)|0;c=Q&1^1;r[g>>1]=(w[g>>1]|0)+(Q?8191:57345);r[D+(u<<1)>>1]=Q?32767:-32768;Q=y+(A<<1)|0;g=r[Q>>1]|0;do{if(g<<16>>16>=0){v=Y+(A<<1)|0;a=(g<<16>>16|0)<=(e<<16>>16|0);e=y+((A|4)<<1)|0;if((c&65535|0)==(w[v>>1]&1|0))if(a){r[e>>1]=o;break}else{r[e>>1]=g;r[Q>>1]=o;r[v>>1]=c;break}else if(a){r[e>>1]=g;r[Q>>1]=o;r[v>>1]=c;break}else{r[e>>1]=o;break}}else{r[Q>>1]=o;r[Y+(A<<1)>>1]=c}}while(0);u=u+1|0}while((u|0)!=8);h=D+2|0;P=D+4|0;k=D+6|0;b=D+8|0;d=D+10|0;I=D+12|0;M=D+14|0;D=r[D>>1]|0;u=0;v=f+(0-(r[E>>1]|0)<<1)|0;g=f+(0-(r[E+2>>1]|0)<<1)|0;Q=f+(0-(r[E+4>>1]|0)<<1)|0;o=f+(0-(r[E+6>>1]|0)<<1)|0;e=f+(0-(r[E+8>>1]|0)<<1)|0;A=f+(0-(r[E+10>>1]|0)<<1)|0;a=f+(0-(r[E+12>>1]|0)<<1)|0;f=f+(0-(r[E+14>>1]|0)<<1)|0;do{c=AA(r[v>>1]|0,D)|0;v=v+2|0;if((c|0)!=1073741824?(T=c<<1,!((c|0)>0&(T|0)<0)):0)c=T;else{n[B>>2]=1;c=2147483647}C=AA(r[h>>1]|0,r[g>>1]|0)|0;g=g+2|0;if((C|0)!=1073741824){i=(C<<1)+c|0;if((C^c|0)>0&(i^c|0)<0){n[B>>2]=1;c=(c>>>31)+2147483647|0}else c=i}else{n[B>>2]=1;c=2147483647}C=AA(r[P>>1]|0,r[Q>>1]|0)|0;Q=Q+2|0;if((C|0)!=1073741824){i=(C<<1)+c|0;if((C^c|0)>0&(i^c|0)<0){n[B>>2]=1;i=(c>>>31)+2147483647|0}}else{n[B>>2]=1;i=2147483647}C=AA(r[k>>1]|0,r[o>>1]|0)|0;o=o+2|0;if((C|0)!=1073741824){c=(C<<1)+i|0;if((C^i|0)>0&(c^i|0)<0){n[B>>2]=1;c=(i>>>31)+2147483647|0}}else{n[B>>2]=1;c=2147483647}C=AA(r[b>>1]|0,r[e>>1]|0)|0;e=e+2|0;if((C|0)!=1073741824){i=(C<<1)+c|0;if((C^c|0)>0&(i^c|0)<0){n[B>>2]=1;i=(c>>>31)+2147483647|0}}else{n[B>>2]=1;i=2147483647}C=AA(r[d>>1]|0,r[A>>1]|0)|0;A=A+2|0;if((C|0)!=1073741824){c=(C<<1)+i|0;if((C^i|0)>0&(c^i|0)<0){n[B>>2]=1;c=(i>>>31)+2147483647|0}}else{n[B>>2]=1;c=2147483647}C=AA(r[I>>1]|0,r[a>>1]|0)|0;a=a+2|0;if((C|0)!=1073741824){i=(C<<1)+c|0;if((C^c|0)>0&(i^c|0)<0){n[B>>2]=1;i=(c>>>31)+2147483647|0}}else{n[B>>2]=1;i=2147483647}C=AA(r[M>>1]|0,r[f>>1]|0)|0;f=f+2|0;if((C|0)!=1073741824){c=(C<<1)+i|0;if((C^i|0)>0&(c^i|0)<0){n[B>>2]=1;c=(i>>>31)+2147483647|0}}else{n[B>>2]=1;c=2147483647}r[t+(u<<1)>>1]=er(c,B)|0;u=u+1|0}while((u|0)!=40);r[l>>1]=r[Y>>1]|0;r[l+2>>1]=r[H>>1]|0;r[l+4>>1]=r[G>>1]|0;r[l+6>>1]=r[L>>1]|0;A=r[y>>1]|0;e=r[y+8>>1]|0;a=r[F>>1]|0;r[l+8>>1]=e<<1&2|A&1|a<<2&4|(((e>>1)*327680|0)+(A>>>1<<16)+(AA(a>>1,1638400)|0)|0)>>>13&65528;a=r[U>>1]|0;A=r[y+12>>1]|0;e=r[y+10>>1]|0;r[l+10>>1]=A<<1&2|a&1|e<<2&4|(((A>>1)*327680|0)+(a>>>1<<16)+(AA(e>>1,1638400)|0)|0)>>>13&65528;e=r[y+14>>1]|0;a=r[R>>1]|0;A=a<<16>>16>>>1;if(!(e&2)){t=A;B=e<<16>>16;Y=B>>1;Y=Y*327680|0;t=t<<16;Y=t+Y|0;Y=Y<<5;Y=Y>>16;Y=Y|12;Y=Y*2622|0;Y=Y>>>16;t=a&65535;t=t&1;B=B<<17;B=B&131072;Y=Y<<18;B=Y|B;B=B>>>16;t=B|t;t=t&65535;l=l+12|0;r[l>>1]=t;s=z;return}t=4-(A<<16>>16)|0;B=e<<16>>16;Y=B>>1;Y=Y*327680|0;t=t<<16;Y=t+Y|0;Y=Y<<5;Y=Y>>16;Y=Y|12;Y=Y*2622|0;Y=Y>>>16;t=a&65535;t=t&1;B=B<<17;B=B&131072;Y=Y<<18;B=Y|B;B=B>>>16;t=B|t;t=t&65535;l=l+12|0;r[l>>1]=t;s=z;return}function Le(A,e,f,i,t){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;var w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0;c=f<<16>>16;w=0-c|0;f=t+(w<<2)|0;t=((c-(i<<16>>16)|0)>>>2)+1&65535;if(t<<16>>16<=0)return;c=e<<16>>16>>>1&65535;if(!(c<<16>>16)){lA()}u=A+(w<<1)|0;while(1){s=u+4|0;Q=r[s>>1]|0;B=r[u>>1]|0;o=Q;a=c;g=A;v=u;u=u+8|0;l=0;w=0;i=0;e=0;while(1){D=r[g>>1]|0;C=(AA(B<<16>>16,D)|0)+l|0;l=r[v+2>>1]|0;w=(AA(l,D)|0)+w|0;B=(AA(o<<16>>16,D)|0)+i|0;i=r[v+6>>1]|0;o=(AA(i,D)|0)+e|0;e=r[g+2>>1]|0;l=C+(AA(e,l)|0)|0;w=w+(AA(Q<<16>>16,e)|0)|0;s=s+4|0;i=B+(AA(e,i)|0)|0;B=r[s>>1]|0;e=o+(AA(B<<16>>16,e)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;D=Q;o=B;Q=r[v+8>>1]|0;g=g+4|0;v=v+4|0;B=D}n[f>>2]=l<<1;n[f+4>>2]=w<<1;n[f+8>>2]=i<<1;n[f+12>>2]=e<<1;if(t<<16>>16<=1)break;else{f=f+16|0;t=t+-1<<16>>16}}return}function Re(A,e,f,i,t,l,B,a,o){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;var Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0;b=s;s=s+16|0;P=b+2|0;k=b;do{if(t<<16>>16>0){C=i<<16>>16;E=0;v=0;i=0;g=0;D=0;while(1){Q=r[A+(E<<1)>>1]|0;u=Q<<16>>16;v=(AA(u,u)|0)+v|0;u=r[e+(E<<1)>>1]|0;i=(AA(u,u)|0)+i|0;g=(AA(r[f+(E<<1)>>1]|0,u)|0)+g|0;u=AA(u,C)|0;if((u|0)==1073741824){n[o>>2]=1;c=2147483647}else c=u<<1;u=c<<1;u=(nr(Q,er((u>>1|0)==(c|0)?u:c>>31^2147483647,o)|0,o)|0)<<16>>16;u=AA(u,u)|0;if((u|0)!=1073741824){Q=(u<<1)+D|0;if((u^D|0)>0&(Q^D|0)<0){n[o>>2]=1;Q=(D>>>31)+2147483647|0}}else{n[o>>2]=1;Q=2147483647}E=E+1|0;if((E&65535)<<16>>16==t<<16>>16){D=Q;break}else D=Q}v=v<<1;i=i<<1;g=g<<1;if((v|0)>=0){if((v|0)<400){Q=D;h=14;break}}else{n[o>>2]=1;v=2147483647}c=Ni(v)|0;u=c<<16>>16;if(c<<16>>16>0){Q=v<<u;if((Q>>u|0)!=(v|0))Q=v>>31^2147483647}else{Q=0-u<<16;if((Q|0)<2031616)Q=v>>(Q>>16);else Q=0}r[l>>1]=Q>>>16;v=i;C=g;Q=D;i=15-(c&65535)&65535}else{i=0;g=0;Q=0;h=14}}while(0);if((h|0)==14){r[l>>1]=0;v=i;C=g;i=-15}r[B>>1]=i;if((v|0)<0){n[o>>2]=1;v=2147483647}u=Ni(v)|0;g=u<<16>>16;if(u<<16>>16>0){i=v<<g;if((i>>g|0)!=(v|0))i=v>>31^2147483647}else{i=0-g<<16;if((i|0)<2031616)i=v>>(i>>16);else i=0}r[l+2>>1]=i>>>16;r[B+2>>1]=15-(u&65535);v=Ni(C)|0;g=v<<16>>16;if(v<<16>>16>0){i=C<<g;if((i>>g|0)!=(C|0))i=C>>31^2147483647}else{i=0-g<<16;if((i|0)<2031616)i=C>>(i>>16);else i=0}r[l+4>>1]=i>>>16;r[B+4>>1]=2-(v&65535);v=Ni(Q)|0;i=v<<16>>16;if(v<<16>>16>0){g=Q<<i;if((g>>i|0)!=(Q|0))g=Q>>31^2147483647}else{i=0-i<<16;if((i|0)<2031616)g=Q>>(i>>16);else g=0}i=g>>>16&65535;Q=15-(v&65535)&65535;r[l+6>>1]=i;r[B+6>>1]=Q;if((g>>16|0)<=0){o=0;r[a>>1]=o;s=b;return}g=r[l>>1]|0;if(!(g<<16>>16)){o=0;r[a>>1]=o;s=b;return}i=Ei(fr(g,1,o)|0,i)|0;i=(i&65535)<<16;g=((nr(Q,r[B>>1]|0,o)|0)&65535)+3|0;Q=g&65535;g=g<<16>>16;if(Q<<16>>16>0)Q=Q<<16>>16<31?i>>g:0;else{B=0-g<<16>>16;Q=i<<B;Q=(Q>>B|0)==(i|0)?Q:i>>31^2147483647}Gi(Q,P,k,o);k=cf((w[P>>1]|0)+65509&65535,r[k>>1]|0,o)|0;P=k<<13;o=er((P>>13|0)==(k|0)?P:k>>31^2147483647,o)|0;r[a>>1]=o;s=b;return}function Te(A,e,f,i,t,l,B,a,o,Q,g){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;var v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0;b=s;s=s+80|0;h=b;r[B>>1]=r[l>>1]|0;r[a>>1]=r[l+2>>1]|0;c=r[l+4>>1]|0;if(c<<16>>16==-32768)c=32767;else c=0-(c&65535)&65535;r[B+2>>1]=c;r[a+2>>1]=(w[l+6>>1]|0)+1;switch(A|0){case 0:case 5:{E=0;u=0;v=0;D=0;break}default:{E=0;u=1;v=1;D=1}}while(1){C=(r[t+(E<<1)>>1]|0)>>>3;r[h+(E<<1)>>1]=C;C=C<<16>>16;c=AA(C,C)|0;if((c|0)!=1073741824){l=(c<<1)+u|0;if((c^u|0)>0&(l^u|0)<0){n[g>>2]=1;u=(u>>>31)+2147483647|0}else u=l}else{n[g>>2]=1;u=2147483647}c=AA(r[e+(E<<1)>>1]|0,C)|0;if((c|0)!=1073741824){l=(c<<1)+v|0;if((c^v|0)>0&(l^v|0)<0){n[g>>2]=1;v=(v>>>31)+2147483647|0}else v=l}else{n[g>>2]=1;v=2147483647}c=AA(r[i+(E<<1)>>1]|0,C)|0;if((c|0)!=1073741824){l=(c<<1)+D|0;if((c^D|0)>0&(l^D|0)<0){n[g>>2]=1;l=(D>>>31)+2147483647|0}}else{n[g>>2]=1;l=2147483647}E=E+1|0;if((E|0)==40){i=l;C=v;break}else D=l}v=Ni(u)|0;l=v<<16>>16;if(v<<16>>16>0){c=u<<l;if((c>>l|0)!=(u|0))c=u>>31^2147483647}else{c=0-l<<16;if((c|0)<2031616)c=u>>(c>>16);else c=0}t=B+4|0;r[t>>1]=c>>>16;e=a+4|0;r[e>>1]=-3-(v&65535);u=Ni(C)|0;l=u<<16>>16;if(u<<16>>16>0){c=C<<l;if((c>>l|0)!=(C|0))c=C>>31^2147483647}else{c=0-l<<16;if((c|0)<2031616)c=C>>(c>>16);else c=0}l=c>>>16;r[B+6>>1]=(l|0)==32768?32767:0-l&65535;r[a+6>>1]=7-(u&65535);u=Ni(i)|0;l=u<<16>>16;if(u<<16>>16>0){c=i<<l;if((c>>l|0)!=(i|0))c=i>>31^2147483647}else{c=0-l<<16;if((c|0)<2031616)c=i>>(c>>16);else c=0}r[B+8>>1]=c>>>16;r[a+8>>1]=7-(u&65535);switch(A|0){case 0:case 5:{c=0;v=0;break}default:{s=b;return}}do{v=(AA(r[h+(c<<1)>>1]|0,r[f+(c<<1)>>1]|0)|0)+v|0;c=c+1|0}while((c|0)!=40);l=v<<1;c=Ni(l)|0;u=c<<16>>16;if(c<<16>>16>0){v=l<<u;if((v>>u|0)==(l|0)){P=v;k=40}else{P=l>>31^2147483647;k=40}}else{v=0-u<<16;if((v|0)<2031616){P=l>>(v>>16);k=40}}if((k|0)==40?(P>>16|0)>=1:0){g=fr(P>>>16&65535,1,g)|0;r[o>>1]=Ei(g,r[t>>1]|0)|0;r[Q>>1]=65528-(c&65535)-(w[e>>1]|0);s=b;return}r[o>>1]=0;r[Q>>1]=0;s=b;return}function ye(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var t=0,w=0,l=0;w=0;t=0;do{l=r[A+(w<<1)>>1]|0;t=(AA(l,l)|0)+t|0;w=w+1|0}while((w|0)!=40);if((t|0)<0){n[i>>2]=1;t=2147483647}i=Ni(t)|0;A=i<<16>>16;if(i<<16>>16>0){w=t<<A;if((w>>A|0)==(t|0))t=w;else t=t>>31^2147483647}else{A=0-A<<16;if((A|0)<2031616)t=t>>(A>>16);else t=0}r[f>>1]=t>>>16;r[e>>1]=16-(i&65535);return}function Ye(A,e,f,i,t,w,l,B,a,o,Q,g,v){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;v=v|0;var u=0,c=0,C=0,D=0;c=s;s=s+16|0;u=c;if(o>>>0<2){l=Ie(Q,A,e,f,i,l,B,u,n[g+76>>2]|0,v)|0;v=n[a>>2]|0;r[v>>1]=l;l=r[u>>1]|0;n[a>>2]=v+4;r[v+2>>1]=l;s=c;return}switch(o|0){case 2:{l=de(A,e,f,i,l,B,u,v)|0;v=n[a>>2]|0;r[v>>1]=l;l=r[u>>1]|0;n[a>>2]=v+4;r[v+2>>1]=l;s=c;return}case 3:{l=He(A,e,f,i,l,B,u,v)|0;v=n[a>>2]|0;r[v>>1]=l;l=r[u>>1]|0;n[a>>2]=v+4;r[v+2>>1]=l;s=c;return}default:{if((o&-2|0)==4){l=Ue(A,e,f,i,l,B,u,n[g+36>>2]|0,v)|0;v=n[a>>2]|0;r[v>>1]=l;l=r[u>>1]|0;n[a>>2]=v+4;r[v+2>>1]=l;s=c;return}if((o|0)!=6){Q=t<<16>>16;Q=(Q<<17>>17|0)==(Q|0)?Q<<1:Q>>>15^32767;t=f<<16>>16<40;if(!t){be(A,w,e,l,B,n[a>>2]|0,n[g+36>>2]|0,v);n[a>>2]=(n[a>>2]|0)+20;s=c;return}u=f<<16>>16;o=Q<<16>>16;i=u;do{D=(AA(r[e+(i-u<<1)>>1]|0,o)|0)>>>15&65535;C=e+(i<<1)|0;r[C>>1]=Ci(r[C>>1]|0,D,v)|0;i=i+1|0}while((i&65535)<<16>>16!=40);be(A,w,e,l,B,n[a>>2]|0,n[g+36>>2]|0,v);n[a>>2]=(n[a>>2]|0)+20;if(!t){s=c;return}t=f<<16>>16;o=Q<<16>>16;u=t;do{i=(AA(r[l+(u-t<<1)>>1]|0,o)|0)>>15;if((i|0)>32767){n[v>>2]=1;i=32767}D=l+(u<<1)|0;r[D>>1]=Ci(r[D>>1]|0,i&65535,v)|0;u=u+1|0}while((u&65535)<<16>>16!=40);s=c;return}g=i<<16>>16;g=(g<<17>>17|0)==(g|0)?g<<1:g>>>15^32767;Q=f<<16>>16<40;if(!Q){Ge(A,w,e,l,B,n[a>>2]|0,v);n[a>>2]=(n[a>>2]|0)+14;s=c;return}u=f<<16>>16;o=g<<16>>16;i=u;do{t=(AA(r[e+(i-u<<1)>>1]|0,o)|0)>>15;if((t|0)>32767){n[v>>2]=1;t=32767}D=e+(i<<1)|0;r[D>>1]=Ci(r[D>>1]|0,t&65535,v)|0;i=i+1|0}while((i&65535)<<16>>16!=40);Ge(A,w,e,l,B,n[a>>2]|0,v);n[a>>2]=(n[a>>2]|0)+14;if(!Q){s=c;return}t=f<<16>>16;o=g<<16>>16;u=t;do{i=(AA(r[l+(u-t<<1)>>1]|0,o)|0)>>15;if((i|0)>32767){n[v>>2]=1;i=32767}D=l+(u<<1)|0;r[D>>1]=Ci(r[D>>1]|0,i&65535,v)|0;u=u+1|0}while((u&65535)<<16>>16!=40);s=c;return}}}function ze(A){A=A|0;var e=0;if(!A){A=-1;return A|0}n[A>>2]=0;e=lr(4)|0;if(!e){A=-1;return A|0}if(!((Hf(e)|0)<<16>>16)){Uf(n[e>>2]|0)|0;n[A>>2]=e;A=0;return A|0}else{Gf(e);Br(e);A=-1;return A|0}return 0}function Xe(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Gf(e);Br(n[A>>2]|0);n[A>>2]=0;return}function Je(A){A=A|0;if(!A){A=-1;return A|0}Uf(n[A>>2]|0)|0;A=0;return A|0}function Oe(A,e,f,i,t,l,B,a,o,Q,g,v,u,c,C,D,E,h,P,k){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;D=D|0;E=E|0;h=h|0;P=P|0;k=k|0;var b=0,d=0,I=0,M=0;d=s;s=s+16|0;M=d+2|0;I=d;r[u>>1]=Lf(n[A>>2]|0,f,t,B,o,l,40,i,c,I,M,k)|0;A=r[M>>1]|0;i=n[E>>2]|0;n[E>>2]=i+2;r[i>>1]=A;Si(B,r[u>>1]|0,r[c>>1]|0,40,r[I>>1]|0,k);Se(B,l,v,40);r[C>>1]=Qf(f,o,v,D,40,k)|0;r[h>>1]=32767;if(Q<<16>>16!=0?(b=r[C>>1]|0,b<<16>>16>15565):0)b=Qi(e,b,k)|0;else b=0;if(f>>>0<2){M=r[C>>1]|0;r[C>>1]=M<<16>>16>13926?13926:M;if(b<<16>>16)r[h>>1]=15565}else{if(b<<16>>16){r[h>>1]=15565;r[C>>1]=15565}if((f|0)==7){I=Zf(7,r[h>>1]|0,C,0,0,P,k)|0;M=n[E>>2]|0;n[E>>2]=M+2;r[M>>1]=I}}u=r[C>>1]|0;b=0;while(1){I=AA(r[v>>1]|0,u)|0;r[g>>1]=(w[o>>1]|0)-(I>>>14);I=(AA(r[B>>1]|0,u)|0)>>>14;M=a+(b<<1)|0;r[M>>1]=(w[M>>1]|0)-I;b=b+1|0;if((b|0)==40)break;else{B=B+2|0;o=o+2|0;g=g+2|0;v=v+2|0}}s=d;return}function me(A,e){A=A|0;e=e|0;var f=0,i=0,r=0,t=0;t=s;s=s+16|0;r=t;if(!A){A=-1;s=t;return A|0}n[A>>2]=0;f=lr(2532)|0;n[r>>2]=f;if(!f){A=-1;s=t;return A|0}bi(f+2392|0);n[f+2188>>2]=0;n[(n[r>>2]|0)+2192>>2]=0;n[(n[r>>2]|0)+2196>>2]=0;n[(n[r>>2]|0)+2200>>2]=0;n[(n[r>>2]|0)+2204>>2]=0;n[(n[r>>2]|0)+2208>>2]=0;n[(n[r>>2]|0)+2212>>2]=0;n[(n[r>>2]|0)+2220>>2]=0;i=n[r>>2]|0;n[i+2216>>2]=e;n[i+2528>>2]=0;f=i;if((((((((ze(i+2196|0)|0)<<16>>16==0?(Yi(i+2192|0)|0)<<16>>16==0:0)?(lf(i+2200|0)|0)<<16>>16==0:0)?(yf(i+2204|0)|0)<<16>>16==0:0)?(Bi(i+2208|0)|0)<<16>>16==0:0)?(vi(i+2212|0)|0)<<16>>16==0:0)?(Ze(i+2220|0,n[i+2432>>2]|0)|0)<<16>>16==0:0)?(bf(i+2188|0)|0)<<16>>16==0:0){Ke(i)|0;n[A>>2]=f;A=0;s=t;return A|0}Ne(r);A=-1;s=t;return A|0}function Ne(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;df(e+2188|0);Xi((n[A>>2]|0)+2192|0);Bf((n[A>>2]|0)+2200|0);Xe((n[A>>2]|0)+2196|0);zf((n[A>>2]|0)+2204|0);si((n[A>>2]|0)+2208|0);ci((n[A>>2]|0)+2212|0);qe((n[A>>2]|0)+2220|0);Br(n[A>>2]|0);n[A>>2]=0;return}function Ke(A){A=A|0;var e=0,f=0,i=0,t=0;if(!A){t=-1;return t|0}n[A+652>>2]=A+320;n[A+640>>2]=A+240;n[A+644>>2]=A+160;n[A+648>>2]=A+80;n[A+1264>>2]=A+942;n[A+1912>>2]=A+1590;i=A+1938|0;n[A+2020>>2]=i;n[A+2384>>2]=A+2304;e=A+2028|0;n[A+2024>>2]=A+2108;n[A+2528>>2]=0;gr(A|0,0,640)|0;gr(A+1282|0,0,308)|0;gr(A+656|0,0,286)|0;f=A+2224|0;t=i+80|0;do{r[i>>1]=0;i=i+2|0}while((i|0)<(t|0));i=e;t=i+80|0;do{r[i>>1]=0;i=i+2|0}while((i|0)<(t|0));e=A+1268|0;i=f;t=i+80|0;do{r[i>>1]=0;i=i+2|0}while((i|0)<(t|0));r[e>>1]=40;r[A+1270>>1]=40;r[A+1272>>1]=40;r[A+1274>>1]=40;r[A+1276>>1]=40;If(n[A+2188>>2]|0)|0;zi(n[A+2192>>2]|0)|0;Je(n[A+2196>>2]|0)|0;af(n[A+2200>>2]|0)|0;Yf(n[A+2204>>2]|0)|0;ai(n[A+2208>>2]|0)|0;ui(n[A+2212>>2]|0)|0;_e(n[A+2220>>2]|0,n[A+2432>>2]|0)|0;r[A+2388>>1]=0;t=0;return t|0}function xe(A,e,f,i,t,w){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;var l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,AA=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0,wA=0,BA=0,aA=0,sA=0,oA=0,QA=0,gA=0,vA=0,uA=0,cA=0;cA=s;s=s+1184|0;S=cA;Q=cA+1096|0;g=cA+1008|0;a=cA+904|0;sA=cA+928|0;oA=cA+824|0;V=cA+744|0;gA=cA+664|0;vA=cA+584|0;_=cA+328|0;wA=cA+504|0;BA=cA+424|0;QA=cA+344|0;uA=cA+248|0;Z=cA+168|0;iA=cA+88|0;nA=cA+68|0;tA=cA+48|0;rA=cA+28|0;aA=cA+24|0;eA=cA+22|0;$=cA+20|0;W=cA+16|0;j=cA+12|0;p=cA+10|0;AA=cA+8|0;q=cA+6|0;fA=cA+4|0;n[S>>2]=i;x=A+2528|0;l=A+652|0;or(n[l>>2]|0,f|0,320)|0;n[t>>2]=e;o=A+2216|0;if(!(n[o>>2]|0)){f=A+2220|0;i=0}else{lA()}K=A+2392|0;Mf(n[A+2188>>2]|0,e,n[A+644>>2]|0,n[A+648>>2]|0,Q,K,x);B=A+2192|0;Ji(n[B>>2]|0,e,n[t>>2]|0,Q,g,a,S,x);$e(n[f>>2]|0,a,n[l>>2]|0,x);if((n[t>>2]|0)==8){lA()}else N=oi(n[A+2208>>2]|0,n[B>>2]|0,x)|0;J=A+640|0;B=A+2264|0;l=A+1264|0;f=A+2204|0;i=A+2212|0;O=A+1268|0;m=A+1278|0;Jf(e,2842,2862,2882,Q,0,n[J>>2]|0,B,n[l>>2]|0,x);if(e>>>0>1){Ff(n[f>>2]|0,n[i>>2]|0,e,n[l>>2]|0,W,O,m,0,n[o>>2]|0,x);Jf(e,2842,2862,2882,Q,80,n[J>>2]|0,B,n[l>>2]|0,x);Ff(n[f>>2]|0,n[i>>2]|0,e,(n[l>>2]|0)+160|0,W+2|0,O,m,1,n[o>>2]|0,x)}else{Jf(e,2842,2862,2882,Q,80,n[J>>2]|0,B,n[l>>2]|0,x);Ff(n[f>>2]|0,n[i>>2]|0,e,n[l>>2]|0,W,O,m,1,n[o>>2]|0,x);r[W+2>>1]=r[W>>1]|0}if(n[o>>2]|0)lA();if((n[t>>2]|0)==8){lA()}d=A+2224|0;I=A+2244|0;M=A+2284|0;F=A+2388|0;H=A+2020|0;U=A+1916|0;G=A+1912|0;L=A+2024|0;R=A+2384|0;T=A+2196|0;y=A+2208|0;Y=A+2464|0;z=A+2200|0;X=A+2224|0;P=A+2244|0;k=A+1270|0;b=A+1280|0;h=0;o=0;a=0;C=0;D=0;B=0;E=-1;while(1){v=E;E=E+1<<16>>16;C=1-(C<<16>>16)|0;i=C&65535;c=(C&65535|0)!=0;f=n[t>>2]|0;l=(f|0)==0;do{if(c)if(l){l=nA;f=d;u=l+20|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0));l=tA;f=I;u=l+20|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0));l=rA;f=M;u=l+20|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0));r[aA>>1]=r[F>>1]|0;e=(n[J>>2]|0)+(h<<1)|0;l=20;break}else{e=(n[J>>2]|0)+(h<<1)|0;l=19;break}else{e=(n[J>>2]|0)+(h<<1)|0;if(l)l=20;else l=19}}while(0);if((l|0)==19)wi(f,2842,2862,2882,Q,g,e,M,P,n[H>>2]|0,U,(n[G>>2]|0)+(h<<1)|0,n[L>>2]|0,sA,wA,n[R>>2]|0);else if((l|0)==20?(0,wi(0,2842,2862,2882,Q,g,e,M,tA,n[H>>2]|0,U,(n[G>>2]|0)+(h<<1)|0,n[L>>2]|0,sA,wA,n[R>>2]|0),c):0){l=iA;f=n[L>>2]|0;u=l+80|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0))}l=BA;f=wA;u=l+80|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0));Oe(n[T>>2]|0,n[y>>2]|0,n[t>>2]|0,D,W,n[L>>2]|0,(n[G>>2]|0)+(h<<1)|0,BA,sA,N,oA,gA,j,p,AA,_,S,fA,n[Y>>2]|0,x);switch(v<<16>>16){case-1:{if((r[m>>1]|0)>0)r[k>>1]=r[j>>1]|0;break}case 2:{if((r[b>>1]|0)>0)r[O>>1]=r[j>>1]|0;break}default:{}}Ye(oA,n[L>>2]|0,r[j>>1]|0,r[F>>1]|0,r[AA>>1]|0,BA,V,vA,S,n[t>>2]|0,E,K,x);sf(n[z>>2]|0,n[t>>2]|0,wA,(n[G>>2]|0)+(h<<1)|0,V,sA,oA,gA,vA,_,i,r[fA>>1]|0,eA,$,AA,q,S,K,x);gi(n[y>>2]|0,r[AA>>1]|0,x);e=n[t>>2]|0;do{if(!e)if(c){l=QA;f=sA;u=l+80|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0));l=uA;f=vA;u=l+80|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0));l=Z;f=V;u=l+80|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0));a=r[j>>1]|0;o=r[p>>1]|0;li(n[J>>2]|0,0,D,r[AA>>1]|0,r[q>>1]|0,g,w,sA,V,gA,vA,nA,M,tA,n[G>>2]|0,F,x);r[F>>1]=r[aA>>1]|0;B=D;break}else{l=M;f=rA;u=l+20|0;do{r[l>>1]=r[f>>1]|0;l=l+2|0;f=f+2|0}while((l|0)<(u|0));c=B<<16>>16;Si((n[G>>2]|0)+(c<<1)|0,a,o,40,1,x);Se((n[G>>2]|0)+(c<<1)|0,iA,gA,40);li(n[J>>2]|0,n[t>>2]|0,B,r[eA>>1]|0,r[$>>1]|0,g+-22|0,w,QA,Z,gA,uA,X,M,P,n[G>>2]|0,aA,x);wi(n[t>>2]|0,2842,2862,2882,Q,g,(n[J>>2]|0)+(h<<1)|0,M,P,n[H>>2]|0,U,(n[G>>2]|0)+(h<<1)|0,n[L>>2]|0,sA,wA,n[R>>2]|0);Si((n[G>>2]|0)+(h<<1)|0,r[j>>1]|0,r[p>>1]|0,40,1,x);Se((n[G>>2]|0)+(h<<1)|0,n[L>>2]|0,gA,40);li(n[J>>2]|0,n[t>>2]|0,D,r[AA>>1]|0,r[q>>1]|0,g,w,sA,V,gA,vA,X,M,P,n[G>>2]|0,F,x);break}else li(n[J>>2]|0,e,D,r[AA>>1]|0,r[q>>1]|0,g,w,sA,V,gA,vA,X,M,P,n[G>>2]|0,F,x)}while(0);e=h+40|0;D=e&65535;if(D<<16>>16>=160)break;else{h=e<<16>>16;Q=Q+22|0;g=g+22|0}}or(A+1282|0,A+1602|0,308)|0;vA=A+656|0;uA=A+976|0;or(vA|0,uA|0,286)|0;uA=A+320|0;or(A|0,uA|0,320)|0;s=cA;return 0}function Se(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0;g=i<<16>>16;if(i<<16>>16>1)Q=1;else return;while(1){n=r[A>>1]|0;l=e+(Q+-1<<1)|0;i=AA(r[e+(Q<<1)>>1]|0,n)|0;a=r[l>>1]|0;n=AA(a<<16>>16,n)|0;w=(Q+131071|0)>>>1;B=w&65535;t=r[A+2>>1]|0;if(!(B<<16>>16)){e=l;w=a}else{s=(w<<1)+131070&131070;o=Q-s|0;w=A;do{u=(AA(a<<16>>16,t)|0)+i|0;v=w;w=w+4|0;i=r[l+-2>>1]|0;t=(AA(i,t)|0)+n|0;n=r[w>>1]|0;l=l+-4|0;i=u+(AA(n,i)|0)|0;a=r[l>>1]|0;n=t+(AA(a<<16>>16,n)|0)|0;B=B+-1<<16>>16;t=r[v+6>>1]|0}while(B<<16>>16!=0);w=e+(o+-3<<1)|0;A=A+(s+2<<1)|0;e=w;w=r[w>>1]|0}i=(AA(w<<16>>16,t)|0)+i|0;r[f>>1]=n>>>12;r[f+2>>1]=i>>>12;i=(Q<<16)+131072>>16;if((i|0)<(g|0)){f=f+4|0;A=A+(1-Q<<1)|0;Q=i}else break}return}function je(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0;d=s;s=s+80|0;b=d;w=20;t=A;n=1;while(1){k=r[t>>1]|0;k=(AA(k,k)|0)+n|0;n=r[t+2>>1]|0;n=k+(AA(n,n)|0)|0;w=w+-1<<16>>16;if(!(w<<16>>16))break;else t=t+4|0}n=n<<1;if((n|0)<0){t=20;n=A;i=b;while(1){r[i>>1]=(r[n>>1]|0)>>>1;r[i+2>>1]=(r[n+2>>1]|0)>>>1;t=t+-1<<16>>16;if(!(t<<16>>16)){k=b;break}else{n=n+4|0;i=i+4|0}}}else{n=Ui(n>>1,i)|0;if((n|0)<16777215)n=((n>>9)*32440|0)>>>15<<16>>16;else n=32440;w=20;t=A;i=b;while(1){r[i>>1]=((AA(r[t>>1]|0,n)|0)+32|0)>>>6;r[i+2>>1]=((AA(r[t+2>>1]|0,n)|0)+32|0)>>>6;w=w+-1<<16>>16;if(!(w<<16>>16)){k=b;break}else{t=t+4|0;i=i+4|0}}}w=20;t=k;i=f+3198|0;n=0;while(1){P=r[t>>1]|0;P=(AA(P,P)|0)+n|0;r[i>>1]=(P+16384|0)>>>15;h=r[t+2>>1]|0;n=(AA(h,h)|0)+P|0;r[i+-82>>1]=(n+16384|0)>>>15;w=w+-1<<16>>16;if(!(w<<16>>16))break;else{t=t+4|0;i=i+-164|0}}P=e+78|0;h=1;while(1){n=39-h|0;A=f+3120+(n<<1)|0;i=f+(n*80|0)+78|0;n=e+(n<<1)|0;B=b+(h<<1)|0;t=65575-h|0;l=t&65535;w=r[k>>1]|0;if(!(l<<16>>16)){l=P;t=0}else{c=t+65535&65535;D=c*41|0;E=(AA(h,-40)|0)-D|0;C=0-h|0;D=C-D|0;C=C-c|0;u=h+c|0;v=r[B>>1]|0;Q=k;g=P;a=f+((38-h|0)*80|0)+78|0;t=0;o=0;while(1){B=B+2|0;t=(AA(v<<16>>16,w)|0)+t|0;Q=Q+2|0;v=r[B>>1]|0;o=(AA(v<<16>>16,w)|0)+o|0;M=n;n=n+-2|0;w=r[n>>1]|0;I=r[g>>1]<<1;M=(AA((AA(I,r[M>>1]|0)|0)>>16,(t<<1)+32768>>16)|0)>>>15&65535;r[i>>1]=M;r[A>>1]=M;w=(AA((AA(I,w)|0)>>16,(o<<1)+32768>>16)|0)>>>15&65535;r[A+-2>>1]=w;r[a>>1]=w;l=l+-1<<16>>16;w=r[Q>>1]|0;if(!(l<<16>>16))break;else{g=g+-2|0;A=A+-82|0;i=i+-82|0;a=a+-82|0}}B=b+(u+1<<1)|0;l=e+(38-c<<1)|0;n=e+(C+38<<1)|0;A=f+3040+(D+38<<1)|0;i=f+3040+(E+38<<1)|0}M=(AA(r[B>>1]|0,w)|0)+t|0;M=(AA((M<<1)+32768>>16,(AA(r[l>>1]<<1,r[n>>1]|0)|0)>>16)|0)>>>15&65535;r[A>>1]=M;r[i>>1]=M;i=(h<<16)+131072|0;if((i|0)<2621440)h=i>>16;else break}s=d;return}function pe(A,e,f,i,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0;c=s;s=s+160|0;u=c;if(t<<16>>16>0){g=w&65535;v=0;B=5;do{if((v|0)<40){Q=v;o=v&65535;w=0;while(1){if(o<<16>>16<40){o=o<<16>>16;a=0;do{a=(AA(r[A+(o-Q<<1)>>1]|0,r[e+(o<<1)>>1]|0)|0)+a|0;o=o+1|0}while((o&65535)<<16>>16!=40)}else a=0;a=a<<1;n[u+(Q<<2)>>2]=a;a=vf(a)|0;w=(a|0)>(w|0)?a:w;a=Q+g|0;o=a&65535;if(o<<16>>16>=40)break;else Q=a<<16>>16}}else w=0;B=(w>>1)+B|0;v=v+1|0}while((v&65535)<<16>>16!=t<<16>>16)}else B=5;i=((Ni(B)|0)&65535)-(i&65535)|0;w=i<<16>>16;a=0-w<<16;B=(a|0)<2031616;a=a>>16;if((i&65535)<<16>>16>0)if(B){B=0;do{i=n[u+(B<<2)>>2]|0;e=i<<w;r[f+(B<<1)>>1]=er((e>>w|0)==(i|0)?e:i>>31^2147483647,l)|0;B=B+1|0}while((B|0)!=40);s=c;return}else{lA()}else if(B){B=0;do{r[f+(B<<1)>>1]=er(n[u+(B<<2)>>2]>>a,l)|0;B=B+1|0}while((B|0)!=40);s=c;return}else{lA()}}function We(A,e,f,i,t){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;var w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0;d=s;s=s+160|0;b=d;h=A+2|0;P=r[A>>1]|0;k=0;t=5;do{E=k;B=0;while(1){Q=e+(E<<1)|0;D=40-E|0;w=(D+131071|0)>>>1&65535;a=e+(E+1<<1)|0;l=AA(r[Q>>1]<<1,P)|0;if(!(w<<16>>16))w=h;else{C=131111-E+131070&131070;c=E+C|0;u=h;v=A;g=Q;while(1){o=g+4|0;Q=v+4|0;l=(AA(r[a>>1]<<1,r[u>>1]|0)|0)+l|0;w=w+-1<<16>>16;l=(AA(r[o>>1]<<1,r[Q>>1]|0)|0)+l|0;if(!(w<<16>>16))break;else{a=g+6|0;u=v+6|0;v=Q;g=o}}a=e+(c+3<<1)|0;w=A+(C+3<<1)|0}if(!(D&1))l=(AA(r[a>>1]<<1,r[w>>1]|0)|0)+l|0;n[b+(E<<2)>>2]=l;l=(l|0)<0?0-l|0:l;B=(l|0)>(B|0)?l:B;l=E+5|0;if((l&65535)<<16>>16<40)E=l<<16>>16;else break}t=(B>>1)+t|0;k=k+1|0}while((k|0)!=5);i=((Ni(t)|0)&65535)-(i&65535)|0;l=i<<16>>16;t=0-l<<16;B=t>>16;if((i&65535)<<16>>16>0){w=20;t=b;while(1){b=n[t>>2]|0;i=b<<l;r[f>>1]=(((i>>l|0)==(b|0)?i:b>>31^2147483647)+32768|0)>>>16;b=n[t+4>>2]|0;i=b<<l;r[f+2>>1]=(((i>>l|0)==(b|0)?i:b>>31^2147483647)+32768|0)>>>16;w=w+-1<<16>>16;if(!(w<<16>>16))break;else{f=f+4|0;t=t+8|0}}s=d;return}if((t|0)<2031616){w=20;t=b;while(1){r[f>>1]=((n[t>>2]>>B)+32768|0)>>>16;r[f+2>>1]=((n[t+4>>2]>>B)+32768|0)>>>16;w=w+-1<<16>>16;if(!(w<<16>>16))break;else{f=f+4|0;t=t+8|0}}s=d;return}else{lA()}}function Ve(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var r=0,t=0,w=0;w=(Ei(16383,e)|0)<<16>>16;e=AA(w,e<<16>>16)|0;if((e|0)==1073741824){n[i>>2]=1;r=2147483647}else r=e<<1;t=(AA(w,f<<16>>16)|0)>>15;e=r+(t<<1)|0;if((r^t|0)>0&(e^r|0)<0){n[i>>2]=1;e=(r>>>31)+2147483647|0}r=2147483647-e|0;f=r>>16;e=AA(f,w)|0;if((e|0)==1073741824){n[i>>2]=1;t=2147483647}else t=e<<1;w=(AA((r>>>1)-(f<<15)<<16>>16,w)|0)>>15;e=t+(w<<1)|0;if((t^w|0)>0&(e^t|0)<0){n[i>>2]=1;e=(t>>>31)+2147483647|0}t=e>>16;w=A>>16;f=AA(t,w)|0;f=(f|0)==1073741824?2147483647:f<<1;r=(AA((e>>>1)-(t<<15)<<16>>16,w)|0)>>15;i=(r<<1)+f|0;i=(r^f|0)>0&(i^f|0)<0?(f>>>31)+2147483647|0:i;w=(AA(t,(A>>>1)-(w<<15)<<16>>16)|0)>>15;A=i+(w<<1)|0;A=(i^w|0)>0&(A^i|0)<0?(i>>>31)+2147483647|0:A;i=A<<2;return((i>>2|0)==(A|0)?i:A>>31^2147483647)|0}function Ze(A,e){A=A|0;e=e|0;var f=0,i=0,t=0,w=0;if(!A){w=-1;return w|0}n[A>>2]=0;f=lr(192)|0;if(!f){w=-1;return w|0}i=f+176|0;r[i>>1]=0;r[i+2>>1]=0;r[i+4>>1]=0;r[i+6>>1]=0;r[i+8>>1]=0;r[i+10>>1]=0;i=f;t=e;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=f+20|0;t=e;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=f+40|0;t=e;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=f+60|0;t=e;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=f+80|0;t=e;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=f+100|0;t=e;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=f+120|0;t=e;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=f+140|0;t=e;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=f+160|0;w=i+20|0;do{r[i>>1]=0;i=i+2|0}while((i|0)<(w|0));r[f+188>>1]=7;r[f+190>>1]=32767;n[A>>2]=f;w=0;return w|0}function _e(A,e){A=A|0;e=e|0;var f=0,i=0,n=0;if(!A){n=-1;return n|0}f=A+176|0;r[f>>1]=0;r[f+2>>1]=0;r[f+4>>1]=0;r[f+6>>1]=0;r[f+8>>1]=0;r[f+10>>1]=0;f=A;i=e;n=f+20|0;do{r[f>>1]=r[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(n|0));f=A+20|0;i=e;n=f+20|0;do{r[f>>1]=r[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(n|0));f=A+40|0;i=e;n=f+20|0;do{r[f>>1]=r[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(n|0));f=A+60|0;i=e;n=f+20|0;do{r[f>>1]=r[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(n|0));f=A+80|0;i=e;n=f+20|0;do{r[f>>1]=r[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(n|0));f=A+100|0;i=e;n=f+20|0;do{r[f>>1]=r[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(n|0));f=A+120|0;i=e;n=f+20|0;do{r[f>>1]=r[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(n|0));f=A+140|0;i=e;n=f+20|0;do{r[f>>1]=r[i>>1]|0;f=f+2|0;i=i+2|0}while((f|0)<(n|0));f=A+160|0;n=f+20|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(n|0));r[A+188>>1]=7;r[A+190>>1]=32767;n=1;return n|0}function qe(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function $e(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var t=0,l=0,B=0,a=0,o=0,Q=0;Q=s;s=s+16|0;B=Q+2|0;o=Q;a=A+176|0;l=(w[a>>1]|0)+1|0;l=(l&65535|0)==8?0:l&65535;r[a>>1]=l;l=A+((l<<16>>16)*10<<1)|0;t=l+20|0;do{r[l>>1]=r[e>>1]|0;l=l+2|0;e=e+2|0}while((l|0)<(t|0));e=0;t=160;while(1){l=r[f>>1]|0;e=(AA(l<<1,l)|0)+e|0;if((e|0)<0){e=2147483647;break}t=t+-1<<16>>16;if(!(t<<16>>16))break;else f=f+2|0}Gi(e,B,o,i);e=r[B>>1]|0;B=e<<16>>16;f=B<<10;if((f|0)!=(B<<26>>16|0)){n[i>>2]=1;f=e<<16>>16>0?32767:-32768}r[A+160+(r[a>>1]<<1)>>1]=(((r[o>>1]|0)>>>5)+f<<16)+-558432256>>17;s=Q;return}function Af(A,e,f,i,r,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;if(!(n<<16>>16)){n=A<<16>>16;if(((n<<16)+-5570560|0)<65536){e=(n*3|0)+-58+(e<<16>>16)|0;e=e&65535;return e|0}else{e=n+112|0;e=e&65535;return e|0}}if(!(t<<16>>16)){w=(A&65535)-(i&65535)<<16;e=(e<<16>>16)+2+(w>>15)+(w>>16)|0;e=e&65535;return e|0}i=i<<16>>16;i=(((f&65535)-i<<16)+-327680|0)>0?i+5&65535:f;r=r<<16>>16;f=A<<16>>16;i=(((r-(i&65535)<<16)+-262144|0)>0?r+65532&65535:i)<<16>>16;r=i*196608|0;A=r+-393216>>16;n=((e&65535)<<16)+(f*196608|0)>>16;if(!(A-n&32768)){e=f+5-i|0;e=e&65535;return e|0}if((r+196608>>16|0)>(n|0)){e=n+3-A|0;e=e&65535;return e|0}else{e=f+11-i|0;e=e&65535;return e|0}return 0}function ef(A,e,f,i,r){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;r=A<<16>>16;do{if(!(i<<16>>16))if(A<<16>>16<95){r=((r*393216|0)+-6881280>>16)+(e<<16>>16)|0;break}else{r=r+368|0;break}else r=((((r-(f&65535)|0)*393216|0)+196608|0)>>>16)+(e&65535)|0}while(0);return r&65535|0}function ff(A,e,f,t){A=A|0;e=e|0;f=f|0;t=t|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0;g=n[t+100>>2]|0;Q=n[t+96>>2]|0;i[f>>0]=A<<3;Q=Q+(A<<1)|0;l=r[Q>>1]|0;if(A>>>0>=8){lA()}a=l<<16>>16;if(l<<16>>16>7){l=g+(A<<2)|0;t=0;o=0;B=1;while(1){v=w[e+(r[(n[l>>2]|0)+(t<<1)>>1]<<1)>>1]<<7;a=f+(B<<16>>16)|0;i[a>>0]=v;v=w[e+(r[(n[l>>2]|0)+((o|1)<<16>>16<<1)>>1]<<1)>>1]<<6|v;i[a>>0]=v;v=w[e+(r[(n[l>>2]|0)+((o|2)<<16>>16<<1)>>1]<<1)>>1]<<5|v;i[a>>0]=v;v=w[e+(r[(n[l>>2]|0)+((o|3)<<16>>16<<1)>>1]<<1)>>1]<<4|v;i[a>>0]=v;v=w[e+(r[(n[l>>2]|0)+((o|4)<<16>>16<<1)>>1]<<1)>>1]<<3|v&240;i[a>>0]=v;v=w[e+(r[(n[l>>2]|0)+((o|5)<<16>>16<<1)>>1]<<1)>>1]<<2|v;i[a>>0]=v;v=w[e+(r[(n[l>>2]|0)+((o|6)<<16>>16<<1)>>1]<<1)>>1]<<1|v;i[a>>0]=v;s=o+8<<16>>16;B=B+1<<16>>16;i[a>>0]=v&254|w[e+(r[(n[l>>2]|0)+((o|7)<<16>>16<<1)>>1]<<1)>>1];t=s<<16>>16;a=r[Q>>1]|0;if((t|0)>=(a+-7|0))break;else o=s}}else{s=0;B=1}Q=a&7;o=f+(B<<16>>16)|0;i[o>>0]=0;if(!Q)return;B=g+(A<<2)|0;l=0;t=0;a=0;while(1){t=(w[e+(r[(n[B>>2]|0)+(s<<16>>16<<1)>>1]<<1)>>1]&255)<<7-l|t&255;i[o>>0]=t;a=a+1<<16>>16;l=a<<16>>16;if((l|0)>=(Q|0))break;else s=s+1<<16>>16}return}function rf(A){A=A|0;var e=0;if(!A){A=-1;return A|0}n[A>>2]=0;e=lr(16)|0;if(!e){A=-1;return A|0}r[e>>1]=0;r[e+2>>1]=0;r[e+4>>1]=0;r[e+6>>1]=0;r[e+8>>1]=0;r[e+10>>1]=0;r[e+12>>1]=0;r[e+14>>1]=0;n[A>>2]=e;A=0;return A|0}function nf(A){A=A|0;if(!A){A=-1;return A|0}r[A>>1]=0;r[A+2>>1]=0;r[A+4>>1]=0;r[A+6>>1]=0;r[A+8>>1]=0;r[A+10>>1]=0;r[A+12>>1]=0;r[A+14>>1]=0;A=0;return A|0}function tf(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function wf(A,e,f,i,t){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;var w=0,l=0,B=0,a=0,s=0;B=e<<16>>16<2722?0:e<<16>>16<5444?1:2;l=ir(f,1,t)|0;s=A+4|0;if(!(f<<16>>16>200?l<<16>>16>(r[s>>1]|0):0)){l=r[A>>1]|0;if(l<<16>>16){w=l+-1<<16>>16;r[A>>1]=w;w=w<<16>>16!=0;a=5}}else{r[A>>1]=8;w=1;a=5}if((a|0)==5)if((B&65535)<2&w)B=(B&65535)+1&65535;a=A+6|0;r[a>>1]=e;w=di(a,5)|0;if(!(B<<16>>16!=0|w<<16>>16>5443))if(w<<16>>16<0)w=16384;else{w=w<<16>>16;w=(((w<<18>>18|0)==(w|0)?w<<2:w>>>15^32767)<<16>>16)*24660>>15;if((w|0)>32767){n[t>>2]=1;w=32767}w=16384-w&65535}else w=0;l=A+2|0;if(!(r[l>>1]|0))w=fr(w,1,t)|0;r[i>>1]=w;r[l>>1]=w;r[s>>1]=f;i=A+12|0;r[A+14>>1]=r[i>>1]|0;f=A+10|0;r[i>>1]=r[f>>1]|0;A=A+8|0;r[f>>1]=r[A>>1]|0;r[A>>1]=r[a>>1]|0;return}function lf(A){A=A|0;var e=0,f=0,i=0,t=0,w=0,l=0;if(!A){A=-1;return A|0}n[A>>2]=0;e=lr(68)|0;i=e;if(!e){A=-1;return A|0}n[e+28>>2]=0;t=e+64|0;n[t>>2]=0;w=e+32|0;if(((hi(w)|0)<<16>>16==0?(l=e+48|0,(hi(l)|0)<<16>>16==0):0)?(rf(t)|0)<<16>>16==0:0){f=e+32|0;do{r[e>>1]=0;e=e+2|0}while((e|0)<(f|0));hi(w)|0;hi(l)|0;nf(n[t>>2]|0)|0;n[A>>2]=i;A=0;return A|0}tf(t);Br(e);A=-1;return A|0}function Bf(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;tf(e+64|0);Br(n[A>>2]|0);n[A>>2]=0;return}function af(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}e=A+32|0;f=A;i=f+32|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));hi(e)|0;hi(A+48|0)|0;nf(n[A+64>>2]|0)|0;i=0;return i|0}function sf(A,e,f,i,t,l,B,a,o,Q,g,v,u,c,C,D,E,h,P){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;D=D|0;E=E|0;h=h|0;P=P|0;var k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0;R=s;s=s+48|0;b=R+34|0;I=R+32|0;F=R+30|0;M=R+28|0;d=R+18|0;k=R+8|0;H=R+6|0;U=R+4|0;G=R+2|0;L=R;if(e){g=A+32|0;Pi(g,e,t,b,I,H,U,P);do{if((e|0)!=7){Te(e,l,B,a,o,Q,d,k,L,G,P);if((e|0)==5){Wf(n[A+64>>2]|0,f,i,t,d,k,r[H>>1]|0,r[U>>1]|0,r[b>>1]|0,r[I>>1]|0,40,r[L>>1]|0,r[G>>1]|0,v,C,D,F,M,E,h,P);break}else{A=_f(e,r[b>>1]|0,r[I>>1]|0,d,k,v,C,D,F,M,h,P)|0;l=n[E>>2]|0;n[E>>2]=l+2;r[l>>1]=A;break}}else{r[D>>1]=of(B,o,P)|0;A=Vf(7,r[b>>1]|0,r[I>>1]|0,D,F,M,n[h+68>>2]|0,P)|0;l=n[E>>2]|0;n[E>>2]=l+2;r[l>>1]=A}}while(0);ki(g,r[F>>1]|0,r[M>>1]|0);s=R;return}if(!(g<<16>>16)){Pi(A+48|0,0,t,b,I,H,U,P);Te(0,l,B,a,o,Q,d,k,L,G,P);ye(l,H,U,P);l=jf(A+32|0,r[A>>1]|0,r[A+2>>1]|0,A+8|0,A+18|0,r[A+4>>1]|0,r[A+6>>1]|0,t,r[b>>1]|0,r[I>>1]|0,k,d,r[H>>1]|0,r[U>>1]|0,v,u,c,C,D,P)|0;r[n[A+28>>2]>>1]=l;s=R;return}g=n[E>>2]|0;n[E>>2]=g+2;n[A+28>>2]=g;g=A+48|0;f=A+32|0;u=f;u=w[u>>1]|w[u+2>>1]<<16;f=f+4|0;f=w[f>>1]|w[f+2>>1]<<16;E=g;c=E;r[c>>1]=u;r[c+2>>1]=u>>>16;E=E+4|0;r[E>>1]=f;r[E+2>>1]=f>>>16;E=A+40|0;f=E;f=w[f>>1]|w[f+2>>1]<<16;E=E+4|0;E=w[E>>1]|w[E+2>>1]<<16;c=A+56|0;u=c;r[u>>1]=f;r[u+2>>1]=f>>>16;c=c+4|0;r[c>>1]=E;r[c+2>>1]=E>>>16;c=A+2|0;Pi(g,0,t,A,c,H,U,P);Te(0,l,B,a,o,Q,A+18|0,A+8|0,L,G,P);a=(w[G>>1]|0)+1|0;E=r[L>>1]|0;u=a<<16>>16;if((a&65535)<<16>>16<0){h=0-u<<16;if((h|0)<983040)h=E<<16>>16>>(h>>16)&65535;else h=0}else{E=E<<16>>16;h=E<<u;if((h<<16>>16>>u|0)==(E|0))h=h&65535;else h=(E>>>15^32767)&65535}r[D>>1]=h;ye(l,A+4|0,A+6|0,P);Sf(g,r[A>>1]|0,r[c>>1]|0,r[G>>1]|0,r[L>>1]|0,P);s=R;return}function of(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,n=0,t=0;n=10;f=A;i=e;A=0;while(1){A=(AA(r[i>>1]>>1,r[f>>1]|0)|0)+A|0;A=A+(AA(r[i+2>>1]>>1,r[f+2>>1]|0)|0)|0;A=A+(AA(r[i+4>>1]>>1,r[f+4>>1]|0)|0)|0;A=A+(AA(r[i+6>>1]>>1,r[f+6>>1]|0)|0)|0;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{f=f+8|0;i=i+8|0}}f=A<<1;n=Ni(f|1)|0;t=n<<16>>16;f=(n<<16>>16<17?f>>17-t:f<<t+-17)&65535;if(f<<16>>16<1){e=0;return e|0}else{n=20;i=e;A=0}while(1){e=r[i>>1]>>1;e=((AA(e,e)|0)>>>2)+A|0;A=r[i+2>>1]>>1;A=e+((AA(A,A)|0)>>>2)|0;n=n+-1<<16>>16;if(!(n<<16>>16))break;else i=i+4|0}A=A<<3;n=Ni(A)|0;e=n<<16>>16;f=Ei(f,(n<<16>>16<16?A>>16-e:A<<e+-16)&65535)|0;e=(t<<16)+327680-(e<<16)|0;A=e>>16;if((e|0)>65536)A=f<<16>>16>>A+-1;else A=f<<16>>16<<1-A;e=A&65535;return e|0}function Qf(A,e,f,i,t,w){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0;n[w>>2]=0;o=t<<16>>16;a=o>>>2&65535;g=a<<16>>16==0;if(g)B=0;else{s=a;l=f;B=0;while(1){v=r[l>>1]|0;v=(AA(v,v)|0)+B|0;B=r[l+2>>1]|0;B=v+(AA(B,B)|0)|0;v=r[l+4>>1]|0;v=B+(AA(v,v)|0)|0;B=r[l+6>>1]|0;B=v+(AA(B,B)|0)|0;s=s+-1<<16>>16;if(!(s<<16>>16))break;else l=l+8|0}}if(!((B>>>31^1)&(B|0)<1073741824)){B=o>>>1&65535;if(!(B<<16>>16))B=1;else{l=B;s=f;B=0;while(1){v=r[s>>1]>>2;v=(AA(v,v)|0)+B|0;B=r[s+2>>1]>>2;B=v+(AA(B,B)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;else s=s+4|0}B=B<<1|1}v=(Ni(B)|0)<<16>>16;Q=v+65532&65535;v=er(B<<v,w)|0}else{o=B<<1|1;v=Ni(o)|0;Q=v;v=er(o<<(v<<16>>16),w)|0}n[w>>2]=0;do{if(!(t<<16>>16)){B=1;u=14}else{o=t;s=e;B=f;t=0;while(1){c=AA(r[B>>1]|0,r[s>>1]|0)|0;l=c+t|0;if((c^t|0)>0&(l^t|0)<0)break;o=o+-1<<16>>16;if(!(o<<16>>16)){u=13;break}else{s=s+2|0;B=B+2|0;t=l}}if((u|0)==13){B=l<<1|1;u=14;break}n[w>>2]=1;if(g)B=1;else{B=e;l=0;while(1){l=(AA(r[f>>1]>>2,r[B>>1]|0)|0)+l|0;l=l+(AA(r[f+2>>1]>>2,r[B+2>>1]|0)|0)|0;l=l+(AA(r[f+4>>1]>>2,r[B+4>>1]|0)|0)|0;l=l+(AA(r[f+6>>1]>>2,r[B+6>>1]|0)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{B=B+8|0;f=f+8|0}}B=l<<1|1}f=(Ni(B)|0)<<16>>16;l=f+65532&65535;f=er(B<<f,w)|0}}while(0);if((u|0)==14){f=Ni(B)|0;l=f;f=er(B<<(f<<16>>16),w)|0}r[i>>1]=v;B=Q<<16>>16;r[i+2>>1]=15-B;r[i+4>>1]=f;l=l<<16>>16;r[i+6>>1]=15-l;if(f<<16>>16<4){c=0;return c|0}l=fr(Ei(f<<16>>16>>>1&65535,v)|0,l-B&65535,w)|0;l=l<<16>>16>19661?19661:l;if((A|0)!=7){c=l;return c|0}c=l&65532;return c|0}function gf(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;if(f<<16>>16)e=e<<16>>16<<1&65535;if(e<<16>>16<0){A=A+-2|0;e=(e&65535)+6&65535}f=e<<16>>16;i=6-f<<16>>16;e=(AA(r[3468+(f<<1)>>1]|0,r[A>>1]|0)|0)+16384|0;e=e+(AA(r[3468+(i<<1)>>1]|0,r[A+2>>1]|0)|0)|0;e=e+(AA(r[3468+(f+6<<1)>>1]|0,r[A+-2>>1]|0)|0)|0;e=e+(AA(r[3468+(i+6<<1)>>1]|0,r[A+4>>1]|0)|0)|0;e=(AA(r[3468+(f+12<<1)>>1]|0,r[A+-4>>1]|0)|0)+e|0;e=e+(AA(r[3468+(i+12<<1)>>1]|0,r[A+6>>1]|0)|0)|0;f=e+(AA(r[3468+(f+18<<1)>>1]|0,r[A+-6>>1]|0)|0)|0;return(f+(AA(r[3468+(i+18<<1)>>1]|0,r[A+8>>1]|0)|0)|0)>>>15&65535|0}function vf(A){A=A|0;A=A-(A>>>31)|0;return A>>31^A|0}function uf(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0;if(!(A<<16>>16))return;else{n=3518;t=3538;i=f}while(1){i=i+2|0;e=e+2|0;B=r[e>>1]|0;l=r[n>>1]|0;f=AA(l,B)|0;f=(f|0)==1073741824?2147483647:f<<1;B=(AA(r[t>>1]|0,B)|0)>>15;w=(B<<1)+f|0;w=(f^B|0)>0&(w^f|0)<0?(f>>>31)+2147483647|0:w;l=(AA(l,r[i>>1]|0)|0)>>15;f=w+(l<<1)|0;f=(w^l|0)>0&(f^w|0)<0?(w>>>31)+2147483647|0:f;r[e>>1]=f>>>16;r[i>>1]=(f>>>1)-(f>>16<<15);A=A+-1<<16>>16;if(!(A<<16>>16))break;else{n=n+2|0;t=t+2|0}}return}function cf(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0;i=A&65535;r=i<<16;e=e<<16>>16;A=(e<<1)+r|0;if(!((e^r|0)>0&(A^r|0)<0)){r=A;return r|0}n[f>>2]=1;r=(i>>>15)+2147483647|0;return r|0}function Cf(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}n[A>>2]=0;e=lr(22)|0;if(!e){i=-1;return i|0}r[e>>1]=4096;f=e+2|0;i=f+20|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));n[A>>2]=e;i=0;return i|0}function Df(A){A=A|0;var e=0;if(!A){e=-1;return e|0}r[A>>1]=4096;A=A+2|0;e=A+20|0;do{r[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function Ef(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function hf(A,e,f,i,n,t){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;var l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0;Y=s;s=s+96|0;T=Y+66|0;y=Y+44|0;R=Y+22|0;B=Y;H=e+2|0;L=f+2|0;G=(r[L>>1]<<1)+(w[H>>1]<<16)|0;l=vf(G)|0;l=Ve(l,r[e>>1]|0,r[f>>1]|0,t)|0;if((G|0)>0)l=kf(l)|0;M=l>>16;r[n>>1]=er(l,t)|0;h=l>>20;U=T+2|0;r[U>>1]=h;G=y+2|0;r[G>>1]=(l>>>5)-(h<<15);h=AA(M,M)|0;h=(h|0)==1073741824?2147483647:h<<1;M=(AA((l>>>1)-(M<<15)<<16>>16,M)|0)>>15;F=M<<1;I=F+h|0;I=(M^h|0)>0&(I^h|0)<0?(h>>>31)+2147483647|0:I;F=I+F|0;F=2147483647-(vf((I^M|0)>0&(F^I|0)<0?(I>>>31)+2147483647|0:F)|0)|0;I=F>>16;M=r[e>>1]|0;h=AA(I,M)|0;h=(h|0)==1073741824?2147483647:h<<1;M=(AA((F>>>1)-(I<<15)<<16>>16,M)|0)>>15;F=(M<<1)+h|0;F=(M^h|0)>0&(F^h|0)<0?(h>>>31)+2147483647|0:F;I=(AA(r[f>>1]|0,I)|0)>>15;h=F+(I<<1)|0;h=(F^I|0)>0&(h^F|0)<0?(F>>>31)+2147483647|0:h;F=Ni(h)|0;h=h<<(F<<16>>16);I=R+2|0;M=B+2|0;a=h;h=(h>>>1)-(h>>16<<15)|0;P=B+4|0;k=R+4|0;b=2;d=2;while(1){E=a>>>16;l=E&65535;c=h&65535;C=d+-1|0;Q=T+(C<<1)|0;D=y+(C<<1)|0;u=1;v=Q;g=D;o=H;B=L;a=0;while(1){z=r[o>>1]|0;X=((AA(r[g>>1]|0,z)|0)>>15)+a|0;a=r[v>>1]|0;a=X+(AA(a,z)|0)+((AA(a,r[B>>1]|0)|0)>>15)|0;u=u+1<<16>>16;if((u<<16>>16|0)>=(d|0))break;else{v=v+-2|0;g=g+-2|0;o=o+2|0;B=B+2|0}}X=(w[e+(d<<1)>>1]<<16)+(a<<5)+(r[f+(d<<1)>>1]<<1)|0;a=Ve(vf(X)|0,l,c,t)|0;if((X|0)>0)a=kf(a)|0;B=F<<16>>16;if(F<<16>>16>0){l=a<<B;if((l>>B|0)!=(a|0))l=a>>31^2147483647}else{B=0-B<<16;if((B|0)<2031616)l=a>>(B>>16);else l=0}u=l>>16;if((d|0)<5)r[n+(C<<1)>>1]=(l+32768|0)>>>16;X=(l>>>16)-(l>>>31)|0;if(((X<<16>>31^X)&65535)<<16>>16>32750){l=16;break}g=(l>>>1)-(u<<15)<<16>>16;v=1;a=D;B=I;o=M;while(1){z=(AA(r[a>>1]|0,u)|0)>>15;D=r[Q>>1]|0;X=(AA(D,g)|0)>>15;D=AA(D,u)|0;X=D+z+(r[y+(v<<1)>>1]|0)+(r[T+(v<<1)>>1]<<15)+X|0;r[B>>1]=X>>>15;r[o>>1]=X&32767;v=v+1|0;if((v&65535)<<16>>16==b<<16>>16)break;else{Q=Q+-2|0;a=a+-2|0;B=B+2|0;o=o+2|0}}r[k>>1]=l>>20;r[P>>1]=(l>>>5)-(r[R+(d<<1)>>1]<<15);z=AA(u,u)|0;z=(z|0)==1073741824?2147483647:z<<1;l=(AA(g,u)|0)>>15;X=l<<1;B=X+z|0;B=(l^z|0)>0&(B^z|0)<0?(z>>>31)+2147483647|0:B;X=B+X|0;X=2147483647-(vf((B^l|0)>0&(X^B|0)<0?(B>>>31)+2147483647|0:X)|0)|0;B=X>>16;l=E<<16>>16;l=((AA(B,h<<16>>16)|0)>>15)+(AA(B,l)|0)+((AA((X>>>1)-(B<<15)<<16>>16,l)|0)>>15)<<1;B=(Ni(l)|0)<<16>>16;l=l<<B;X=d<<1;or(U|0,I|0,X|0)|0;or(G|0,M|0,X|0)|0;d=d+1|0;if((d|0)>=11){l=20;break}else{F=B+(F&65535)&65535;a=l;h=(l>>1)-(l>>16<<15)|0;P=P+2|0;k=k+2|0;b=b+1<<16>>16}}if((l|0)==16){lA()}else if((l|0)==20){r[i>>1]=4096;X=((r[G>>1]|0)+8192+(r[U>>1]<<15)|0)>>>14&65535;r[i+2>>1]=X;r[A+2>>1]=X;X=((r[y+4>>1]|0)+8192+(r[T+4>>1]<<15)|0)>>>14&65535;r[i+4>>1]=X;r[A+4>>1]=X;X=((r[y+6>>1]|0)+8192+(r[T+6>>1]<<15)|0)>>>14&65535;r[i+6>>1]=X;r[A+6>>1]=X;X=((r[y+8>>1]|0)+8192+(r[T+8>>1]<<15)|0)>>>14&65535;r[i+8>>1]=X;r[A+8>>1]=X;X=((r[y+10>>1]|0)+8192+(r[T+10>>1]<<15)|0)>>>14&65535;r[i+10>>1]=X;r[A+10>>1]=X;X=((r[y+12>>1]|0)+8192+(r[T+12>>1]<<15)|0)>>>14&65535;r[i+12>>1]=X;r[A+12>>1]=X;X=((r[y+14>>1]|0)+8192+(r[T+14>>1]<<15)|0)>>>14&65535;r[i+14>>1]=X;r[A+14>>1]=X;X=((r[y+16>>1]|0)+8192+(r[T+16>>1]<<15)|0)>>>14&65535;r[i+16>>1]=X;r[A+16>>1]=X;X=((r[y+18>>1]|0)+8192+(r[T+18>>1]<<15)|0)>>>14&65535;r[i+18>>1]=X;r[A+18>>1]=X;X=((r[y+20>>1]|0)+8192+(r[T+20>>1]<<15)|0)>>>14&65535;r[i+20>>1]=X;r[A+20>>1]=X;s=Y;return 0}return 0}function Pf(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;i=A>>16;r[e>>1]=i;r[f>>1]=(A>>>1)-(i<<15);return}function kf(A){A=A|0;return((A|0)==-2147483648?2147483647:0-A|0)|0}function bf(A){A=A|0;var e=0;if(!A){A=-1;return A|0}n[A>>2]=0;e=lr(4)|0;if(!e){A=-1;return A|0}n[e>>2]=0;if(!((Cf(e)|0)<<16>>16)){Df(n[e>>2]|0)|0;n[A>>2]=e;A=0;return A|0}else{Ef(e);Br(e);A=-1;return A|0}return 0}function df(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Ef(e);Br(n[A>>2]|0);n[A>>2]=0;return}function If(A){A=A|0;if(!A){A=-1;return A|0}Df(n[A>>2]|0)|0;A=0;return A|0}function Mf(A,e,f,i,r,t,w){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;t=t|0;w=w|0;var l=0,B=0,a=0,o=0;o=s;s=s+64|0;a=o+48|0;B=o+22|0;l=o;if((e|0)==7){f=n[t+116>>2]|0;ke(i,10,l,B,n[t+112>>2]|0,w)|0;uf(10,l,B,w);hf(n[A>>2]|0,l,B,r+22|0,a,w)|0;ke(i,10,l,B,f,w)|0;uf(10,l,B,w);hf(n[A>>2]|0,l,B,r+66|0,a,w)|0;s=o;return}else{ke(f,10,l,B,n[t+108>>2]|0,w)|0;uf(10,l,B,w);hf(n[A>>2]|0,l,B,r+66|0,a,w)|0;s=o;return}}function Ff(A,e,f,i,n,t,w,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;if((f|0)==6){r[n>>1]=Xf(A,e,i,20,143,80,t,w,l,B,a)|0;return}r[w>>1]=0;r[w+2>>1]=0;if(f>>>0<2){r[n>>1]=Rf(e,f,i,20,143,160,l,B,a)|0;return}if(f>>>0<6){r[n>>1]=Rf(e,f,i,20,143,80,l,B,a)|0;return}else{r[n>>1]=Rf(e,f,i,18,143,80,l,B,a)|0;return}}function Hf(A){A=A|0;var e=0;if((A|0)!=0?(n[A>>2]=0,e=lr(2)|0,(e|0)!=0):0){r[e>>1]=0;n[A>>2]=e;e=0}else e=-1;return e|0}function Uf(A){A=A|0;if(!A)A=-1;else{r[A>>1]=0;A=0}return A|0}function Gf(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function Lf(A,e,f,i,n,t,l,B,a,o,Q,g){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;var v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0;j=s;s=s+240|0;E=j+160|0;h=j+80|0;O=j;J=r[3558+(e*18|0)>>1]|0;S=r[3558+(e*18|0)+2>>1]|0;v=r[3558+(e*18|0)+4>>1]|0;m=r[3558+(e*18|0)+6>>1]|0;C=r[3558+(e*18|0)+12>>1]|0;c=r[3558+(e*18|0)+14>>1]|0;u=r[3558+(e*18|0)+16>>1]|0;A:do{switch(B<<16>>16){case 0:case 80:if(e>>>0<2&B<<16>>16==80){N=(w[A>>1]|0)-(C&65535)|0;N=(N<<16>>16|0)<(u<<16>>16|0)?u:N&65535;X=c<<16>>16;K=(N&65535)+X&65535;x=K<<16>>16>143;N=x?143-X&65535:N;K=x?143:K;x=1;break A}else{N=(w[f+((B<<16>>16!=0&1)<<1)>>1]|0)-(w[3558+(e*18|0)+8>>1]|0)|0;N=(N<<16>>16|0)<(u<<16>>16|0)?u:N&65535;X=r[3558+(e*18|0)+10>>1]|0;K=(N&65535)+X&65535;x=K<<16>>16>143;N=x?143-X&65535:N;K=x?143:K;x=0;break A}default:{N=(w[A>>1]|0)-(C&65535)|0;N=(N<<16>>16|0)<(u<<16>>16|0)?u:N&65535;X=c<<16>>16;K=(N&65535)+X&65535;x=K<<16>>16>143;N=x?143-X&65535:N;K=x?143:K;x=1}}}while(0);z=N&65535;B=z+65532|0;D=B&65535;Y=(K&65535)+4&65535;X=B<<16>>16;B=0-(B&65535)|0;C=B&65535;Se(i+(B<<16>>16<<1)|0,t,E,l);B=l<<16>>16;M=B>>>1&65535;P=M<<16>>16==0;if(P)l=1;else{l=M;u=E;f=h;c=0;while(1){y=r[u>>1]|0;r[f>>1]=y>>>2;y=(AA(y,y)|0)+c|0;c=r[u+2>>1]|0;r[f+2>>1]=c>>>2;c=y+(AA(c,c)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;else{u=u+4|0;f=f+4|0}}l=(c|0)<33554433}y=l?0:2;I=l?E:h;k=l?E:h;A:do{if(D<<16>>16<=Y<<16>>16){b=B+-1|0;L=I+(b<<1)|0;R=t+(b<<1)|0;T=I+(B+-2<<1)|0;H=b>>>1;U=H&65535;d=U<<16>>16==0;G=l?12:14;H=(H<<1)+131070&131070;f=B+-3-H|0;F=I+(f<<1)|0;H=I+(B+-4-H<<1)|0;t=t+(f<<1)|0;if(!P){P=X;while(1){h=M;E=k;u=n;c=0;l=0;while(1){h=h+-1<<16>>16;B=r[E>>1]|0;c=(AA(B,r[u>>1]|0)|0)+c|0;B=(AA(B,B)|0)+l|0;l=r[E+2>>1]|0;c=c+(AA(l,r[u+2>>1]|0)|0)|0;l=B+(AA(l,l)|0)|0;if(!(h<<16>>16))break;else{E=E+4|0;u=u+4|0}}E=Ui(l<<1,g)|0;l=E>>16;u=c<<1>>16;h=AA(l,u)|0;h=(h|0)==1073741824?2147483647:h<<1;u=(AA((E>>>1)-(l<<15)<<16>>16,u)|0)>>15;E=(u<<1)+h|0;E=(u^h|0)>0&(E^h|0)<0?(h>>>31)+2147483647|0:E;l=(AA(l,c&32767)|0)>>15;h=E+(l<<1)|0;r[O+(P-X<<1)>>1]=(E^l|0)>0&(h^E|0)<0?(E>>>31)+65535|0:h;if(D<<16>>16!=Y<<16>>16){C=C+-1<<16>>16;h=r[i+(C<<16>>16<<1)>>1]|0;if(d){E=b;l=T;c=R;u=L}else{E=U;l=T;c=R;u=L;while(1){P=(AA(r[c>>1]|0,h)|0)>>G;r[u>>1]=P+(w[l>>1]|0);P=(AA(r[c+-2>>1]|0,h)|0)>>G;r[u+-2>>1]=P+(w[l+-2>>1]|0);E=E+-1<<16>>16;if(!(E<<16>>16)){E=f;l=H;c=t;u=F;break}else{l=l+-4|0;c=c+-4|0;u=u+-4|0}}}P=(AA(r[c>>1]|0,h)|0)>>G;r[u>>1]=P+(w[l>>1]|0);r[I+(E+-1<<1)>>1]=h>>y}D=D+1<<16>>16;if(D<<16>>16>Y<<16>>16)break A;else P=D<<16>>16}}if(d){lA()}E=I+(f+-1<<1)|0;l=X;while(1){Ui(0,g)|0;r[O+(l-X<<1)>>1]=0;if(D<<16>>16!=Y<<16>>16){lA()}D=D+1<<16>>16;if(D<<16>>16>Y<<16>>16)break;else l=D<<16>>16}}}while(0);D=N<<16>>16;f=z+1&65535;if(f<<16>>16>K<<16>>16)t=N;else{C=N;B=r[O+(D-X<<1)>>1]|0;while(1){c=r[O+((f<<16>>16)-X<<1)>>1]|0;u=c<<16>>16<B<<16>>16;C=u?C:f;f=f+1<<16>>16;if(f<<16>>16>K<<16>>16){t=C;break}else B=u?B:c}}A:do{if(!(x<<16>>16==0?t<<16>>16>J<<16>>16:0)){if(!(e>>>0<4&x<<16>>16!=0)){C=O+((t<<16>>16)-X<<1)|0;c=gf(C,v,S,g)|0;f=(v&65535)+1&65535;if(f<<16>>16<=m<<16>>16)while(1){u=gf(C,f,S,g)|0;B=u<<16>>16>c<<16>>16;v=B?f:v;f=f+1<<16>>16;if(f<<16>>16>m<<16>>16)break;else c=B?u:c}if((e+-7|0)>>>0<2){m=v<<16>>16==-3;f=(m<<31>>31)+t<<16>>16;v=m?3:v;break}switch(v<<16>>16){case-2:{f=t+-1<<16>>16;v=1;break A}case 2:{f=t+1<<16>>16;v=-1;break A}default:{f=t;break A}}}J=r[A>>1]|0;J=((J<<16>>16)-D|0)>5?D+5&65535:J;B=K<<16>>16;J=(B-(J<<16>>16)|0)>4?B+65532&65535:J;B=t<<16>>16;f=J<<16>>16;if((B|0)==(f+-1|0)?1:t<<16>>16==J<<16>>16){C=O+(B-X<<1)|0;B=gf(C,v,S,g)|0;f=(v&65535)+1&65535;if(f<<16>>16<=m<<16>>16)while(1){c=gf(C,f,S,g)|0;u=c<<16>>16>B<<16>>16;v=u?f:v;f=f+1<<16>>16;if(f<<16>>16>m<<16>>16)break;else B=u?c:B}if((e+-7|0)>>>0<2){lA()}switch(v<<16>>16){case-2:{f=t+-1<<16>>16;v=1;break A}case 2:{f=t+1<<16>>16;v=-1;break A}default:{f=t;break A}}}if((B|0)==(f+-2|0)){f=O+(B-X<<1)|0;B=gf(f,0,S,g)|0;if((e|0)!=8){v=0;C=1;while(1){c=gf(f,C,S,g)|0;u=c<<16>>16>B<<16>>16;v=u?C:v;C=C+1<<16>>16;if(C<<16>>16>m<<16>>16)break;else B=u?c:B}if((e+-7|0)>>>0>=2)switch(v<<16>>16){case-2:{f=t+-1<<16>>16;v=1;break A}case 2:{f=t+1<<16>>16;v=-1;break A}default:{f=t;break A}}}else v=0;m=v<<16>>16==-3;f=(m<<31>>31)+t<<16>>16;v=m?3:v;break}if((B|0)==(f+1|0)){C=O+(B-X<<1)|0;f=gf(C,v,S,g)|0;B=(v&65535)+1&65535;if(B<<16>>16<=0)while(1){u=gf(C,B,S,g)|0;c=u<<16>>16>f<<16>>16;v=c?B:v;B=B+1<<16>>16;if(B<<16>>16>0)break;else f=c?u:f}if((e+-7|0)>>>0<2){lA()}switch(v<<16>>16){case-2:{f=t+-1<<16>>16;v=1;break A}case 2:{f=t+1<<16>>16;v=-1;break A}default:{f=t;break A}}}else{f=t;v=0}}else{f=t;v=0}}while(0);if((e+-7|0)>>>0>1){m=A;A=Af(f,v,r[A>>1]|0,N,K,x,e>>>0<4&1,g)|0;r[Q>>1]=A;r[m>>1]=f;r[o>>1]=S;r[a>>1]=v;s=j;return f|0}else{g=ef(f,v,N,x,g)|0;r[Q>>1]=g;r[A>>1]=f;r[o>>1]=S;r[a>>1]=v;s=j;return f|0}return 0}function Rf(A,e,f,i,t,w,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;var o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0;H=s;s=s+1200|0;M=H+1188|0;I=H+580|0;F=H+578|0;d=H+576|0;h=H;k=H+582|0;b=(B|0)!=0;if(b){lA()}P=t<<16>>16;g=0-P|0;Q=f+(g<<1)|0;g=g&65535;C=w<<16>>16;do{if(g<<16>>16<w<<16>>16){c=g;u=Q;g=0;while(1){D=r[u>>1]|0;g=(AA(D<<1,D)|0)+g|0;if((g|0)<0)break;c=c+1<<16>>16;if(c<<16>>16>=w<<16>>16){E=14;break}else u=u+2|0}if((E|0)==14){if((g|0)<1048576){E=15;break}or(k|0,Q|0,C+P<<1|0)|0;D=0;break}o=C+P|0;v=o>>>1;c=v&65535;if(!(c<<16>>16))g=k;else{D=((v<<1)+131070&131070)+2|0;C=D-P|0;u=k;while(1){r[u>>1]=(r[Q>>1]|0)>>>3;r[u+2>>1]=(r[Q+2>>1]|0)>>>3;c=c+-1<<16>>16;if(!(c<<16>>16))break;else{Q=Q+4|0;u=u+4|0}}Q=f+(C<<1)|0;g=k+(D<<1)|0}if(!(o&1))D=3;else{r[g>>1]=(r[Q>>1]|0)>>>3;D=3}}else E=15}while(0);if((E|0)==15){D=C+P|0;g=D>>>1;v=g&65535;if(!(v<<16>>16))g=k;else{C=((g<<1)+131070&131070)+2|0;u=C-P|0;c=k;while(1){r[c>>1]=r[Q>>1]<<3;r[c+2>>1]=r[Q+2>>1]<<3;v=v+-1<<16>>16;if(!(v<<16>>16))break;else{Q=Q+4|0;c=c+4|0}}Q=f+(u<<1)|0;g=k+(C<<1)|0}if(!(D&1))D=-3;else{r[g>>1]=r[Q>>1]<<3;D=-3}}C=h+(P<<2)|0;u=k+(P<<1)|0;Le(u,w,t,i,C);o=(e|0)==7&1;g=i<<16>>16;Q=g<<2;if((Q|0)!=(g<<18>>16|0)){n[a>>2]=1;Q=i<<16>>16>0?32767:-32768}c=Tf(A,C,u,D,o,w,t,Q&65535,M,B,a)|0;g=g<<1;v=Tf(A,C,u,D,o,w,Q+65535&65535,g&65535,I,B,a)|0;g=Tf(A,C,u,D,o,w,g+65535&65535,i,F,B,a)|0;if(l<<16>>16==1&b){lA()}Q=r[M>>1]|0;o=r[I>>1]|0;if(((Q<<16>>16)*55706>>16|0)>=(o<<16>>16|0)){I=Q;M=c;I=I<<16>>16;I=I*55706|0;I=I>>16;F=r[F>>1]|0;F=F<<16>>16;F=(I|0)<(F|0);F=F?g:M;s=H;return F|0}r[M>>1]=o;I=o;M=v;I=I<<16>>16;I=I*55706|0;I=I>>16;F=r[F>>1]|0;F=F<<16>>16;F=(I|0)<(F|0);F=F?g:M;s=H;return F|0}function Tf(A,e,f,i,t,w,l,B,a,s,o){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;var Q=0,g=0,v=0,u=0,c=0;if(l<<16>>16<B<<16>>16){B=-2147483648;v=l}else{v=l;Q=-2147483648;g=e+(0-(l<<16>>16)<<2)|0;e=l;while(1){l=n[g>>2]|0;c=(l|0)<(Q|0);e=c?e:v;Q=c?Q:l;v=v+-1<<16>>16;if(v<<16>>16<B<<16>>16){B=Q;v=e;break}else g=g+4|0}}e=w<<16>>16>>>2&65535;if(!(e<<16>>16))e=0;else{Q=e;l=f+(0-(v<<16>>16)<<1)|0;e=0;while(1){c=r[l>>1]|0;c=(AA(c,c)|0)+e|0;e=r[l+2>>1]|0;e=c+(AA(e,e)|0)|0;c=r[l+4>>1]|0;c=e+(AA(c,c)|0)|0;e=r[l+6>>1]|0;e=c+(AA(e,e)|0)|0;Q=Q+-1<<16>>16;if(!(Q<<16>>16))break;else l=l+8|0}e=e<<1}if(s)lA();e=Ui(e,o)|0;l=t<<16>>16!=0;if(l)e=(e|0)>1073741823?2147483647:e<<1;t=B>>16;A=e>>16;o=AA(A,t)|0;o=(o|0)==1073741824?2147483647:o<<1;e=(AA((e>>>1)-(A<<15)<<16>>16,t)|0)>>15;c=(e<<1)+o|0;c=(e^o|0)>0&(c^o|0)<0?(o>>>31)+2147483647|0:c;t=(AA(A,(B>>>1)-(t<<15)<<16>>16)|0)>>15;e=c+(t<<1)|0;e=(c^t|0)>0&(e^c|0)<0?(c>>>31)+2147483647|0:e;if(!l){r[a>>1]=e;return v|0}l=i<<16>>16;if(i<<16>>16>0)if(i<<16>>16<31){l=e>>l;u=16}else l=0;else{u=0-l<<16>>16;l=e<<u;l=(l>>u|0)==(e|0)?l:e>>31^2147483647;u=16}if((u|0)==16){if((l|0)>65535){r[a>>1]=32767;return v|0}if((l|0)<-65536){r[a>>1]=-32768;return v|0}}r[a>>1]=l>>>1;return v|0}function yf(A){A=A|0;var e=0;if(!A){A=-1;return A|0}n[A>>2]=0;e=lr(6)|0;if(!e){A=-1;return A|0}r[e>>1]=40;r[e+2>>1]=0;r[e+4>>1]=0;n[A>>2]=e;A=0;return A|0}function Yf(A){A=A|0;if(!A){A=-1;return A|0}r[A>>1]=40;r[A+2>>1]=0;r[A+4>>1]=0;A=0;return A|0}function zf(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function Xf(A,e,f,i,t,w,l,B,a,o,Q){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;var g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0;G=s;s=s+1200|0;P=G+1186|0;k=G+1184|0;U=G+1182|0;h=G;d=G+576|0;b=t<<16>>16;H=d+(b<<1)|0;g=(0-b&65535)<<16>>16<w<<16>>16;if(g){C=0-t<<16>>16<<16>>16;v=0;do{c=r[f+(C<<1)>>1]|0;c=AA(c,c)|0;if((c|0)!=1073741824){u=(c<<1)+v|0;if((c^v|0)>0&(u^v|0)<0){n[Q>>2]=1;v=(v>>>31)+2147483647|0}else v=u}else{n[Q>>2]=1;v=2147483647}C=C+1|0}while((C&65535)<<16>>16!=w<<16>>16)}else v=0;if((2147483646-v&v|0)>=0)if((v|0)==2147483647){if(g){v=0-t<<16>>16<<16>>16;do{r[d+(v+b<<1)>>1]=fr(r[f+(v<<1)>>1]|0,3,Q)|0;v=v+1|0}while((v&65535)<<16>>16!=w<<16>>16)}}else D=14;else{n[Q>>2]=1;D=14}do{if((D|0)==14){if((1048575-v&v|0)<0){n[Q>>2]=1;v=(v>>>31)+2147483647|0}else v=v+-1048576|0;if((v|0)>=0){if(!g)break;F=0-t<<16>>16<<16>>16;or(d+(b+F<<1)|0,f+(F<<1)|0,(((w+t<<16>>16)+-1&65535)<<1)+2|0)|0;break}if(g){v=0-t<<16>>16<<16>>16;do{F=r[f+(v<<1)>>1]|0;r[d+(v+b<<1)>>1]=(F<<19>>19|0)==(F|0)?F<<3:F>>>15^32767;v=v+1|0}while((v&65535)<<16>>16!=w<<16>>16)}}}while(0);M=h+(b<<2)|0;Le(H,w,t,i,M);C=r[A>>1]|0;F=A+4|0;I=B+(a<<16>>16<<1)|0;A:do{if(t<<16>>16<i<<16>>16)E=t;else{if((r[F>>1]|0)<=0){f=t;B=-2147483648;c=t;D=3402;while(1){Pf(n[h+(b-(f<<16>>16)<<2)>>2]|0,P,k,Q);u=r[k>>1]|0;v=r[D>>1]|0;C=AA(v,r[P>>1]|0)|0;if((C|0)==1073741824){n[Q>>2]=1;g=2147483647}else g=C<<1;E=(AA(v,u<<16>>16)|0)>>15;C=g+(E<<1)|0;if((g^E|0)>0&(C^g|0)<0){lA()}u=(C|0)<(B|0);c=u?c:f;f=f+-1<<16>>16;if(f<<16>>16<i<<16>>16){E=c;break A}else{B=u?B:C;D=D+-2|0}}}B=t;g=-2147483648;c=t;E=2902+(b+123-(C<<16>>16)<<1)|0;f=3402;while(1){Pf(n[h+(b-(B<<16>>16)<<2)>>2]|0,P,k,Q);D=r[k>>1]|0;u=r[f>>1]|0;C=AA(u,r[P>>1]|0)|0;if((C|0)==1073741824){n[Q>>2]=1;v=2147483647}else v=C<<1;D=(AA(u,D<<16>>16)|0)>>15;C=v+(D<<1)|0;if((v^D|0)>0&(C^v|0)<0){n[Q>>2]=1;C=(v>>>31)+2147483647|0}Pf(C,P,k,Q);D=r[k>>1]|0;u=r[E>>1]|0;C=AA(u,r[P>>1]|0)|0;if((C|0)==1073741824){n[Q>>2]=1;v=2147483647}else v=C<<1;D=(AA(u,D<<16>>16)|0)>>15;C=v+(D<<1)|0;if((v^D|0)>0&(C^v|0)<0){n[Q>>2]=1;C=(v>>>31)+2147483647|0}u=(C|0)<(g|0);c=u?c:B;B=B+-1<<16>>16;if(B<<16>>16<i<<16>>16){E=c;break}else{g=u?g:C;E=E+-2|0;f=f+-2|0}}}}while(0);if(w<<16>>16>0){B=0;f=H;D=d+(b-(E<<16>>16)<<1)|0;c=0;v=0;while(1){C=r[D>>1]|0;u=AA(C,r[f>>1]|0)|0;if((u|0)!=1073741824){g=(u<<1)+c|0;if((u^c|0)>0&(g^c|0)<0){lA()}else c=g}else{n[Q>>2]=1;c=2147483647}g=AA(C,C)|0;if((g|0)!=1073741824){u=(g<<1)+v|0;if((g^v|0)>0&(u^v|0)<0){lA()}else v=u}else{n[Q>>2]=1;v=2147483647}B=B+1<<16>>16;if(B<<16>>16>=w<<16>>16)break;else{f=f+2|0;D=D+2|0}}}else{c=0;v=0}u=(o|0)==0;if(!u){lA()}g=(er(v,Q)|0)<<16>>16;if((g*13107|0)==1073741824){n[Q>>2]=1;v=2147483647}else v=g*26214|0;g=c-v|0;if(((g^c)&(v^c)|0)<0){n[Q>>2]=1;g=(c>>>31)+2147483647|0}o=er(g,Q)|0;r[I>>1]=o;if(o<<16>>16>0){g=l+6|0;r[l+8>>1]=r[g>>1]|0;o=l+4|0;r[g>>1]=r[o>>1]|0;g=l+2|0;r[o>>1]=r[g>>1]|0;r[g>>1]=r[l>>1]|0;r[l>>1]=E;r[A>>1]=di(l,5)|0;r[A+2>>1]=32767;g=32767}else{r[A>>1]=E;A=A+2|0;g=((r[A>>1]|0)*29491|0)>>>15&65535;r[A>>1]=g}r[F>>1]=((nr(g,9830,Q)|0)&65535)>>>15^1;if(u){s=G;return E|0}if((nr(a,1,Q)|0)<<16>>16){s=G;return E|0}lA();return E|0}function Jf(A,e,f,i,r,n,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;r=r|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,o=0;B=s;s=s+48|0;o=B+22|0;a=B;e=A>>>0<6?e:f;f=n<<16>>16>0?22:0;A=r+(f<<1)|0;wr(A,e,o);wr(A,i,a);A=n<<16>>16;n=l+(A<<1)|0;Ar(o,t+(A<<1)|0,n,40);tr(a,n,n,40,w,1);f=r+(((f<<16)+720896|0)>>>16<<1)|0;wr(f,e,o);wr(f,i,a);A=(A<<16)+2621440>>16;l=l+(A<<1)|0;Ar(o,t+(A<<1)|0,l,40);tr(a,l,l,40,w,1);s=B;return}function Of(A){A=A|0;var e=0;if(!A){A=-1;return A|0}n[A>>2]=0;e=lr(12)|0;if(!e){A=-1;return A|0}r[e>>1]=0;r[e+2>>1]=0;r[e+4>>1]=0;r[e+6>>1]=0;r[e+8>>1]=0;r[e+10>>1]=0;n[A>>2]=e;A=0;return A|0}function mf(A){A=A|0;if(!A){A=-1;return A|0}r[A>>1]=0;r[A+2>>1]=0;r[A+4>>1]=0;r[A+6>>1]=0;r[A+8>>1]=0;r[A+10>>1]=0;A=0;return A|0}function Nf(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function Kf(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0;o=A+10|0;n=r[o>>1]|0;Q=A+8|0;i=r[Q>>1]|0;if(!(f<<16>>16)){lA()}l=A+4|0;B=A+6|0;a=A+2|0;w=r[B>>1]|0;s=r[l>>1]|0;t=f;f=n;while(1){g=(AA(r[A>>1]|0,-3733)|0)+(((s<<16>>16)*7807|0)+((w<<16>>16)*7807>>15))|0;r[A>>1]=s;g=g+((AA(r[a>>1]|0,-3733)|0)>>15)|0;r[a>>1]=w;g=((f<<16>>16)*1899|0)+g+(AA(i<<16>>16,-3798)|0)|0;f=r[e>>1]|0;g=g+((f<<16>>16)*1899|0)|0;r[e>>1]=(g+2048|0)>>>12;n=g>>>12;s=n&65535;r[l>>1]=s;w=(g<<3)-(n<<15)&65535;r[B>>1]=w;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{g=i;e=e+2|0;i=f;f=g}}r[o>>1]=i;r[Q>>1]=f;return}function xf(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var t=0,w=0,l=0,B=0;t=r[(n[i+88>>2]|0)+(A<<1)>>1]|0;if(!(t<<16>>16))return;B=f;l=n[(n[i+92>>2]|0)+(A<<2)>>2]|0;while(1){f=r[l>>1]|0;if(!(f<<16>>16))f=0;else{A=r[e>>1]|0;w=f;i=B+((f<<16>>16)+-1<<1)|0;while(1){f=A<<16>>16;r[i>>1]=f&1;w=w+-1<<16>>16;if(!(w<<16>>16))break;else{A=f>>>1&65535;i=i+-2|0}}f=r[l>>1]|0}e=e+2|0;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{B=B+(f<<16>>16<<1)|0;l=l+2|0}}return}function Sf(A,e,f,i,t,l){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;var B=0,a=0,o=0,Q=0,g=0;g=s;s=s+16|0;o=g+2|0;Q=g;B=t<<16>>16;if(t<<16>>16<1){l=-5443;Q=-32768;ki(A,Q,l);s=g;return}a=xi(14,f,l)|0;if((B|0)<(a<<16>>16|0))f=i;else{f=(i&65535)+1&65535;t=B>>>1&65535}i=Ei(t,a&65535)|0;r[Q>>1]=i;Gi(i<<16>>16,o,Q,l);r[o>>1]=((((f&65535)-(e&65535)<<16)+-65536|0)>>>16)+(w[o>>1]|0);i=ir(r[Q>>1]|0,5,l)|0;B=r[o>>1]|0;i=((B&65535)<<10)+(i&65535)&65535;if(i<<16>>16>18284){lA()}t=r[Q>>1]|0;B=B<<16>>16;if((B*24660|0)==1073741824){n[l>>2]=1;f=2147483647}else f=B*49320|0;Q=(t<<16>>16)*24660>>15;B=f+(Q<<1)|0;if((f^Q|0)>0&(B^f|0)<0){n[l>>2]=1;B=(f>>>31)+2147483647|0}Q=B<<13;l=er((Q>>13|0)==(B|0)?Q:B>>31^2147483647,l)|0;Q=i;ki(A,Q,l);s=g;return}function jf(A,e,f,i,t,l,B,a,o,Q,g,v,u,c,C,D,E,h,P,k){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;D=D|0;E=E|0;h=h|0;P=P|0;k=k|0;var b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0,wA=0;wA=s;s=s+80|0;iA=wA+66|0;rA=wA+64|0;nA=wA+62|0;tA=wA+60|0;O=wA+40|0;m=wA+20|0;X=wA;r[iA>>1]=e;r[rA>>1]=o;r[nA>>1]=Q;z=xi(14,f,k)|0;fA=z&65535;r[tA>>1]=fA;J=xi(14,Q,k)|0;Y=(w[i>>1]|0)+65523|0;r[X>>1]=Y;U=(w[i+2>>1]|0)+65522|0;G=X+2|0;r[G>>1]=U;L=((e&65535)<<16)+-720896|0;M=L>>16;L=(L>>>15)+15+(w[i+4>>1]|0)|0;R=X+4|0;r[R>>1]=L;T=(w[i+6>>1]|0)+M|0;y=X+6|0;r[y>>1]=T;M=M+1+(w[i+8>>1]|0)|0;F=X+8|0;r[F>>1]=M;b=(w[g>>1]|0)+65523&65535;r[X+10>>1]=b;H=(w[g+2>>1]|0)+65522&65535;r[X+12>>1]=H;d=((o&65535)<<16)+-720896|0;i=d>>16;d=(d>>>15)+15+(w[g+4>>1]|0)&65535;r[X+14>>1]=d;I=(w[g+6>>1]|0)+i&65535;r[X+16>>1]=I;i=i+1+(w[g+8>>1]|0)&65535;r[X+18>>1]=i;$=(l&65535)-(u&65535)<<16;o=$>>16;if(($|0)>0){Q=B;f=c<<16>>16>>o&65535}else{Q=B<<16>>16>>0-o&65535;f=c}if((ir(f,1,k)|0)<<16>>16>Q<<16>>16)f=1;else f=(((Q<<16>>16)+3>>2|0)>(f<<16>>16|0))<<31>>31;g=Y+f&65535;r[X>>1]=g;$=U+f&65535;r[G>>1]=$;q=L+f&65535;r[R>>1]=q;_=T+f&65535;r[y>>1]=_;Z=M+f&65535;r[F>>1]=Z;o=i<<16>>16>g<<16>>16?i:g;o=I<<16>>16>o<<16>>16?I:o;o=d<<16>>16>o<<16>>16?d:o;o=H<<16>>16>o<<16>>16?H:o;o=b<<16>>16>o<<16>>16?b:o;o=Z<<16>>16>o<<16>>16?Z:o;o=_<<16>>16>o<<16>>16?_:o;o=q<<16>>16>o<<16>>16?q:o;o=($<<16>>16>o<<16>>16?$:o)+1&65535;i=0;while(1){f=o-(g&65535)|0;g=f&65535;Q=w[t>>1]<<16;f=f<<16>>16;if(g<<16>>16>0)g=g<<16>>16<31?Q>>f:0;else{lA()}$=g>>16;r[O+(i<<1)>>1]=$;r[m+(i<<1)>>1]=(g>>>1)-($<<15);i=i+1|0;if((i|0)==5){f=5;Q=v;break}g=r[X+(i<<1)>>1]|0;t=t+2|0}while(1){i=o-(b&65535)|0;b=i&65535;g=w[Q>>1]<<16;i=i<<16>>16;if(b<<16>>16>0)g=b<<16>>16<31?g>>i:0;else{lA()}$=g>>16;r[O+(f<<1)>>1]=$;r[m+(f<<1)>>1]=(g>>>1)-($<<15);g=f+1|0;if((g&65535)<<16>>16==10)break;b=r[X+(g<<1)>>1]|0;f=g;Q=Q+2|0}N=z<<16>>16;K=r[O>>1]|0;x=r[m>>1]|0;S=r[O+2>>1]|0;j=r[m+2>>1]|0;p=r[O+4>>1]|0;W=r[m+4>>1]|0;V=r[O+6>>1]|0;Z=r[m+6>>1]|0;_=r[O+8>>1]|0;q=r[m+8>>1]|0;$=C&65535;u=J<<16>>16;l=r[O+10>>1]|0;I=r[m+10>>1]|0;d=r[O+12>>1]|0;t=r[m+12>>1]|0;f=r[O+14>>1]|0;Q=r[m+14>>1]|0;i=r[O+16>>1]|0;b=r[m+16>>1]|0;M=r[O+18>>1]|0;m=r[m+18>>1]|0;o=2147483647;O=0;g=0;F=782;do{X=r[F>>1]|0;T=(AA(N,r[F+2>>1]|0)|0)>>>15<<16;v=T>>16;L=X<<1;Y=(AA(L,X)|0)>>16;c=AA(Y,K)|0;if((c|0)==1073741824){n[k>>2]=1;y=2147483647}else y=c<<1;J=(AA(x,Y)|0)>>15;c=y+(J<<1)|0;if((y^J|0)>0&(c^y|0)<0){n[k>>2]=1;c=(y>>>31)+2147483647|0}Y=AA(S,X)|0;if((Y|0)==1073741824){n[k>>2]=1;y=2147483647}else y=Y<<1;J=(AA(j,X)|0)>>15;Y=y+(J<<1)|0;if((y^J|0)>0&(Y^y|0)<0){n[k>>2]=1;Y=(y>>>31)+2147483647|0}T=(AA(T>>15,v)|0)>>16;y=AA(p,T)|0;if((y|0)==1073741824){n[k>>2]=1;R=2147483647}else R=y<<1;J=(AA(W,T)|0)>>15;y=R+(J<<1)|0;if((R^J|0)>0&(y^R|0)<0){n[k>>2]=1;y=(R>>>31)+2147483647|0}T=AA(V,v)|0;if((T|0)==1073741824){n[k>>2]=1;R=2147483647}else R=T<<1;J=(AA(Z,v)|0)>>15;T=R+(J<<1)|0;if((R^J|0)>0&(T^R|0)<0){n[k>>2]=1;J=(R>>>31)+2147483647|0}else J=T;R=(AA(L,v)|0)>>16;T=AA(_,R)|0;if((T|0)==1073741824){n[k>>2]=1;L=2147483647}else L=T<<1;z=(AA(q,R)|0)>>15;T=L+(z<<1)|0;if((L^z|0)>0&(T^L|0)<0){n[k>>2]=1;T=(L>>>31)+2147483647|0}R=r[F+4>>1]|0;L=r[F+6>>1]|0;F=F+8|0;if((X-$&65535)<<16>>16<1?(eA=R<<16>>16,R<<16>>16<=C<<16>>16):0){U=(AA(L<<16>>16,u)|0)>>>15<<16;X=U>>16;H=eA<<1;L=(AA(H,eA)|0)>>16;R=AA(l,L)|0;if((R|0)==1073741824){n[k>>2]=1;G=2147483647}else G=R<<1;z=(AA(I,L)|0)>>15;R=G+(z<<1)|0;if((G^z|0)>0&(R^G|0)<0){n[k>>2]=1;R=(G>>>31)+2147483647|0}L=AA(d,eA)|0;if((L|0)==1073741824){n[k>>2]=1;G=2147483647}else G=L<<1;z=(AA(t,eA)|0)>>15;L=G+(z<<1)|0;if((G^z|0)>0&(L^G|0)<0){n[k>>2]=1;z=(G>>>31)+2147483647|0}else z=L;G=(AA(U>>15,X)|0)>>16;L=AA(f,G)|0;if((L|0)==1073741824){n[k>>2]=1;U=2147483647}else U=L<<1;v=(AA(Q,G)|0)>>15;L=U+(v<<1)|0;if((U^v|0)>0&(L^U|0)<0){n[k>>2]=1;v=(U>>>31)+2147483647|0}else v=L;L=AA(i,X)|0;if((L|0)==1073741824){n[k>>2]=1;G=2147483647}else G=L<<1;U=(AA(b,X)|0)>>15;L=G+(U<<1)|0;if((G^U|0)>0&(L^G|0)<0){n[k>>2]=1;B=(G>>>31)+2147483647|0}else B=L;G=(AA(H,X)|0)>>16;L=AA(M,G)|0;if((L|0)==1073741824){n[k>>2]=1;U=2147483647}else U=L<<1;X=(AA(m,G)|0)>>15;L=U+(X<<1)|0;if((U^X|0)>0&(L^U|0)<0){n[k>>2]=1;L=(U>>>31)+2147483647|0}X=Y+c+y+J+T+R+z+v+B+L|0;J=(X|0)<(o|0);o=J?X:o;g=J?O:g}O=O+1<<16>>16}while(O<<16>>16<256);C=(g&65535)<<18>>16;pf(A,782+(C<<1)|0,fA,e,D,E,k);Pi(A,0,a,rA,nA,iA,tA,k);a=(xi(14,r[nA>>1]|0,k)|0)&65535;pf(A,782+((C|2)<<1)|0,a,r[rA>>1]|0,h,P,k);s=wA;return g|0}function pf(A,e,f,i,t,l,B){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;var a=0,o=0,Q=0,g=0;g=s;s=s+16|0;o=g+2|0;Q=g;r[t>>1]=r[e>>1]|0;a=r[e+2>>1]|0;f=AA(f<<16>>16<<1,a)|0;t=10-(i&65535)|0;e=t&65535;t=t<<16>>16;if(e<<16>>16>0)e=e<<16>>16<31?f>>t:0;else{t=0-t<<16>>16;e=f<<t;e=(e>>t|0)==(f|0)?e:f>>31^2147483647}r[l>>1]=e>>>16;Gi(a,o,Q,B);r[o>>1]=(w[o>>1]|0)+65524;t=ir(r[Q>>1]|0,5,B)|0;i=r[o>>1]|0;t=((i&65535)<<10)+(t&65535)&65535;f=r[Q>>1]|0;i=i<<16>>16;if((i*24660|0)==1073741824){n[B>>2]=1;e=2147483647}else e=i*49320|0;Q=(f<<16>>16)*24660>>15;i=e+(Q<<1)|0;if(!((e^Q|0)>0&(i^e|0)<0)){B=i;B=B<<13;B=B+32768|0;B=B>>>16;B=B&65535;ki(A,t,B);s=g;return}n[B>>2]=1;B=(e>>>31)+2147483647|0;B=B<<13;B=B+32768|0;B=B>>>16;B=B&65535;ki(A,t,B);s=g;return}function Wf(A,e,f,i,t,l,B,a,o,Q,g,v,u,c,C,D,E,h,P,k,b){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;D=D|0;E=E|0;h=h|0;P=P|0;k=k|0;b=b|0;var d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0,wA=0,BA=0,aA=0,sA=0,oA=0;oA=s;s=s+80|0;BA=oA+72|0;aA=oA+70|0;sA=oA+68|0;tA=oA+66|0;wA=oA+56|0;_=oA+24|0;Z=oA+12|0;W=oA+48|0;V=oA+40|0;K=oA+34|0;S=oA+22|0;m=oA+6|0;N=oA;Zf(5,c,C,m,N,n[k+72>>2]|0,b)|0;M=xi(14,Q,b)|0;x=k+68|0;O=n[x>>2]|0;p=o<<16>>16;j=p+65526|0;c=(w[l>>1]|0)+65523&65535;r[wA>>1]=c;k=(w[l+2>>1]|0)+65522&65535;r[wA+2>>1]=k;iA=j<<16>>16;rA=((j<<17>>17|0)==(iA|0)?j<<1:iA>>>15^32767)+15+(w[l+4>>1]|0)&65535;r[wA+4>>1]=rA;nA=(w[l+6>>1]|0)+iA&65535;r[wA+6>>1]=nA;l=iA+1+(w[l+8>>1]|0)&65535;r[wA+8>>1]=l;k=k<<16>>16>c<<16>>16?k:c;k=rA<<16>>16>k<<16>>16?rA:k;k=nA<<16>>16>k<<16>>16?nA:k;k=(Ci(l<<16>>16>k<<16>>16?l:k,1,b)|0)&65535;l=c;c=0;while(1){Q=k-(l&65535)|0;l=Q&65535;I=w[t+(c<<1)>>1]<<16;Q=Q<<16>>16;if(l<<16>>16>0)Q=l<<16>>16<31?I>>Q:0;else{lA()}Pf(Q,_+(c<<1)|0,Z+(c<<1)|0,b);Q=c+1|0;if((Q|0)==5)break;l=r[wA+(Q<<1)>>1]|0;c=Q}X=_+2|0;J=Z+2|0;nA=M<<16>>16;q=_+4|0;$=Z+4|0;eA=_+6|0;fA=Z+6|0;iA=_+8|0;rA=Z+8|0;U=0;l=2147483647;t=0;Q=0;while(1){z=r[m+(t<<1)>>1]|0;M=AA(z,z)|0;if(M>>>0>1073741823){n[b>>2]=1;M=32767}else M=M>>>15;k=r[Z>>1]|0;I=M<<16>>16;M=AA(I,r[_>>1]|0)|0;if((M|0)==1073741824){n[b>>2]=1;c=2147483647}else c=M<<1;Y=(AA(k<<16>>16,I)|0)>>15;M=c+(Y<<1)|0;if((c^Y|0)>0&(M^c|0)<0){n[b>>2]=1;M=(c>>>31)+2147483647|0}k=r[J>>1]|0;I=AA(r[X>>1]|0,z)|0;if((I|0)!=1073741824){c=(I<<1)+M|0;if((I^M|0)>0&(c^M|0)<0){n[b>>2]=1;c=(M>>>31)+2147483647|0}}else{n[b>>2]=1;c=2147483647}M=(AA(k<<16>>16,z)|0)>>15;if((M|0)>32767){n[b>>2]=1;M=32767}Y=M<<16;M=(Y>>15)+c|0;if((Y>>16^c|0)>0&(M^c|0)<0){n[b>>2]=1;Y=(c>>>31)+2147483647|0}else Y=M;T=(Y>>>31)+2147483647|0;y=t&65535;M=U;L=0;R=O;do{I=(AA(r[R>>1]|0,nA)|0)>>15;R=R+6|0;if((I|0)>32767){n[b>>2]=1;I=32767}G=I<<16>>16;I=AA(G,G)|0;if((I|0)==1073741824){n[b>>2]=1;H=2147483647}else H=I<<1;Pf(H,BA,aA,b);I=AA(G,z)|0;if((I|0)==1073741824){n[b>>2]=1;H=2147483647}else H=I<<1;Pf(H,sA,tA,b);c=r[$>>1]|0;F=r[aA>>1]|0;I=r[q>>1]|0;k=r[BA>>1]|0;U=AA(k,I)|0;if((U|0)!=1073741824){H=(U<<1)+Y|0;if((U^Y|0)>0&(H^Y|0)<0){n[b>>2]=1;H=T}}else{n[b>>2]=1;H=2147483647}U=(AA(F<<16>>16,I)|0)>>15;if((U|0)>32767){n[b>>2]=1;U=32767}F=U<<16;U=(F>>15)+H|0;if((F>>16^H|0)>0&(U^H|0)<0){n[b>>2]=1;U=(H>>>31)+2147483647|0}H=(AA(k,c<<16>>16)|0)>>15;if((H|0)>32767){n[b>>2]=1;H=32767}F=H<<16;H=(F>>15)+U|0;if((F>>16^U|0)>0&(H^U|0)<0){n[b>>2]=1;H=(U>>>31)+2147483647|0}I=r[fA>>1]|0;U=AA(r[eA>>1]|0,G)|0;if((U|0)!=1073741824){F=(U<<1)+H|0;if((U^H|0)>0&(F^H|0)<0){lA()}}else{n[b>>2]=1;F=2147483647}I=(AA(I<<16>>16,G)|0)>>15;if((I|0)>32767){n[b>>2]=1;I=32767}G=I<<16;I=(G>>15)+F|0;if((G>>16^F|0)>0&(I^F|0)<0){n[b>>2]=1;I=(F>>>31)+2147483647|0}k=r[rA>>1]|0;F=r[tA>>1]|0;c=r[iA>>1]|0;d=r[sA>>1]|0;U=AA(d,c)|0;do{if((U|0)==1073741824){n[b>>2]=1;U=2147483647}else{H=(U<<1)+I|0;if(!((U^I|0)>0&(H^I|0)<0)){U=H;break}n[b>>2]=1;U=(I>>>31)+2147483647|0}}while(0);H=(AA(F<<16>>16,c)|0)>>15;if((H|0)>32767){n[b>>2]=1;H=32767}G=H<<16;H=(G>>15)+U|0;if((G>>16^U|0)>0&(H^U|0)<0){n[b>>2]=1;H=(U>>>31)+2147483647|0}I=(AA(d,k<<16>>16)|0)>>15;if((I|0)>32767){n[b>>2]=1;I=32767}G=I<<16;I=(G>>15)+H|0;if((G>>16^H|0)>0&(I^H|0)<0){n[b>>2]=1;I=(H>>>31)+2147483647|0}G=(I|0)<(l|0);M=G?L:M;Q=G?y:Q;l=G?I:l;L=L+1<<16>>16}while(L<<16>>16<32);t=t+1|0;if((t|0)==3){I=M;t=Q;break}else U=M}J=(I<<16>>16)*3|0;l=r[O+(J<<1)>>1]|0;r[E>>1]=r[O+(J+1<<1)>>1]|0;r[h>>1]=r[O+(J+2<<1)>>1]|0;l=AA(l<<16>>16,nA)|0;if((l|0)==1073741824){n[b>>2]=1;M=2147483647}else M=l<<1;J=9-p|0;O=J&65535;J=J<<16>>16;X=O<<16>>16>0;if(X)M=O<<16>>16<31?M>>J:0;else{Y=0-J<<16>>16;z=M<<Y;M=(z>>Y|0)==(M|0)?z:M>>31^2147483647}r[D>>1]=M>>>16;z=t<<16>>16;m=r[m+(z<<1)>>1]|0;r[C>>1]=m;N=r[N+(z<<1)>>1]|0;Re(e,f,i,m,g,W,V,K,b);wf(A,r[K>>1]|0,r[D>>1]|0,S,b);if(!((r[W>>1]|0)!=0&(r[S>>1]|0)>0)){b=I;E=n[P>>2]|0;D=E+2|0;r[E>>1]=N;E=E+4|0;n[P>>2]=E;r[D>>1]=b;s=oA;return}G=W+6|0;r[G>>1]=a;H=V+6|0;r[H>>1]=B;o=((nr(u,o,b)|0)&65535)+10|0;k=o<<16>>16;if((o&65535)<<16>>16<0){Q=0-k<<16;if((Q|0)<983040)v=v<<16>>16>>(Q>>16)&65535;else v=0}else{Q=v<<16>>16;c=Q<<k;if((c<<16>>16>>k|0)==(Q|0))v=c&65535;else v=(Q>>>15^32767)&65535}l=r[C>>1]|0;M=r[S>>1]|0;x=n[x>>2]|0;c=r[D>>1]|0;S=10-p|0;k=S<<16>>16;if((S&65535)<<16>>16<0){Q=0-k<<16;if((Q|0)<983040)a=c<<16>>16>>(Q>>16)&65535;else a=0}else{Q=c<<16>>16;c=Q<<k;if((c<<16>>16>>k|0)==(Q|0))a=c&65535;else a=(Q>>>15^32767)&65535}t=l<<16>>16;Q=AA(t,t)|0;if(Q>>>0>1073741823){n[b>>2]=1;l=32767}else l=Q>>>15;I=Ci(32767-(M&65535)&65535,1,b)|0;M=M<<16>>16;Q=AA(r[W+2>>1]|0,M)|0;if((Q|0)==1073741824){n[b>>2]=1;Q=2147483647}else Q=Q<<1;S=Q<<1;Q=AA(((S>>1|0)==(Q|0)?S:Q>>31^2147418112)>>16,l<<16>>16)|0;if((Q|0)==1073741824){n[b>>2]=1;U=2147483647}else U=Q<<1;F=(w[V+2>>1]|0)+65521|0;k=F&65535;Q=AA(r[W+4>>1]|0,M)|0;if((Q|0)==1073741824){n[b>>2]=1;l=2147483647}else l=Q<<1;Q=l<<1;Q=(AA(((Q>>1|0)==(l|0)?Q:l>>31^2147418112)>>16,t)|0)>>15;if((Q|0)>32767){n[b>>2]=1;Q=32767}r[q>>1]=Q;l=j&65535;r[BA>>1]=l;l=Ci(r[V+4>>1]|0,l,b)|0;Q=AA(r[G>>1]|0,M)|0;if((Q|0)==1073741824){n[b>>2]=1;Q=2147483647}else Q=Q<<1;d=Q<<1;r[eA>>1]=((d>>1|0)==(Q|0)?d:Q>>31^2147418112)>>>16;d=((p<<17>>17|0)==(p|0)?p<<1:p>>>15^32767)+65529&65535;r[BA>>1]=d;d=Ci(r[H>>1]|0,d,b)|0;Q=(AA(r[G>>1]|0,I<<16>>16)|0)>>15;if((Q|0)>32767){n[b>>2]=1;Q=32767}r[iA>>1]=Q;I=Ci(d,1,b)|0;c=AA(r[W>>1]|0,M)|0;if((c|0)==1073741824){n[b>>2]=1;Q=2147483647}else Q=c<<1;H=rr(Q,BA,b)|0;t=(w[BA>>1]|0)+47|0;r[BA>>1]=t;t=(w[V>>1]|0)-(t&65535)|0;M=t+31&65535;M=k<<16>>16>M<<16>>16?k:M;M=l<<16>>16>M<<16>>16?l:M;M=d<<16>>16>M<<16>>16?d:M;M=(I<<16>>16>M<<16>>16?I:M)<<16>>16;c=M-(F&65535)|0;Q=c&65535;c=c<<16>>16;if(Q<<16>>16>0)Y=Q<<16>>16<31?U>>c:0;else{V=0-c<<16>>16;Y=U<<V;Y=(Y>>V|0)==(U|0)?Y:U>>31^2147483647}k=M-(l&65535)|0;Q=k&65535;c=w[q>>1]<<16;k=k<<16>>16;if(Q<<16>>16>0)c=Q<<16>>16<31?c>>k:0;else{W=0-k<<16>>16;V=c<<W;c=(V>>W|0)==(c|0)?V:c>>31^2147483647}Pf(c,q,$,b);d=M-(d&65535)|0;c=d&65535;k=w[eA>>1]<<16;d=d<<16>>16;if(c<<16>>16>0)c=c<<16>>16<31?k>>d:0;else{lA()}Pf(c,eA,fA,b);d=M-(I&65535)|0;c=d&65535;k=w[iA>>1]<<16;d=d<<16>>16;if(c<<16>>16>0)c=c<<16>>16<31?k>>d:0;else{V=0-d<<16>>16;c=k<<V;c=(c>>V|0)==(k|0)?c:k>>31^2147483647}Pf(c,iA,rA,b);d=M+65505|0;r[BA>>1]=d;d=d-(t&65535)|0;c=fr(d&65535,1,b)|0;k=c<<16>>16;if(c<<16>>16>0)k=c<<16>>16<31?H>>k:0;else{V=0-k<<16>>16;k=H<<V;k=(k>>V|0)==(H|0)?k:H>>31^2147483647}do{if(!(d&1))U=k;else{Pf(k,_,Z,b);c=r[Z>>1]|0;k=r[_>>1]|0;if((k*23170|0)==1073741824){n[b>>2]=1;d=2147483647}else d=k*46340|0;_=(c<<16>>16)*23170>>15;k=d+(_<<1)|0;if(!((d^_|0)>0&(k^d|0)<0)){U=k;break}n[b>>2]=1;U=(d>>>31)+2147483647|0}}while(0);G=(Y>>>31)+2147483647|0;H=2147483647;F=0;k=0;L=x;while(1){c=(AA(r[L>>1]|0,nA)|0)>>15;L=L+6|0;if((c|0)>32767){n[b>>2]=1;c=32767}d=c&65535;if(d<<16>>16>=a<<16>>16)break;l=c<<16>>16;c=AA(l,l)|0;if((c|0)==1073741824){n[b>>2]=1;Q=2147483647}else Q=c<<1;Pf(Q,aA,sA,b);c=(nr(d,v,b)|0)<<16>>16;c=AA(c,c)|0;if((c|0)==1073741824){n[b>>2]=1;c=2147483647}else c=c<<1;Pf(c,tA,wA,b);d=r[$>>1]|0;Q=AA(r[q>>1]|0,l)|0;do{if((Q|0)==1073741824){n[b>>2]=1;Q=2147483647}else{c=(Q<<1)+Y|0;if(!((Q^Y|0)>0&(c^Y|0)<0)){Q=c;break}n[b>>2]=1;Q=G}}while(0);c=(AA(d<<16>>16,l)|0)>>15;if((c|0)>32767){n[b>>2]=1;c=32767}_=c<<16;c=(_>>15)+Q|0;if((_>>16^Q|0)>0&(c^Q|0)<0){n[b>>2]=1;c=(Q>>>31)+2147483647|0}t=r[fA>>1]|0;I=r[sA>>1]|0;l=r[eA>>1]|0;M=r[aA>>1]|0;Q=AA(M,l)|0;do{if((Q|0)==1073741824){n[b>>2]=1;d=2147483647}else{d=(Q<<1)+c|0;if(!((Q^c|0)>0&(d^c|0)<0))break;n[b>>2]=1;d=(c>>>31)+2147483647|0}}while(0);Q=(AA(I<<16>>16,l)|0)>>15;if((Q|0)>32767){n[b>>2]=1;Q=32767}_=Q<<16;Q=(_>>15)+d|0;if((_>>16^d|0)>0&(Q^d|0)<0){n[b>>2]=1;Q=(d>>>31)+2147483647|0}c=(AA(M,t<<16>>16)|0)>>15;if((c|0)>32767){n[b>>2]=1;c=32767}_=c<<16;c=(_>>15)+Q|0;if((_>>16^Q|0)>0&(c^Q|0)<0){n[b>>2]=1;c=(Q>>>31)+2147483647|0}c=rr(c,BA,b)|0;d=fr(r[BA>>1]|0,1,b)|0;Q=d<<16>>16;if(d<<16>>16>0)d=d<<16>>16<31?c>>Q:0;else{_=0-Q<<16>>16;d=c<<_;d=(d>>_|0)==(c|0)?d:c>>31^2147483647}c=d-U|0;if(((c^d)&(d^U)|0)<0){n[b>>2]=1;c=(d>>>31)+2147483647|0}c=(er(c,b)|0)<<16>>16;c=AA(c,c)|0;if((c|0)==1073741824){n[b>>2]=1;d=2147483647}else d=c<<1;M=r[rA>>1]|0;l=r[wA>>1]|0;I=r[iA>>1]|0;t=r[tA>>1]|0;Q=AA(t,I)|0;do{if((Q|0)==1073741824){n[b>>2]=1;c=2147483647}else{c=(Q<<1)+d|0;if(!((Q^d|0)>0&(c^d|0)<0))break;n[b>>2]=1;c=(d>>>31)+2147483647|0}}while(0);Q=(AA(l<<16>>16,I)|0)>>15;if((Q|0)>32767){n[b>>2]=1;Q=32767}_=Q<<16;Q=(_>>15)+c|0;if((_>>16^c|0)>0&(Q^c|0)<0){n[b>>2]=1;Q=(c>>>31)+2147483647|0}c=(AA(t,M<<16>>16)|0)>>15;if((c|0)>32767){n[b>>2]=1;c=32767}_=c<<16;c=(_>>15)+Q|0;if((_>>16^Q|0)>0&(c^Q|0)<0){n[b>>2]=1;c=(Q>>>31)+2147483647|0}Q=(c|0)<(H|0);k=Q?F:k;F=F+1<<16>>16;if(F<<16>>16>=32)break;else H=Q?c:H}sA=(k<<16>>16)*3|0;d=r[x+(sA<<1)>>1]|0;r[E>>1]=r[x+(sA+1<<1)>>1]|0;r[h>>1]=r[x+(sA+2<<1)>>1]|0;d=AA(d<<16>>16,nA)|0;if((d|0)==1073741824){n[b>>2]=1;d=2147483647}else d=d<<1;if(X)d=O<<16>>16<31?d>>J:0;else{E=0-J<<16>>16;b=d<<E;d=(b>>E|0)==(d|0)?b:d>>31^2147483647}r[D>>1]=d>>>16;b=k;E=n[P>>2]|0;D=E+2|0;r[E>>1]=N;E=E+4|0;n[P>>2]=E;r[D>>1]=b;s=oA;return}function Vf(A,e,f,i,n,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,s=0,o=0,Q=0;Q=(A|0)==7;B=r[i>>1]|0;if(Q){B=B<<16>>16>>>1&65535;o=xi(e,f,l)|0;e=o<<16;A=e>>16;if((o<<20>>20|0)==(A|0))A=e>>12;else A=A>>>15^32767}else{lA()}o=A<<16>>16;l=B<<16>>16;e=l-((AA(o,r[w>>1]|0)|0)>>>15&65535)|0;e=((e&32768|0)!=0?0-e|0:e)&65535;a=1;A=0;s=w;while(1){s=s+6|0;B=l-((AA(r[s>>1]|0,o)|0)>>>15&65535)|0;f=B<<16;B=(f|0)<0?0-(f>>16)|0:B;f=(B<<16>>16|0)<(e<<16>>16|0);A=f?a:A;a=a+1<<16>>16;if(a<<16>>16>=32)break;else e=f?B&65535:e}s=(A<<16>>16)*196608>>16;r[i>>1]=(AA(r[w+(s<<1)>>1]|0,o)|0)>>>15<<(Q&1);r[n>>1]=r[w+(s+1<<1)>>1]|0;r[t>>1]=r[w+(s+2<<1)>>1]|0;return A|0}function Zf(A,e,f,i,n,t,w){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;w=w|0;var l=0,B=0,a=0,s=0,o=0,Q=0;l=nr(r[f>>1]|0,r[t>>1]|0,w)|0;l=(l&65535)-((l&65535)>>>15&65535)|0;l=(l<<16>>31^l)&65535;a=0;s=1;while(1){B=r[t+(s<<1)>>1]|0;if(B<<16>>16>e<<16>>16)B=l;else{B=nr(r[f>>1]|0,B,w)|0;B=(B&65535)-((B&65535)>>>15&65535)|0;B=(B<<16>>31^B)&65535;Q=B<<16>>16<l<<16>>16;B=Q?B:l;a=Q?s&65535:a}s=s+1|0;if((s|0)==16)break;else l=B}if((A|0)!=5){l=r[t+(a<<16>>16<<1)>>1]|0;if((A|0)==7){r[f>>1]=l&65532;return a|0}else{r[f>>1]=l;return a|0}}B=a<<16>>16;switch(a<<16>>16){case 0:{l=0;break}case 15:{o=8;break}default:if((r[t+(B+1<<1)>>1]|0)>e<<16>>16)o=8;else l=B+65535&65535}if((o|0)==8)l=B+65534&65535;r[n>>1]=l;Q=l<<16>>16;r[i>>1]=r[t+(Q<<1)>>1]|0;Q=Q+1|0;r[n+2>>1]=Q;Q=Q<<16>>16;r[i+2>>1]=r[t+(Q<<1)>>1]|0;Q=Q+1|0;r[n+4>>1]=Q;r[i+4>>1]=r[t+(Q<<16>>16<<1)>>1]|0;r[f>>1]=r[t+(B<<1)>>1]|0;return a|0}function _f(A,e,f,i,t,l,B,a,o,Q,g,v){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;v=v|0;var u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0;Y=s;s=s+32|0;C=Y+20|0;D=Y+10|0;c=Y;switch(A|0){case 3:case 4:case 6:{g=g+84|0;y=128;break}default:{g=g+80|0;y=64}}T=n[g>>2]|0;u=xi(14,f,v)|0;R=e<<16>>16;L=R+65525|0;A=(w[t>>1]|0)+65523&65535;r[c>>1]=A;e=(w[t+2>>1]|0)+65522&65535;r[c+2>>1]=e;G=L<<16>>16;G=Ci(r[t+4>>1]|0,((L<<17>>17|0)==(G|0)?L<<1:G>>>15^32767)+15&65535,v)|0;r[c+4>>1]=G;L=Ci(r[t+6>>1]|0,L&65535,v)|0;r[c+6>>1]=L;t=Ci(r[t+8>>1]|0,R+65526&65535,v)|0;r[c+8>>1]=t;e=e<<16>>16>A<<16>>16?e:A;e=G<<16>>16>e<<16>>16?G:e;e=L<<16>>16>e<<16>>16?L:e;e=(t<<16>>16>e<<16>>16?t:e)+1&65535;t=0;while(1){f=e-(A&65535)|0;g=f&65535;A=w[i+(t<<1)>>1]<<16;f=f<<16>>16;if(g<<16>>16>0)g=g<<16>>16<31?A>>f:0;else{lA()}Pf(g,C+(t<<1)|0,D+(t<<1)|0,v);g=t+1|0;if((g|0)==5)break;A=r[c+(g<<1)>>1]|0;t=g}L=u<<16>>16;b=r[C>>1]|0;d=r[D>>1]|0;I=r[C+2>>1]|0;M=r[D+2>>1]|0;F=r[C+4>>1]|0;H=r[D+4>>1]|0;U=r[C+6>>1]|0;G=r[D+6>>1]|0;k=r[C+8>>1]|0;E=r[D+8>>1]|0;e=2147483647;h=0;g=0;P=T;while(1){t=r[P>>1]|0;if(t<<16>>16>l<<16>>16)u=e;else{u=(AA(r[P+2>>1]|0,L)|0)>>15;if((u|0)>32767){n[v>>2]=1;u=32767}D=t<<16>>16;t=AA(D,D)|0;if(t>>>0>1073741823){n[v>>2]=1;c=32767}else c=t>>>15;f=u<<16>>16;u=AA(f,f)|0;if(u>>>0>1073741823){n[v>>2]=1;C=32767}else C=u>>>15;i=(AA(f,D)|0)>>15;if((i|0)>32767){n[v>>2]=1;i=32767}u=c<<16>>16;c=AA(b,u)|0;if((c|0)==1073741824){n[v>>2]=1;t=2147483647}else t=c<<1;u=(AA(d,u)|0)>>15;c=t+(u<<1)|0;if((t^u|0)>0&(c^t|0)<0){n[v>>2]=1;c=(t>>>31)+2147483647|0}u=AA(I,D)|0;if((u|0)==1073741824){n[v>>2]=1;t=2147483647}else t=u<<1;D=(AA(M,D)|0)>>15;u=t+(D<<1)|0;if((t^D|0)>0&(u^t|0)<0){n[v>>2]=1;u=(t>>>31)+2147483647|0}t=u+c|0;if((u^c|0)>-1&(t^c|0)<0){n[v>>2]=1;t=(c>>>31)+2147483647|0}u=C<<16>>16;c=AA(F,u)|0;if((c|0)==1073741824){n[v>>2]=1;A=2147483647}else A=c<<1;D=(AA(H,u)|0)>>15;c=A+(D<<1)|0;if((A^D|0)>0&(c^A|0)<0){n[v>>2]=1;c=(A>>>31)+2147483647|0}u=c+t|0;if((c^t|0)>-1&(u^t|0)<0){n[v>>2]=1;A=(t>>>31)+2147483647|0}else A=u;u=AA(U,f)|0;if((u|0)==1073741824){n[v>>2]=1;c=2147483647}else c=u<<1;D=(AA(G,f)|0)>>15;u=c+(D<<1)|0;if((c^D|0)>0&(u^c|0)<0){n[v>>2]=1;u=(c>>>31)+2147483647|0}t=u+A|0;if((u^A|0)>-1&(t^A|0)<0){n[v>>2]=1;c=(A>>>31)+2147483647|0}else c=t;t=i<<16>>16;u=AA(k,t)|0;if((u|0)==1073741824){n[v>>2]=1;A=2147483647}else A=u<<1;D=(AA(E,t)|0)>>15;u=A+(D<<1)|0;if((A^D|0)>0&(u^A|0)<0){n[v>>2]=1;t=(A>>>31)+2147483647|0}else t=u;u=t+c|0;if((t^c|0)>-1&(u^c|0)<0){n[v>>2]=1;u=(c>>>31)+2147483647|0}D=(u|0)<(e|0);u=D?u:e;g=D?h:g}P=P+8|0;h=h+1<<16>>16;if((h<<16>>16|0)>=(y|0))break;else e=u}l=g<<16>>16;l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16;r[B>>1]=r[T+(l<<1)>>1]|0;e=r[T+(l+1<<1)>>1]|0;r[o>>1]=r[T+(l+2<<1)>>1]|0;r[Q>>1]=r[T+(l+3<<1)>>1]|0;e=AA(e<<16>>16,L)|0;if((e|0)==1073741824){n[v>>2]=1;A=2147483647}else A=e<<1;f=10-R|0;e=f&65535;f=f<<16>>16;if(e<<16>>16>0){v=e<<16>>16<31?A>>f:0;v=v>>>16;v=v&65535;r[a>>1]=v;s=Y;return g|0}else{o=0-f<<16>>16;v=A<<o;v=(v>>o|0)==(A|0)?v:A>>31^2147483647;v=v>>>16;v=v&65535;r[a>>1]=v;s=Y;return g|0}return 0}function qf(A,e,f,i,n,t,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;l=l|0;B=B|0;a=a|0;var o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0,W=0,V=0,Z=0,_=0,q=0,$=0,eA=0,fA=0,iA=0,rA=0,nA=0,tA=0,wA=0,BA=0,aA=0,sA=0,oA=0,QA=0,gA=0,vA=0,uA=0,cA=0,CA=0,DA=0,EA=0,hA=0,PA=0,kA=0;kA=s;s=s+160|0;PA=kA;Q=A<<16>>16;EA=A<<16>>16==10;hA=r[l+(r[t>>1]<<1)>>1]|0;if(A<<16>>16>0){a=0;o=B;while(1){r[o>>1]=a;a=a+1<<16>>16;if(a<<16>>16>=A<<16>>16)break;else o=o+2|0}}if(f<<16>>16<=1){s=kA;return}CA=t+2|0;DA=hA<<16>>16;vA=i+(DA<<1)|0;uA=n+(DA*80|0)+(DA<<1)|0;cA=t+6|0;V=e&65535;Z=t+4|0;_=t+10|0;q=t+8|0;$=t+14|0;eA=t+12|0;fA=t+18|0;iA=t+16|0;rA=B+2|0;nA=B+4|0;tA=B+6|0;wA=B+8|0;BA=B+10|0;aA=B+12|0;sA=B+14|0;oA=B+16|0;QA=B+18|0;gA=A<<16>>16>2;p=t+(Q+-1<<1)|0;S=1;W=1;J=0;O=0;j=-1;while(1){x=r[l+(r[CA>>1]<<1)>>1]|0;K=x<<16>>16;e=(w[i+(K<<1)>>1]|0)+(w[vA>>1]|0)|0;o=(r[n+(DA*80|0)+(K<<1)>>1]<<13)+32768+((r[n+(K*80|0)+(K<<1)>>1]|0)+(r[uA>>1]|0)<<12)|0;Q=r[cA>>1]|0;if(Q<<16>>16<40){Q=Q<<16>>16;g=PA;while(1){m=(r[n+(Q*80|0)+(Q<<1)>>1]|0)>>>1;X=r[n+(Q*80|0)+(DA<<1)>>1]|0;N=r[n+(Q*80|0)+(K<<1)>>1]|0;r[g>>1]=e+(w[i+(Q<<1)>>1]|0);r[g+2>>1]=(X+2+m+N|0)>>>2;Q=Q+V|0;if((Q&65535)<<16>>16<40){Q=Q<<16>>16;g=g+4|0}else break}M=r[cA>>1]|0}else M=Q;e=r[Z>>1]|0;I=o>>12;Q=e<<16>>16;A:do{if(e<<16>>16<40){d=M<<16>>16;if(M<<16>>16<40){g=1;u=e;C=M;c=0;v=-1}else while(1){lA()}while(1){b=((r[n+(Q*80|0)+(Q<<1)>>1]|0)+I>>1)+(r[n+(Q*80|0)+(DA<<1)>>1]|0)+(r[n+(Q*80|0)+(K<<1)>>1]|0)|0;k=w[i+(Q<<1)>>1]|0;h=d;P=M;E=PA;D=c;while(1){o=(w[E>>1]|0)+k|0;a=o<<16>>16;a=(AA(a,a)|0)>>>15;c=(b+(r[n+(Q*80|0)+(h<<1)>>1]|0)>>2)+(r[E+2>>1]|0)>>1;if((AA(a<<16>>16,g<<16>>16)|0)>(AA(c,v<<16>>16)|0)){g=c&65535;u=e;C=P;c=o&65535;v=a&65535}else c=D;o=h+V|0;P=o&65535;if(P<<16>>16>=40)break;else{h=o<<16>>16;E=E+4|0;D=c}}Q=Q+V|0;e=Q&65535;if(e<<16>>16<40)Q=Q<<16>>16;else{N=u;m=C;Q=c;break}}}else{g=1;N=e;m=M;Q=0}}while(0);u=g<<16>>16<<15;g=r[_>>1]|0;if(g<<16>>16<40){o=N<<16>>16;a=m<<16>>16;e=Q&65535;g=g<<16>>16;Q=PA;while(1){y=r[n+(g*80|0)+(g<<1)>>1]>>1;T=r[n+(g*80|0)+(DA<<1)>>1]|0;Y=r[n+(g*80|0)+(K<<1)>>1]|0;z=r[n+(g*80|0)+(o<<1)>>1]|0;X=r[n+(g*80|0)+(a<<1)>>1]|0;r[Q>>1]=(w[i+(g<<1)>>1]|0)+e;r[Q+2>>1]=(T+2+y+Y+z+X|0)>>>2;g=g+V|0;if((g&65535)<<16>>16<40){g=g<<16>>16;Q=Q+4|0}else break}y=r[_>>1]|0}else y=g;v=r[q>>1]|0;g=v<<16>>16;A:do{if(v<<16>>16<40){F=N<<16>>16;H=m<<16>>16;U=y<<16>>16;M=u+32768|0;if(y<<16>>16<40){c=1;u=v;e=y;C=v;Q=0;v=-1}else while(1){lA()}while(1){a=w[i+(g<<1)>>1]|0;I=(r[n+(g*80|0)+(K<<1)>>1]|0)+(r[n+(g*80|0)+(DA<<1)>>1]|0)+(r[n+(g*80|0)+(F<<1)>>1]|0)+(r[n+(g*80|0)+(H<<1)>>1]|0)|0;d=M+(r[n+(g*80|0)+(g<<1)>>1]<<11)|0;k=U;h=y;b=PA;while(1){D=(w[b>>1]|0)+a|0;o=d+(r[b+2>>1]<<14)+(I+(r[n+(g*80|0)+(k<<1)>>1]|0)<<12)|0;E=D<<16>>16;E=(AA(E,E)|0)>>>15;if((AA(E<<16>>16,c<<16>>16)|0)>(AA(o>>16,v<<16>>16)|0)){c=o>>>16&65535;P=C;e=h;Q=D&65535;v=E&65535}else P=u;u=k+V|0;h=u&65535;if(h<<16>>16>=40){u=P;break}else{k=u<<16>>16;u=P;b=b+4|0}}g=g+V|0;C=g&65535;if(C<<16>>16<40)g=g<<16>>16;else{g=c;X=u;z=e;break}}}else{g=1;X=v;z=y;Q=0}}while(0);c=g<<16>>16<<15;g=r[$>>1]|0;if(g<<16>>16<40){o=N<<16>>16;a=m<<16>>16;v=X<<16>>16;u=z<<16>>16;e=Q&65535;g=g<<16>>16;Q=PA;while(1){G=r[n+(g*80|0)+(g<<1)>>1]>>1;U=r[n+(DA*80|0)+(g<<1)>>1]|0;L=r[n+(K*80|0)+(g<<1)>>1]|0;R=r[n+(o*80|0)+(g<<1)>>1]|0;T=r[n+(a*80|0)+(g<<1)>>1]|0;y=r[n+(v*80|0)+(g<<1)>>1]|0;Y=r[n+(u*80|0)+(g<<1)>>1]|0;r[Q>>1]=(w[i+(g<<1)>>1]|0)+e;r[Q+2>>1]=(U+4+G+L+R+T+y+Y|0)>>>3;g=g+V|0;if((g&65535)<<16>>16<40){g=g<<16>>16;Q=Q+4|0}else break}e=r[$>>1]|0}else e=g;C=r[eA>>1]|0;if(C<<16>>16<40){y=N<<16>>16;G=m<<16>>16;U=X<<16>>16;H=z<<16>>16;F=e<<16>>16;M=e<<16>>16<40;L=c+32768|0;T=C<<16>>16;a=1;P=C;h=e;R=C;u=0;g=-1;while(1){if(M){c=w[i+(T<<1)>>1]|0;Q=(r[n+(T*80|0)+(K<<1)>>1]|0)+(r[n+(T*80|0)+(DA<<1)>>1]|0)+(r[n+(T*80|0)+(y<<1)>>1]|0)+(r[n+(T*80|0)+(G<<1)>>1]|0)+(r[n+(T*80|0)+(U<<1)>>1]|0)+(r[n+(T*80|0)+(H<<1)>>1]|0)|0;v=L+(r[n+(T*80|0)+(T<<1)>>1]<<10)|0;E=F;C=e;d=h;I=PA;while(1){b=(w[I>>1]|0)+c|0;h=v+(r[I+2>>1]<<14)+(Q+(r[n+(T*80|0)+(E<<1)>>1]|0)<<11)|0;k=b<<16>>16;k=(AA(k,k)|0)>>>15;if((AA(k<<16>>16,a<<16>>16)|0)>(AA(h>>16,g<<16>>16)|0)){a=h>>>16&65535;P=R;h=C;u=b&65535;g=k&65535}else h=d;D=E+V|0;C=D&65535;if(C<<16>>16>=40)break;else{E=D<<16>>16;d=h;I=I+4|0}}}C=T+V|0;R=C&65535;if(R<<16>>16>=40){Y=h;break}else T=C<<16>>16}}else{a=1;P=C;Y=e;u=0;g=-1}if(EA){E=a<<16>>16<<15;g=r[fA>>1]|0;if(g<<16>>16<40){Q=N<<16>>16;e=m<<16>>16;o=X<<16>>16;a=z<<16>>16;c=P<<16>>16;C=Y<<16>>16;v=u&65535;g=g<<16>>16;u=PA;while(1){U=r[n+(g*80|0)+(g<<1)>>1]>>1;H=r[n+(DA*80|0)+(g<<1)>>1]|0;G=r[n+(K*80|0)+(g<<1)>>1]|0;L=r[n+(Q*80|0)+(g<<1)>>1]|0;R=r[n+(e*80|0)+(g<<1)>>1]|0;T=r[n+(o*80|0)+(g<<1)>>1]|0;y=r[n+(a*80|0)+(g<<1)>>1]|0;J=r[n+(c*80|0)+(g<<1)>>1]|0;O=r[n+(C*80|0)+(g<<1)>>1]|0;r[u>>1]=(w[i+(g<<1)>>1]|0)+v;r[u+2>>1]=(H+4+U+G+L+R+T+y+J+O|0)>>>3;g=g+V|0;if((g&65535)<<16>>16<40){g=g<<16>>16;u=u+4|0}else break}y=r[fA>>1]|0}else y=g;c=r[iA>>1]|0;if(c<<16>>16<40){U=N<<16>>16;H=m<<16>>16;F=X<<16>>16;o=z<<16>>16;G=P<<16>>16;L=Y<<16>>16;R=y<<16>>16;T=y<<16>>16<40;M=E+32768|0;Q=c<<16>>16;a=1;C=c;u=y;e=c;g=-1;while(1){if(T){E=w[i+(Q<<1)>>1]|0;v=(r[n+(K*80|0)+(Q<<1)>>1]|0)+(r[n+(DA*80|0)+(Q<<1)>>1]|0)+(r[n+(U*80|0)+(Q<<1)>>1]|0)+(r[n+(H*80|0)+(Q<<1)>>1]|0)+(r[n+(F*80|0)+(Q<<1)>>1]|0)+(r[n+(o*80|0)+(Q<<1)>>1]|0)+(r[n+(G*80|0)+(Q<<1)>>1]|0)+(r[n+(L*80|0)+(Q<<1)>>1]|0)|0;c=M+(r[n+(Q*80|0)+(Q<<1)>>1]<<9)|0;I=R;k=y;d=PA;while(1){b=(w[d>>1]|0)+E<<16>>16;b=(AA(b,b)|0)>>>15;h=c+(r[d+2>>1]<<13)+(v+(r[n+(Q*80|0)+(I<<1)>>1]|0)<<10)|0;if((AA(b<<16>>16,a<<16>>16)|0)>(AA(h>>16,g<<16>>16)|0)){a=h>>>16&65535;C=e;u=k;g=b&65535}D=I+V|0;k=D&65535;if(k<<16>>16>=40)break;else{I=D<<16>>16;d=d+4|0}}}c=Q+V|0;e=c&65535;if(e<<16>>16>=40)break;else Q=c<<16>>16}}else{a=1;C=c;u=y;g=-1}}else{C=J;u=O}if((AA(g<<16>>16,S<<16>>16)|0)>(AA(a<<16>>16,j<<16>>16)|0)){r[B>>1]=hA;r[rA>>1]=x;r[nA>>1]=N;r[tA>>1]=m;r[wA>>1]=X;r[BA>>1]=z;r[aA>>1]=P;r[sA>>1]=Y;if(EA){r[oA>>1]=C;r[QA>>1]=u}}else{a=S;g=j}Q=r[CA>>1]|0;if(gA){e=1;o=2;while(1){r[t+(e<<1)>>1]=r[t+(o<<1)>>1]|0;o=o+1|0;if((o&65535)<<16>>16==A<<16>>16)break;else e=e+1|0}}r[p>>1]=Q;W=W+1<<16>>16;if(W<<16>>16>=f<<16>>16)break;else{S=a;J=C;O=u;j=g}}s=kA;return}function $f(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0,a=0;l=39;while(1){w=A+(l<<1)|0;t=r[w>>1]|0;n=e+(l<<1)|0;if(t<<16>>16>-1)r[n>>1]=32767;else{r[n>>1]=-32767;if(t<<16>>16==-32768)t=32767;else t=0-(t&65535)&65535;r[w>>1]=t}r[f+(l<<1)>>1]=t;if((l|0)>0)l=l+-1|0;else break}a=8-(i<<16>>16)|0;if((a|0)>0){B=0;n=0}else return;do{i=0;A=0;w=32767;while(1){e=r[f+(i<<1)>>1]|0;l=e<<16>>16>-1?e<<16>>16<w<<16>>16:0;n=l?A:n;t=i+5|0;A=t&65535;if(A<<16>>16>=40)break;else{i=t<<16>>16;w=l?e:w}}r[f+(n<<16>>16<<1)>>1]=-1;B=B+1<<16>>16}while((B<<16>>16|0)<(a|0));B=0;do{e=1;A=1;t=32767;while(1){i=r[f+(e<<1)>>1]|0;l=i<<16>>16>-1?i<<16>>16<t<<16>>16:0;n=l?A:n;w=e+5|0;A=w&65535;if(A<<16>>16>=40)break;else{e=w<<16>>16;t=l?i:t}}r[f+(n<<16>>16<<1)>>1]=-1;B=B+1<<16>>16}while((B<<16>>16|0)<(a|0));B=0;do{e=2;A=2;t=32767;while(1){i=r[f+(e<<1)>>1]|0;l=i<<16>>16>-1?i<<16>>16<t<<16>>16:0;n=l?A:n;w=e+5|0;A=w&65535;if(A<<16>>16>=40)break;else{e=w<<16>>16;t=l?i:t}}r[f+(n<<16>>16<<1)>>1]=-1;B=B+1<<16>>16}while((B<<16>>16|0)<(a|0));B=0;while(1){e=3;A=3;t=32767;while(1){i=r[f+(e<<1)>>1]|0;l=i<<16>>16>-1?i<<16>>16<t<<16>>16:0;n=l?A:n;w=e+5|0;A=w&65535;if(A<<16>>16>=40){t=n;break}else{e=w<<16>>16;t=l?i:t}}r[f+(t<<16>>16<<1)>>1]=-1;B=B+1<<16>>16;if((B<<16>>16|0)>=(a|0)){n=0;break}else n=t}do{e=4;A=4;B=32767;while(1){i=r[f+(e<<1)>>1]|0;l=i<<16>>16>-1?i<<16>>16<B<<16>>16:0;t=l?A:t;w=e+5|0;A=w&65535;if(A<<16>>16>=40)break;else{e=w<<16>>16;B=l?i:B}}r[f+(t<<16>>16<<1)>>1]=-1;n=n+1<<16>>16}while((n<<16>>16|0)<(a|0));return}function Ai(A,e,f,i,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0;b=s;s=s+80|0;k=b;v=40;u=e;c=A;o=256;Q=256;while(1){a=r[u>>1]|0;u=u+2|0;a=AA(a,a)|0;if((a|0)!=1073741824){g=(a<<1)+o|0;if((a^o|0)>0&(g^o|0)<0){n[B>>2]=1;o=(o>>>31)+2147483647|0}else o=g}else{n[B>>2]=1;o=2147483647}P=r[c>>1]|0;Q=(AA(P<<1,P)|0)+Q|0;v=v+-1<<16>>16;if(!(v<<16>>16))break;else c=c+2|0}P=Ui(o,B)|0;E=P<<5;P=((E>>5|0)==(P|0)?E:P>>31^2147418112)>>16;E=(Ui(Q,B)|0)<<5>>16;h=39;C=e+78|0;D=k+78|0;a=f+78|0;while(1){c=AA(r[C>>1]|0,P)|0;C=C+-2|0;u=c<<1;e=A+(h<<1)|0;o=r[e>>1]|0;v=AA(o<<16>>16,E)|0;if((v|0)!=1073741824){g=(v<<1)+u|0;if((v^u|0)>0&(g^u|0)<0){n[B>>2]=1;g=(c>>>30&1)+2147483647|0}}else{n[B>>2]=1;g=2147483647}Q=g<<10;Q=er((Q>>10|0)==(g|0)?Q:g>>31^2147483647,B)|0;if(Q<<16>>16>-1)r[a>>1]=32767;else{r[a>>1]=-32767;if(Q<<16>>16==-32768)Q=32767;else Q=0-(Q&65535)&65535;if(o<<16>>16==-32768)g=32767;else g=0-(o&65535)&65535;r[e>>1]=g}a=a+-2|0;r[D>>1]=Q;if((h|0)<=0)break;else{h=h+-1|0;D=D+-2|0}}e=t<<16>>16;if(t<<16>>16<=0){r[w+(e<<1)>>1]=r[w>>1]|0;s=b;return}c=l&65535;u=0;v=-1;a=0;while(1){if((u|0)<40){Q=u;g=u&65535;o=-1;while(1){B=r[k+(Q<<1)>>1]|0;l=B<<16>>16>o<<16>>16;o=l?B:o;a=l?g:a;Q=Q+c|0;g=Q&65535;if(g<<16>>16>=40)break;else Q=Q<<16>>16}}else o=-1;r[i+(u<<1)>>1]=a;if(o<<16>>16>v<<16>>16)r[w>>1]=u;else o=v;u=u+1|0;if((u&65535)<<16>>16==t<<16>>16)break;else v=o}a=r[w>>1]|0;r[w+(e<<1)>>1]=a;if(t<<16>>16>1)o=1;else{s=b;return}do{i=a+1<<16>>16;a=i<<16>>16>=t<<16>>16?0:i;r[w+(o<<1)>>1]=a;r[w+(o+e<<1)>>1]=a;o=o+1|0}while((o&65535)<<16>>16!=t<<16>>16);s=b;return}function ei(A){A=A|0;var e=0;if(!A){A=-1;return A|0}n[A>>2]=0;e=lr(12)|0;if(!e){A=-1;return A|0}r[e>>1]=8;n[A>>2]=e;r[e+2>>1]=3;r[e+4>>1]=0;n[e+8>>2]=0;A=0;return A|0}function fi(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function ii(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,t=0,w=0;do{if((e|0)==8){lA()}else{r[A+2>>1]=r[A>>1]|0;n[f>>2]=0;e=A+8|0}}while(0);n[e>>2]=n[f>>2];return}function ri(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0,t=0;if(!A){A=-1;return A|0}n[A>>2]=0;f=lr(12)|0;i=f;if(!f){A=-1;return A|0}n[f>>2]=0;r=f+4|0;n[r>>2]=0;t=f+8|0;n[t>>2]=e;if((Of(f)|0)<<16>>16==0?(me(r,n[t>>2]|0)|0)<<16>>16==0:0){mf(n[f>>2]|0)|0;Ke(n[r>>2]|0)|0;n[A>>2]=i;A=0;return A|0}Nf(f);Ne(r);Br(f);A=-1;return A|0}function ni(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Nf(e);Ne((n[A>>2]|0)+4|0);Br(n[A>>2]|0);n[A>>2]=0;return}function ti(A,e,f,i,t){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;var l=0,B=0,a=0,o=0,Q=0;o=s;s=s+448|0;B=o+320|0;a=o;gr(i|0,0,488)|0;l=0;do{Q=f+(l<<1)|0;r[Q>>1]=(w[Q>>1]|0)&65528;l=l+1|0}while((l|0)!=160);Kf(n[A>>2]|0,f,160);Q=A+4|0;xe(n[Q>>2]|0,e,f,B,t,a)|0;xf(n[t>>2]|0,B,i,(n[Q>>2]|0)+2392|0);s=o;return}function wi(A,e,f,i,n,t,w,l,B,a,o,Q,g,v,u,c){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;w=w|0;l=l|0;B=B|0;a=a|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;var C=0,D=0,E=0;E=s;s=s+48|0;C=E+22|0;D=E;wr(n,(A&-2|0)==6?f:e,C);wr(n,i,D);f=o;e=C;n=f+22|0;do{r[f>>1]=r[e>>1]|0;f=f+2|0;e=e+2|0}while((f|0)<(n|0));tr(t,o,g,40,a,0);tr(D,g,g,40,a,0);Ar(t,w,u,40);f=Q;e=u;n=f+80|0;do{r[f>>1]=r[e>>1]|0;f=f+2|0;e=e+2|0}while((f|0)<(n|0));tr(t,Q,c,40,l,0);Ar(C,c,v,40);tr(D,v,v,40,B,0);s=E;return}function li(A,e,f,i,n,t,l,B,a,s,o,Q,g,v,u,c,C){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;l=l|0;B=B|0;a=a|0;s=s|0;o=o|0;Q=Q|0;g=g|0;v=v|0;u=u|0;c=c|0;C=C|0;var D=0,E=0,h=0,P=0,k=0;if((e|0)==7){h=11;e=i<<16>>16>>>1&65535;D=2}else{h=13;e=i;D=1}r[c>>1]=i<<16>>16<13017?i:13017;E=f<<16>>16;u=u+(E<<1)|0;c=e<<16>>16;n=n<<16>>16;f=20;e=a;C=u;while(1){a=C+2|0;k=AA(r[C>>1]|0,c)|0;P=AA(r[a>>1]|0,c)|0;k=(AA(r[e>>1]|0,n)|0)+k<<1;P=(AA(r[e+2>>1]|0,n)|0)+P<<1<<D;r[C>>1]=((k<<D)+32768|0)>>>16;r[a>>1]=(P+32768|0)>>>16;f=f+-1<<16>>16;if(!(f<<16>>16))break;else{e=e+4|0;C=C+4|0}}e=i<<16>>16;tr(t,u,l+(E<<1)|0,40,Q,1);f=30;C=0;while(1){P=f+E|0;r[g+(C<<1)>>1]=(w[A+(P<<1)>>1]|0)-(w[l+(P<<1)>>1]|0);P=AA(r[s+(f<<1)>>1]|0,e)|0;k=(AA(r[o+(f<<1)>>1]|0,n)|0)>>h;r[v+(C<<1)>>1]=(w[B+(f<<1)>>1]|0)-(P>>>14)-k;C=C+1|0;if((C|0)==10)break;else f=f+1|0}return}function Bi(A){A=A|0;var e=0;if(!A){A=-1;return A|0}n[A>>2]=0;e=lr(16)|0;if(!e){A=-1;return A|0}r[e>>1]=0;r[e+2>>1]=0;r[e+4>>1]=0;r[e+6>>1]=0;r[e+8>>1]=0;r[e+10>>1]=0;r[e+12>>1]=0;r[e+14>>1]=0;n[A>>2]=e;A=0;return A|0}function ai(A){A=A|0;if(!A){A=-1;return A|0}r[A>>1]=0;r[A+2>>1]=0;r[A+4>>1]=0;r[A+6>>1]=0;r[A+8>>1]=0;r[A+10>>1]=0;r[A+12>>1]=0;r[A+14>>1]=0;A=0;return A|0}function si(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function oi(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,n=0,t=0,l=0;i=w[e+6>>1]|0;f=w[e+8>>1]|0;n=i-f|0;n=(n&65535|0)!=32767?n&65535:32767;t=w[e+10>>1]|0;f=f-t|0;n=(f<<16>>16|0)<(n<<16>>16|0)?f&65535:n;f=w[e+12>>1]|0;t=t-f|0;n=(t<<16>>16|0)<(n<<16>>16|0)?t&65535:n;t=w[e+14>>1]|0;f=f-t|0;n=(f<<16>>16|0)<(n<<16>>16|0)?f&65535:n;t=t-(w[e+16>>1]|0)|0;f=r[e+2>>1]|0;l=w[e+4>>1]|0;e=(f&65535)-l|0;e=(e&65535|0)!=32767?e&65535:32767;i=l-i|0;if(((t<<16>>16|0)<(n<<16>>16|0)?t&65535:n)<<16>>16<1500?1:(((i<<16>>16|0)<(e<<16>>16|0)?i&65535:e)<<16>>16|0)<((f<<16>>16>32e3?600:f<<16>>16>30500?800:1100)|0)){t=(r[A>>1]|0)+1<<16>>16;l=t<<16>>16>11;r[A>>1]=l?12:t;return l&1|0}else{r[A>>1]=0;return 0}return 0}function Qi(A,e,f){A=A|0;e=e|0;f=f|0;e=fr(e,3,f)|0;e=Ci(e,r[A+2>>1]|0,f)|0;e=Ci(e,r[A+4>>1]|0,f)|0;e=Ci(e,r[A+6>>1]|0,f)|0;e=Ci(e,r[A+8>>1]|0,f)|0;e=Ci(e,r[A+10>>1]|0,f)|0;e=Ci(e,r[A+12>>1]|0,f)|0;return(Ci(e,r[A+14>>1]|0,f)|0)<<16>>16>15565|0}function gi(A,e,f){A=A|0;e=e|0;f=f|0;var i=0;f=A+4|0;r[A+2>>1]=r[f>>1]|0;i=A+6|0;r[f>>1]=r[i>>1]|0;f=A+8|0;r[i>>1]=r[f>>1]|0;i=A+10|0;r[f>>1]=r[i>>1]|0;f=A+12|0;r[i>>1]=r[f>>1]|0;A=A+14|0;r[f>>1]=r[A>>1]|0;r[A>>1]=e<<16>>16>>>3;return}function vi(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}n[A>>2]=0;e=lr(128)|0;if(!e){i=-1;return i|0}f=e+72|0;i=f+46|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));r[e>>1]=150;r[e+36>>1]=150;r[e+18>>1]=150;r[e+54>>1]=0;r[e+2>>1]=150;r[e+38>>1]=150;r[e+20>>1]=150;r[e+56>>1]=0;r[e+4>>1]=150;r[e+40>>1]=150;r[e+22>>1]=150;r[e+58>>1]=0;r[e+6>>1]=150;r[e+42>>1]=150;r[e+24>>1]=150;r[e+60>>1]=0;r[e+8>>1]=150;r[e+44>>1]=150;r[e+26>>1]=150;r[e+62>>1]=0;r[e+10>>1]=150;r[e+46>>1]=150;r[e+28>>1]=150;r[e+64>>1]=0;r[e+12>>1]=150;r[e+48>>1]=150;r[e+30>>1]=150;r[e+66>>1]=0;r[e+14>>1]=150;r[e+50>>1]=150;r[e+32>>1]=150;r[e+68>>1]=0;r[e+16>>1]=150;r[e+52>>1]=150;r[e+34>>1]=150;r[e+70>>1]=0;r[e+118>>1]=13106;r[e+120>>1]=0;r[e+122>>1]=0;r[e+124>>1]=0;r[e+126>>1]=13106;n[A>>2]=e;i=0;return i|0}function ui(A){A=A|0;var e=0,f=0;if(!A){f=-1;return f|0}e=A+72|0;f=e+46|0;do{r[e>>1]=0;e=e+2|0}while((e|0)<(f|0));r[A>>1]=150;r[A+36>>1]=150;r[A+18>>1]=150;r[A+54>>1]=0;r[A+2>>1]=150;r[A+38>>1]=150;r[A+20>>1]=150;r[A+56>>1]=0;r[A+4>>1]=150;r[A+40>>1]=150;r[A+22>>1]=150;r[A+58>>1]=0;r[A+6>>1]=150;r[A+42>>1]=150;r[A+24>>1]=150;r[A+60>>1]=0;r[A+8>>1]=150;r[A+44>>1]=150;r[A+26>>1]=150;r[A+62>>1]=0;r[A+10>>1]=150;r[A+46>>1]=150;r[A+28>>1]=150;r[A+64>>1]=0;r[A+12>>1]=150;r[A+48>>1]=150;r[A+30>>1]=150;r[A+66>>1]=0;r[A+14>>1]=150;r[A+50>>1]=150;r[A+32>>1]=150;r[A+68>>1]=0;r[A+16>>1]=150;r[A+52>>1]=150;r[A+34>>1]=150;r[A+70>>1]=0;r[A+118>>1]=13106;r[A+120>>1]=0;r[A+122>>1]=0;r[A+124>>1]=0;r[A+126>>1]=13106;f=0;return f|0}function ci(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function Ci(A,e,f){A=A|0;e=e|0;f=f|0;A=(e<<16>>16)+(A<<16>>16)|0;if((A|0)<=32767){if((A|0)<-32768){n[f>>2]=1;A=-32768}}else{n[f>>2]=1;A=32767}return A&65535|0}function Di(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0;b=s;s=s+32|0;P=b+12|0;k=b;r[P>>1]=1024;r[k>>1]=1024;B=r[A+2>>1]|0;w=r[A+20>>1]|0;i=((w+B|0)>>>2)+64512|0;r[P+2>>1]=i;w=((B-w|0)>>>2)+1024|0;r[k+2>>1]=w;B=r[A+4>>1]|0;n=r[A+18>>1]|0;i=((n+B|0)>>>2)-i|0;r[P+4>>1]=i;w=((B-n|0)>>>2)+w|0;r[k+4>>1]=w;n=r[A+6>>1]|0;B=r[A+16>>1]|0;i=((B+n|0)>>>2)-i|0;r[P+6>>1]=i;w=((n-B|0)>>>2)+w|0;r[k+6>>1]=w;B=r[A+8>>1]|0;n=r[A+14>>1]|0;i=((n+B|0)>>>2)-i|0;r[P+8>>1]=i;w=((B-n|0)>>>2)+w|0;r[k+8>>1]=w;n=r[A+10>>1]|0;B=r[A+12>>1]|0;i=((B+n|0)>>>2)-i|0;r[P+10>>1]=i;r[k+10>>1]=((n-B|0)>>>2)+w;w=r[3454]|0;B=w<<16>>16;A=r[P+2>>1]|0;n=(A<<16>>16<<14)+(B<<10)|0;C=n&-65536;n=(n>>>1)-(n>>16<<15)<<16;h=(((AA(n>>16,B)|0)>>15)+(AA(C>>16,B)|0)<<2)+-16777216|0;h=(r[P+4>>1]<<14)+h|0;l=h>>16;h=(h>>>1)-(l<<15)<<16;C=(((AA(h>>16,B)|0)>>15)+(AA(l,B)|0)<<2)-((n>>15)+C)|0;C=(r[P+6>>1]<<14)+C|0;n=C>>16;C=(C>>>1)-(n<<15)<<16;l=(((AA(C>>16,B)|0)>>15)+(AA(n,B)|0)<<2)-((h>>15)+(l<<16))|0;l=(r[P+8>>1]<<14)+l|0;h=l>>16;n=(i<<16>>3)+((((AA((l>>>1)-(h<<15)<<16>>16,B)|0)>>15)+(AA(h,B)|0)<<1)-((C>>15)+(n<<16)))|0;C=P+4|0;B=P;h=0;l=0;i=0;c=P+10|0;n=(n+33554432|0)>>>0<67108863?n>>>10&65535:(n|0)>33554431?32767:-32768;A:while(1){D=A<<16>>16<<14;u=B+6|0;v=B+8|0;g=l<<16>>16;while(1){if((g|0)>=60)break A;B=(g&65535)+1<<16>>16;a=r[6908+(B<<16>>16<<1)>>1]|0;E=a<<16>>16;l=D+(E<<10)|0;t=l&-65536;l=(l>>>1)-(l>>16<<15)<<16;o=(((AA(l>>16,E)|0)>>15)+(AA(t>>16,E)|0)<<2)+-16777216|0;Q=r[C>>1]|0;o=(Q<<16>>16<<14)+o|0;M=o>>16;o=(o>>>1)-(M<<15)<<16;t=(((AA(o>>16,E)|0)>>15)+(AA(M,E)|0)<<2)-((l>>15)+t)|0;l=r[u>>1]|0;t=(l<<16>>16<<14)+t|0;A=t>>16;t=(t>>>1)-(A<<15)<<16;M=(((AA(t>>16,E)|0)>>15)+(AA(A,E)|0)<<2)-((o>>15)+(M<<16))|0;o=r[v>>1]|0;M=(o<<16>>16<<14)+M|0;I=M>>16;A=(((AA((M>>>1)-(I<<15)<<16>>16,E)|0)>>15)+(AA(I,E)|0)<<1)-((t>>15)+(A<<16))|0;t=r[c>>1]|0;A=(t<<16>>16<<13)+A|0;A=(A+33554432|0)>>>0<67108863?A>>>10&65535:(A|0)>33554431?32767:-32768;if((AA(A<<16>>16,n<<16>>16)|0)<1){E=B;B=Q;break}else{g=g+1|0;w=a;n=A}}C=t<<16>>16<<13;c=B<<16>>16<<14;Q=l<<16>>16<<14;v=o<<16>>16<<14;t=a<<16>>16;g=4;while(1){I=(w<<16>>16>>>1)+(t>>>1)|0;t=I<<16;u=t>>16;t=D+(t>>6)|0;M=t&-65536;t=(t>>>1)-(t>>16<<15)<<16;o=c+((((AA(t>>16,u)|0)>>15)+(AA(M>>16,u)|0)<<2)+-16777216)|0;B=o>>16;o=(o>>>1)-(B<<15)<<16;M=Q+((((AA(o>>16,u)|0)>>15)+(AA(B,u)|0)<<2)-((t>>15)+M))|0;t=M>>16;M=(M>>>1)-(t<<15)<<16;B=v+((((AA(M>>16,u)|0)>>15)+(AA(t,u)|0)<<2)-((o>>15)+(B<<16)))|0;o=B>>16;I=I&65535;t=C+((((AA((B>>>1)-(o<<15)<<16>>16,u)|0)>>15)+(AA(o,u)|0)<<1)-((M>>15)+(t<<16)))|0;t=(t+33554432|0)>>>0<67108863?t>>>10&65535:(t|0)>33554431?32767:-32768;M=(AA(t<<16>>16,A<<16>>16)|0)<1;u=M?a:I;A=M?A:t;w=M?I:w;n=M?t:n;g=g+-1<<16>>16;t=u<<16>>16;if(!(g<<16>>16)){a=t;l=w;w=u;break}else a=u}B=i<<16>>16;t=A<<16>>16;A=(n&65535)-t|0;n=A<<16;if(n){M=(A&65535)-(A>>>15&1)|0;M=M<<16>>31^M;A=(Ki(M&65535)|0)<<16>>16;A=(AA((Ei(16383,M<<16>>16<<A&65535)|0)<<16>>16,(l&65535)-a<<16>>16)|0)>>19-A;if((n|0)<0)A=0-(A<<16>>16)|0;w=a-((AA(A<<16>>16,t)|0)>>>10)&65535}r[e+(B<<1)>>1]=w;n=h<<16>>16==0?k:P;I=w<<16>>16;A=r[n+2>>1]|0;t=(A<<16>>16<<14)+(I<<10)|0;M=t&-65536;t=(t>>>1)-(t>>16<<15)<<16;D=(((AA(t>>16,I)|0)>>15)+(AA(M>>16,I)|0)<<2)+-16777216|0;D=(r[n+4>>1]<<14)+D|0;C=D>>16;D=(D>>>1)-(C<<15)<<16;M=(((AA(D>>16,I)|0)>>15)+(AA(C,I)|0)<<2)-((t>>15)+M)|0;M=(r[n+6>>1]<<14)+M|0;t=M>>16;M=(M>>>1)-(t<<15)<<16;C=(((AA(M>>16,I)|0)>>15)+(AA(t,I)|0)<<2)-((D>>15)+(C<<16))|0;C=(r[n+8>>1]<<14)+C|0;D=C>>16;i=i+1<<16>>16;t=(((AA((C>>>1)-(D<<15)<<16>>16,I)|0)>>15)+(AA(D,I)|0)<<1)-((M>>15)+(t<<16))|0;t=(r[n+10>>1]<<13)+t|0;if(i<<16>>16<10){C=n+4|0;B=n;h=h^1;l=E;c=n+10|0;n=(t+33554432|0)>>>0<67108863?t>>>10&65535:(t|0)>33554431?32767:-32768}else{d=13;break}}if((d|0)==13){s=b;return}r[e>>1]=r[f>>1]|0;r[e+2>>1]=r[f+2>>1]|0;r[e+4>>1]=r[f+4>>1]|0;r[e+6>>1]=r[f+6>>1]|0;r[e+8>>1]=r[f+8>>1]|0;r[e+10>>1]=r[f+10>>1]|0;r[e+12>>1]=r[f+12>>1]|0;r[e+14>>1]=r[f+14>>1]|0;r[e+16>>1]=r[f+16>>1]|0;r[e+18>>1]=r[f+18>>1]|0;s=b;return}function Ei(A,e){A=A|0;e=e|0;var f=0,i=0,r=0,n=0,t=0,w=0;r=e<<16>>16;if(A<<16>>16<1?1:A<<16>>16>e<<16>>16){r=0;return r|0}if(A<<16>>16==e<<16>>16){r=32767;return r|0}i=r<<1;f=r<<2;n=A<<16>>16<<3;A=(n|0)<(f|0);n=n-(A?0:f)|0;A=A?0:4;t=(n|0)<(i|0);n=n-(t?0:i)|0;e=(n|0)<(r|0);A=(e&1|(t?A:A|2))<<3^8;e=n-(e?0:r)<<3;if((e|0)>=(f|0)){e=e-f|0;A=A&65528|4}n=(e|0)<(i|0);t=e-(n?0:i)|0;e=(t|0)<(r|0);A=(e&1^1|(n?A:A|2))<<16>>13;e=t-(e?0:r)<<3;if((e|0)>=(f|0)){e=e-f|0;A=A&65528|4}n=(e|0)<(i|0);t=e-(n?0:i)|0;e=(t|0)<(r|0);A=(e&1^1|(n?A:A|2))<<16>>13;e=t-(e?0:r)<<3;if((e|0)>=(f|0)){e=e-f|0;A=A&65528|4}w=(e|0)<(i|0);n=e-(w?0:i)|0;t=(n|0)<(r|0);e=(t&1^1|(w?A:A|2))<<16>>13;A=n-(t?0:r)<<3;if((A|0)>=(f|0)){A=A-f|0;e=e&65528|4}w=(A|0)<(i|0);w=((A-(w?0:i)|0)>=(r|0)|(w?e:e|2))&65535;return w|0}function hi(A){A=A|0;if(!A){A=-1;return A|0}r[A>>1]=-14336;r[A+8>>1]=-2381;r[A+2>>1]=-14336;r[A+10>>1]=-2381;r[A+4>>1]=-14336;r[A+12>>1]=-2381;r[A+6>>1]=-14336;r[A+14>>1]=-2381;A=0;return A|0}function Pi(A,e,f,i,t,l,B,a){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;l=l|0;B=B|0;a=a|0;var o=0,Q=0,g=0,v=0,u=0,c=0;c=s;s=s+16|0;v=c+2|0;u=c;o=0;Q=10;while(1){g=r[f>>1]|0;g=((AA(g,g)|0)>>>3)+o|0;o=r[f+2>>1]|0;o=g+((AA(o,o)|0)>>>3)|0;g=r[f+4>>1]|0;g=o+((AA(g,g)|0)>>>3)|0;o=r[f+6>>1]|0;o=g+((AA(o,o)|0)>>>3)|0;Q=Q+-1<<16>>16;if(!(Q<<16>>16))break;else f=f+8|0}Q=o<<4;Q=(Q|0)<0?2147483647:Q;if((e|0)==7){Gi(((er(Q,a)|0)<<16>>16)*52428|0,v,u,a);g=w[v>>1]<<16;Q=r[u>>1]<<1;e=r[A+8>>1]|0;o=(e<<16>>16)*88|0;if(e<<16>>16>-1&(o|0)<-783741){n[a>>2]=1;f=2147483647}else f=o+783741|0;e=(r[A+10>>1]|0)*74|0;o=e+f|0;if((e^f|0)>-1&(o^f|0)<0){n[a>>2]=1;f=(f>>>31)+2147483647|0}else f=o;e=(r[A+12>>1]|0)*44|0;o=e+f|0;if((e^f|0)>-1&(o^f|0)<0){n[a>>2]=1;f=(f>>>31)+2147483647|0}else f=o;A=(r[A+14>>1]|0)*24|0;o=A+f|0;if((A^f|0)>-1&(o^f|0)<0){n[a>>2]=1;o=(f>>>31)+2147483647|0}A=g+-1966080+Q|0;f=o-A|0;if(((f^o)&(o^A)|0)<0){n[a>>2]=1;f=(o>>>31)+2147483647|0}a=f>>17;r[i>>1]=a;a=(f>>2)-(a<<15)|0;a=a&65535;r[t>>1]=a;s=c;return}g=Ni(Q)|0;o=g<<16>>16;if(g<<16>>16>0){f=Q<<o;if((f>>o|0)==(Q|0))Q=f;else Q=Q>>31^2147483647}else{o=0-o<<16;if((o|0)<2031616)Q=Q>>(o>>16);else Q=0}Li(Q,g,v,u);v=AA(r[v>>1]|0,-49320)|0;o=(AA(r[u>>1]|0,-24660)|0)>>15;o=(o&65536|0)==0?o:o|-65536;u=o<<1;f=u+v|0;if((u^v|0)>-1&(f^u|0)<0){n[a>>2]=1;f=(o>>>30&1)+2147483647|0}switch(e|0){case 6:{o=f+2134784|0;if((f|0)>-1&(o^f|0)<0){n[a>>2]=1;o=(f>>>31)+2147483647|0}break}case 5:{r[B>>1]=Q>>>16;r[l>>1]=-11-(g&65535);o=f+2183936|0;if((f|0)>-1&(o^f|0)<0){n[a>>2]=1;o=(f>>>31)+2147483647|0}break}case 4:{o=f+2085632|0;if((f|0)>-1&(o^f|0)<0){n[a>>2]=1;o=(f>>>31)+2147483647|0}break}case 3:{o=f+2065152|0;if((f|0)>-1&(o^f|0)<0){n[a>>2]=1;o=(f>>>31)+2147483647|0}break}default:{o=f+2134784|0;if((f|0)>-1&(o^f|0)<0){n[a>>2]=1;o=(f>>>31)+2147483647|0}}}do{if((o|0)<=2097151)if((o|0)<-2097152){n[a>>2]=1;f=-2147483648;break}else{f=o<<10;break}else{n[a>>2]=1;f=2147483647}}while(0);B=(r[A>>1]|0)*11142|0;o=B+f|0;if((B^f|0)>-1&(o^f|0)<0){n[a>>2]=1;o=(f>>>31)+2147483647|0}B=(r[A+2>>1]|0)*9502|0;f=B+o|0;if((B^o|0)>-1&(f^o|0)<0){n[a>>2]=1;f=(o>>>31)+2147483647|0}B=(r[A+4>>1]|0)*5570|0;o=B+f|0;if((B^f|0)>-1&(o^f|0)<0){n[a>>2]=1;o=(f>>>31)+2147483647|0}A=(r[A+6>>1]|0)*3112|0;f=A+o|0;if((A^o|0)>-1&(f^o|0)<0){n[a>>2]=1;f=(o>>>31)+2147483647|0}f=AA(f>>16,(e|0)==4?10878:10886)|0;if((f|0)<0)f=~((f^-256)>>8);else f=f>>8;r[i>>1]=f>>>16;if((f|0)<0)o=~((f^-2)>>1);else o=f>>1;i=f>>16<<15;f=o-i|0;if(((f^o)&(i^o)|0)>=0){a=f;a=a&65535;r[t>>1]=a;s=c;return}n[a>>2]=1;a=(o>>>31)+2147483647|0;a=a&65535;r[t>>1]=a;s=c;return}function ki(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,n=0,t=0;n=A+4|0;r[A+6>>1]=r[n>>1]|0;t=A+12|0;r[A+14>>1]=r[t>>1]|0;i=A+2|0;r[n>>1]=r[i>>1]|0;n=A+10|0;r[t>>1]=r[n>>1]|0;r[i>>1]=r[A>>1]|0;i=A+8|0;r[n>>1]=r[i>>1]|0;r[i>>1]=e;r[A>>1]=f;return}function bi(A){A=A|0;n[A>>2]=6892;n[A+4>>2]=8180;n[A+8>>2]=21e3;n[A+12>>2]=9716;n[A+16>>2]=22024;n[A+20>>2]=12788;n[A+24>>2]=24072;n[A+28>>2]=26120;n[A+32>>2]=28168;n[A+36>>2]=6876;n[A+40>>2]=7452;n[A+44>>2]=8140;n[A+48>>2]=20980;n[A+52>>2]=16884;n[A+56>>2]=17908;n[A+60>>2]=7980;n[A+64>>2]=8160;n[A+68>>2]=6678;n[A+72>>2]=6646;n[A+76>>2]=6614;n[A+80>>2]=29704;n[A+84>>2]=28680;n[A+88>>2]=3720;n[A+92>>2]=8;n[A+96>>2]=4172;n[A+100>>2]=44;n[A+104>>2]=3436;n[A+108>>2]=30316;n[A+112>>2]=30796;n[A+116>>2]=31276;n[A+120>>2]=7472;n[A+124>>2]=7552;n[A+128>>2]=7632;n[A+132>>2]=7712;return}function di(A,e){A=A|0;e=e|0;var f=0,i=0,n=0,t=0,w=0,l=0,B=0,a=0,o=0,Q=0;Q=s;s=s+48|0;a=Q+18|0;o=Q;B=e<<16>>16;or(o|0,A|0,B<<1|0)|0;if(e<<16>>16>0){f=0;i=0}else{lA()}do{l=0;w=-32767;while(1){n=r[o+(l<<1)>>1]|0;t=n<<16>>16<w<<16>>16;i=t?i:l&65535;l=l+1|0;if((l&65535)<<16>>16==e<<16>>16)break;else w=t?w:n}r[o+(i<<16>>16<<1)>>1]=-32768;r[a+(f<<1)>>1]=i;f=f+1|0}while((f&65535)<<16>>16!=e<<16>>16);o=B>>1;o=a+(o<<1)|0;o=r[o>>1]|0;o=o<<16>>16;o=A+(o<<1)|0;o=r[o>>1]|0;s=Q;return o|0}function Ii(A,e,f,i,n){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0;t=s;s=s+32|0;w=t;I=e+2|0;d=w+2|0;r[w>>1]=((r[e>>1]|0)>>>1)+((r[A>>1]|0)>>>1);b=e+4|0;k=w+4|0;r[d>>1]=((r[I>>1]|0)>>>1)+((r[A+2>>1]|0)>>>1);P=e+6|0;h=w+6|0;r[k>>1]=((r[b>>1]|0)>>>1)+((r[A+4>>1]|0)>>>1);E=e+8|0;D=w+8|0;r[h>>1]=((r[P>>1]|0)>>>1)+((r[A+6>>1]|0)>>>1);C=e+10|0;c=w+10|0;r[D>>1]=((r[E>>1]|0)>>>1)+((r[A+8>>1]|0)>>>1);u=e+12|0;v=w+12|0;r[c>>1]=((r[C>>1]|0)>>>1)+((r[A+10>>1]|0)>>>1);g=e+14|0;Q=w+14|0;r[v>>1]=((r[u>>1]|0)>>>1)+((r[A+12>>1]|0)>>>1);o=e+16|0;a=w+16|0;r[Q>>1]=((r[g>>1]|0)>>>1)+((r[A+14>>1]|0)>>>1);B=e+18|0;l=w+18|0;r[a>>1]=((r[o>>1]|0)>>>1)+((r[A+16>>1]|0)>>>1);r[l>>1]=((r[B>>1]|0)>>>1)+((r[A+18>>1]|0)>>>1);yi(w,i,n);yi(e,i+22|0,n);r[w>>1]=((r[f>>1]|0)>>>1)+((r[e>>1]|0)>>>1);r[d>>1]=((r[f+2>>1]|0)>>>1)+((r[I>>1]|0)>>>1);r[k>>1]=((r[f+4>>1]|0)>>>1)+((r[b>>1]|0)>>>1);r[h>>1]=((r[f+6>>1]|0)>>>1)+((r[P>>1]|0)>>>1);r[D>>1]=((r[f+8>>1]|0)>>>1)+((r[E>>1]|0)>>>1);r[c>>1]=((r[f+10>>1]|0)>>>1)+((r[C>>1]|0)>>>1);r[v>>1]=((r[f+12>>1]|0)>>>1)+((r[u>>1]|0)>>>1);r[Q>>1]=((r[f+14>>1]|0)>>>1)+((r[g>>1]|0)>>>1);r[a>>1]=((r[f+16>>1]|0)>>>1)+((r[o>>1]|0)>>>1);r[l>>1]=((r[f+18>>1]|0)>>>1)+((r[B>>1]|0)>>>1);yi(w,i+44|0,n);yi(f,i+66|0,n);s=t;return}function Mi(A,e,f,i,n){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;var t=0,w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0;t=s;s=s+32|0;w=t;I=e+2|0;d=w+2|0;r[w>>1]=((r[e>>1]|0)>>>1)+((r[A>>1]|0)>>>1);b=e+4|0;k=w+4|0;r[d>>1]=((r[I>>1]|0)>>>1)+((r[A+2>>1]|0)>>>1);P=e+6|0;h=w+6|0;r[k>>1]=((r[b>>1]|0)>>>1)+((r[A+4>>1]|0)>>>1);E=e+8|0;D=w+8|0;r[h>>1]=((r[P>>1]|0)>>>1)+((r[A+6>>1]|0)>>>1);C=e+10|0;c=w+10|0;r[D>>1]=((r[E>>1]|0)>>>1)+((r[A+8>>1]|0)>>>1);u=e+12|0;v=w+12|0;r[c>>1]=((r[C>>1]|0)>>>1)+((r[A+10>>1]|0)>>>1);g=e+14|0;Q=w+14|0;r[v>>1]=((r[u>>1]|0)>>>1)+((r[A+12>>1]|0)>>>1);o=e+16|0;a=w+16|0;r[Q>>1]=((r[g>>1]|0)>>>1)+((r[A+14>>1]|0)>>>1);B=e+18|0;l=w+18|0;r[a>>1]=((r[o>>1]|0)>>>1)+((r[A+16>>1]|0)>>>1);r[l>>1]=((r[B>>1]|0)>>>1)+((r[A+18>>1]|0)>>>1);yi(w,i,n);r[w>>1]=((r[f>>1]|0)>>>1)+((r[e>>1]|0)>>>1);r[d>>1]=((r[f+2>>1]|0)>>>1)+((r[I>>1]|0)>>>1);r[k>>1]=((r[f+4>>1]|0)>>>1)+((r[b>>1]|0)>>>1);r[h>>1]=((r[f+6>>1]|0)>>>1)+((r[P>>1]|0)>>>1);r[D>>1]=((r[f+8>>1]|0)>>>1)+((r[E>>1]|0)>>>1);r[c>>1]=((r[f+10>>1]|0)>>>1)+((r[C>>1]|0)>>>1);r[v>>1]=((r[f+12>>1]|0)>>>1)+((r[u>>1]|0)>>>1);r[Q>>1]=((r[f+14>>1]|0)>>>1)+((r[g>>1]|0)>>>1);r[a>>1]=((r[f+16>>1]|0)>>>1)+((r[o>>1]|0)>>>1);r[l>>1]=((r[f+18>>1]|0)>>>1)+((r[B>>1]|0)>>>1);yi(w,i+44|0,n);s=t;return}function Fi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0;n=s;s=s+32|0;t=n;R=r[A>>1]|0;r[t>>1]=R-(R>>>2)+((r[e>>1]|0)>>>2);R=A+2|0;U=r[R>>1]|0;T=e+2|0;L=t+2|0;r[L>>1]=U-(U>>>2)+((r[T>>1]|0)>>>2);U=A+4|0;M=r[U>>1]|0;G=e+4|0;H=t+4|0;r[H>>1]=M-(M>>>2)+((r[G>>1]|0)>>>2);M=A+6|0;b=r[M>>1]|0;F=e+6|0;I=t+6|0;r[I>>1]=b-(b>>>2)+((r[F>>1]|0)>>>2);b=A+8|0;h=r[b>>1]|0;d=e+8|0;k=t+8|0;r[k>>1]=h-(h>>>2)+((r[d>>1]|0)>>>2);h=A+10|0;C=r[h>>1]|0;P=e+10|0;E=t+10|0;r[E>>1]=C-(C>>>2)+((r[P>>1]|0)>>>2);C=A+12|0;v=r[C>>1]|0;D=e+12|0;c=t+12|0;r[c>>1]=v-(v>>>2)+((r[D>>1]|0)>>>2);v=A+14|0;o=r[v>>1]|0;u=e+14|0;g=t+14|0;r[g>>1]=o-(o>>>2)+((r[u>>1]|0)>>>2);o=A+16|0;l=r[o>>1]|0;Q=e+16|0;a=t+16|0;r[a>>1]=l-(l>>>2)+((r[Q>>1]|0)>>>2);l=A+18|0;y=r[l>>1]|0;B=e+18|0;w=t+18|0;r[w>>1]=y-(y>>>2)+((r[B>>1]|0)>>>2);yi(t,f,i);r[t>>1]=((r[A>>1]|0)>>>1)+((r[e>>1]|0)>>>1);r[L>>1]=((r[R>>1]|0)>>>1)+((r[T>>1]|0)>>>1);r[H>>1]=((r[U>>1]|0)>>>1)+((r[G>>1]|0)>>>1);r[I>>1]=((r[M>>1]|0)>>>1)+((r[F>>1]|0)>>>1);r[k>>1]=((r[b>>1]|0)>>>1)+((r[d>>1]|0)>>>1);r[E>>1]=((r[h>>1]|0)>>>1)+((r[P>>1]|0)>>>1);r[c>>1]=((r[C>>1]|0)>>>1)+((r[D>>1]|0)>>>1);r[g>>1]=((r[v>>1]|0)>>>1)+((r[u>>1]|0)>>>1);r[a>>1]=((r[o>>1]|0)>>>1)+((r[Q>>1]|0)>>>1);r[w>>1]=((r[l>>1]|0)>>>1)+((r[B>>1]|0)>>>1);yi(t,f+22|0,i);y=r[e>>1]|0;r[t>>1]=y-(y>>>2)+((r[A>>1]|0)>>>2);A=r[T>>1]|0;r[L>>1]=A-(A>>>2)+((r[R>>1]|0)>>>2);A=r[G>>1]|0;r[H>>1]=A-(A>>>2)+((r[U>>1]|0)>>>2);A=r[F>>1]|0;r[I>>1]=A-(A>>>2)+((r[M>>1]|0)>>>2);A=r[d>>1]|0;r[k>>1]=A-(A>>>2)+((r[b>>1]|0)>>>2);A=r[P>>1]|0;r[E>>1]=A-(A>>>2)+((r[h>>1]|0)>>>2);A=r[D>>1]|0;r[c>>1]=A-(A>>>2)+((r[C>>1]|0)>>>2);A=r[u>>1]|0;r[g>>1]=A-(A>>>2)+((r[v>>1]|0)>>>2);A=r[Q>>1]|0;r[a>>1]=A-(A>>>2)+((r[o>>1]|0)>>>2);A=r[B>>1]|0;r[w>>1]=A-(A>>>2)+((r[l>>1]|0)>>>2);yi(t,f+44|0,i);yi(e,f+66|0,i);s=n;return}function Hi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0;n=s;s=s+32|0;t=n;R=r[A>>1]|0;r[t>>1]=R-(R>>>2)+((r[e>>1]|0)>>>2);R=A+2|0;U=r[R>>1]|0;T=e+2|0;L=t+2|0;r[L>>1]=U-(U>>>2)+((r[T>>1]|0)>>>2);U=A+4|0;M=r[U>>1]|0;G=e+4|0;H=t+4|0;r[H>>1]=M-(M>>>2)+((r[G>>1]|0)>>>2);M=A+6|0;b=r[M>>1]|0;F=e+6|0;I=t+6|0;r[I>>1]=b-(b>>>2)+((r[F>>1]|0)>>>2);b=A+8|0;h=r[b>>1]|0;d=e+8|0;k=t+8|0;r[k>>1]=h-(h>>>2)+((r[d>>1]|0)>>>2);h=A+10|0;C=r[h>>1]|0;P=e+10|0;E=t+10|0;r[E>>1]=C-(C>>>2)+((r[P>>1]|0)>>>2);C=A+12|0;v=r[C>>1]|0;D=e+12|0;c=t+12|0;r[c>>1]=v-(v>>>2)+((r[D>>1]|0)>>>2);v=A+14|0;o=r[v>>1]|0;u=e+14|0;g=t+14|0;r[g>>1]=o-(o>>>2)+((r[u>>1]|0)>>>2);o=A+16|0;l=r[o>>1]|0;Q=e+16|0;a=t+16|0;r[a>>1]=l-(l>>>2)+((r[Q>>1]|0)>>>2);l=A+18|0;y=r[l>>1]|0;B=e+18|0;w=t+18|0;r[w>>1]=y-(y>>>2)+((r[B>>1]|0)>>>2);yi(t,f,i);r[t>>1]=((r[A>>1]|0)>>>1)+((r[e>>1]|0)>>>1);r[L>>1]=((r[R>>1]|0)>>>1)+((r[T>>1]|0)>>>1);r[H>>1]=((r[U>>1]|0)>>>1)+((r[G>>1]|0)>>>1);r[I>>1]=((r[M>>1]|0)>>>1)+((r[F>>1]|0)>>>1);r[k>>1]=((r[b>>1]|0)>>>1)+((r[d>>1]|0)>>>1);r[E>>1]=((r[h>>1]|0)>>>1)+((r[P>>1]|0)>>>1);r[c>>1]=((r[C>>1]|0)>>>1)+((r[D>>1]|0)>>>1);r[g>>1]=((r[v>>1]|0)>>>1)+((r[u>>1]|0)>>>1);r[a>>1]=((r[o>>1]|0)>>>1)+((r[Q>>1]|0)>>>1);r[w>>1]=((r[l>>1]|0)>>>1)+((r[B>>1]|0)>>>1);yi(t,f+22|0,i);e=r[e>>1]|0;r[t>>1]=e-(e>>>2)+((r[A>>1]|0)>>>2);A=r[T>>1]|0;r[L>>1]=A-(A>>>2)+((r[R>>1]|0)>>>2);A=r[G>>1]|0;r[H>>1]=A-(A>>>2)+((r[U>>1]|0)>>>2);A=r[F>>1]|0;r[I>>1]=A-(A>>>2)+((r[M>>1]|0)>>>2);A=r[d>>1]|0;r[k>>1]=A-(A>>>2)+((r[b>>1]|0)>>>2);A=r[P>>1]|0;r[E>>1]=A-(A>>>2)+((r[h>>1]|0)>>>2);A=r[D>>1]|0;r[c>>1]=A-(A>>>2)+((r[C>>1]|0)>>>2);A=r[u>>1]|0;r[g>>1]=A-(A>>>2)+((r[v>>1]|0)>>>2);A=r[Q>>1]|0;r[a>>1]=A-(A>>>2)+((r[o>>1]|0)>>>2);A=r[B>>1]|0;r[w>>1]=A-(A>>>2)+((r[l>>1]|0)>>>2);yi(t,f+44|0,i);s=n;return}function Ui(A,e){A=A|0;e=e|0;var f=0,i=0;if((A|0)<1){e=1073741823;return e|0}f=(Ni(A)|0)<<16>>16;e=30-f|0;A=A<<f>>(e&1^1);f=(A>>25<<16)+-1048576>>16;i=r[7030+(f<<1)>>1]|0;e=(i<<16)-(AA(i-(w[7030+(f+1<<1)>>1]|0)<<16>>15,A>>>10&32767)|0)>>(e<<16>>17)+1;return e|0}function Gi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;i=Ni(A)|0;Li(A<<(i<<16>>16),i,e,f);return}function Li(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;if((A|0)<1){r[f>>1]=0;f=0;r[i>>1]=f;return}else{r[f>>1]=30-(e&65535);f=(A>>25<<16)+-2097152>>16;e=r[7128+(f<<1)>>1]|0;f=((e<<16)-(AA(A>>>9&65534,e-(w[7128+(f+1<<1)>>1]|0)<<16>>16)|0)|0)>>>16&65535;r[i>>1]=f;return}}function Ri(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,n=0;i=A+2|0;f=r[i>>1]|0;r[e>>1]=f;n=A+4|0;r[e+2>>1]=(w[n>>1]|0)-(w[A>>1]|0);r[e+4>>1]=(w[A+6>>1]|0)-(w[i>>1]|0);i=A+8|0;r[e+6>>1]=(w[i>>1]|0)-(w[n>>1]|0);r[e+8>>1]=(w[A+10>>1]|0)-(w[A+6>>1]|0);n=A+12|0;r[e+10>>1]=(w[n>>1]|0)-(w[i>>1]|0);r[e+12>>1]=(w[A+14>>1]|0)-(w[A+10>>1]|0);r[e+14>>1]=(w[A+16>>1]|0)-(w[n>>1]|0);r[e+16>>1]=(w[A+18>>1]|0)-(w[A+14>>1]|0);r[e+18>>1]=16384-(w[A+16>>1]|0);A=10;n=e;while(1){f=f<<16>>16;e=(f<<16)+-120782848|0;if((e|0)>0)e=1843-((e>>16)*12484>>16)|0;else e=3427-((f*56320|0)>>>16)|0;i=n+2|0;r[n>>1]=e<<3;A=A+-1<<16>>16;if(!(A<<16>>16))break;f=r[i>>1]|0;n=i}return}function Ti(A,e,f){A=A|0;e=e|0;f=f|0;f=e<<16>>16;if(e<<16>>16>31){e=0;return e|0}if(e<<16>>16>0)return((1<<f+-1&A|0)!=0&1)+(e<<16>>16<31?A>>f:0)|0;f=0-f<<16>>16;e=A<<f;e=(e>>f|0)==(A|0)?e:A>>31^2147483647;return e|0}function yi(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,t=0,w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0;C=s;s=s+48|0;u=C+24|0;c=C;g=u+4|0;n[u>>2]=16777216;i=0-(r[A>>1]|0)|0;v=u+8|0;n[g>>2]=i<<10;t=r[A+4>>1]|0;a=i>>6;n[v>>2]=33554432-(((AA((i<<9)-(a<<15)<<16>>16,t)|0)>>15)+(AA(a,t)|0)<<2);a=u+4|0;t=(n[a>>2]|0)-(t<<10)|0;n[a>>2]=t;a=u+12|0;i=u+4|0;n[a>>2]=t;f=r[A+8>>1]|0;w=t;o=1;while(1){B=a+-4|0;l=n[B>>2]|0;Q=l>>16;n[a>>2]=w+t-(((AA((l>>>1)-(Q<<15)<<16>>16,f)|0)>>15)+(AA(Q,f)|0)<<2);if((o|0)==2)break;w=n[a+-12>>2]|0;a=B;t=l;o=o+1|0}n[i>>2]=(n[i>>2]|0)-(f<<10);f=u+16|0;i=n[u+8>>2]|0;n[f>>2]=i;B=r[A+12>>1]|0;t=i;a=1;while(1){l=f+-4|0;w=n[l>>2]|0;Q=w>>16;n[f>>2]=t+i-(((AA((w>>>1)-(Q<<15)<<16>>16,B)|0)>>15)+(AA(Q,B)|0)<<2);if((a|0)==3)break;t=n[f+-12>>2]|0;f=l;i=w;a=a+1|0}f=u+4|0;n[f>>2]=(n[f>>2]|0)-(B<<10);f=u+20|0;t=n[u+12>>2]|0;n[f>>2]=t;i=r[A+16>>1]|0;w=t;a=1;while(1){B=f+-4|0;l=n[B>>2]|0;Q=l>>16;n[f>>2]=w+t-(((AA((l>>>1)-(Q<<15)<<16>>16,i)|0)>>15)+(AA(Q,i)|0)<<2);if((a|0)==4)break;w=n[f+-12>>2]|0;f=B;t=l;a=a+1|0}a=u+4|0;n[a>>2]=(n[a>>2]|0)-(i<<10);n[c>>2]=16777216;a=0-(r[A+2>>1]|0)|0;Q=c+8|0;n[c+4>>2]=a<<10;i=r[A+6>>1]|0;o=a>>6;n[Q>>2]=33554432-(((AA((a<<9)-(o<<15)<<16>>16,i)|0)>>15)+(AA(o,i)|0)<<2);o=c+4|0;i=(n[o>>2]|0)-(i<<10)|0;n[o>>2]=i;o=c+12|0;a=c+4|0;n[o>>2]=i;B=r[A+10>>1]|0;t=i;f=1;while(1){l=o+-4|0;w=n[l>>2]|0;D=w>>16;n[o>>2]=t+i-(((AA((w>>>1)-(D<<15)<<16>>16,B)|0)>>15)+(AA(D,B)|0)<<2);if((f|0)==2)break;t=n[o+-12>>2]|0;o=l;i=w;f=f+1|0}n[a>>2]=(n[a>>2]|0)-(B<<10);a=c+16|0;i=n[c+8>>2]|0;n[a>>2]=i;B=r[A+14>>1]|0;t=i;f=1;while(1){l=a+-4|0;w=n[l>>2]|0;D=w>>16;n[a>>2]=t+i-(((AA((w>>>1)-(D<<15)<<16>>16,B)|0)>>15)+(AA(D,B)|0)<<2);if((f|0)==3)break;t=n[a+-12>>2]|0;a=l;i=w;f=f+1|0}f=c+4|0;n[f>>2]=(n[f>>2]|0)-(B<<10);f=c+20|0;B=n[c+12>>2]|0;n[f>>2]=B;i=r[A+18>>1]|0;l=B;a=1;while(1){t=f+-4|0;w=n[t>>2]|0;D=w>>16;n[f>>2]=l+B-(((AA((w>>>1)-(D<<15)<<16>>16,i)|0)>>15)+(AA(D,i)|0)<<2);if((a|0)==4)break;l=n[f+-12>>2]|0;f=t;B=w;a=a+1|0}l=(n[c+4>>2]|0)-(i<<10)|0;o=u+20|0;B=c+20|0;a=n[u+16>>2]|0;A=(n[o>>2]|0)+a|0;n[o>>2]=A;o=n[c+16>>2]|0;D=(n[B>>2]|0)-o|0;n[B>>2]=D;B=n[u+12>>2]|0;a=a+B|0;n[u+16>>2]=a;w=n[c+12>>2]|0;o=o-w|0;n[c+16>>2]=o;i=n[v>>2]|0;B=B+i|0;n[u+12>>2]=B;t=n[Q>>2]|0;v=w-t|0;n[c+12>>2]=v;w=n[g>>2]|0;Q=i+w|0;n[u+8>>2]=Q;g=t-l|0;n[c+8>>2]=g;u=w+(n[u>>2]|0)|0;c=l-(n[c>>2]|0)|0;r[e>>1]=4096;u=u+4096|0;r[e+2>>1]=(u+c|0)>>>13;r[e+20>>1]=(u-c|0)>>>13;c=Q+4096|0;r[e+4>>1]=(c+g|0)>>>13;r[e+18>>1]=(c-g|0)>>>13;c=B+4096|0;r[e+6>>1]=(c+v|0)>>>13;r[e+16>>1]=(c-v|0)>>>13;c=a+4096|0;r[e+8>>1]=(c+o|0)>>>13;r[e+14>>1]=(c-o|0)>>>13;c=A+4096|0;r[e+10>>1]=(c+D|0)>>>13;r[e+12>>1]=(c-D|0)>>>13;s=C;return}function Yi(A){A=A|0;var e=0,f=0,i=0,t=0,w=0;if(!A){w=-1;return w|0}n[A>>2]=0;e=lr(44)|0;if(!e){w=-1;return w|0}f=e+40|0;if((Zi(f)|0)<<16>>16){w=-1;return w|0}i=e;t=7452;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));i=e+20|0;t=7452;w=i+20|0;do{r[i>>1]=r[t>>1]|0;i=i+2|0;t=t+2|0}while((i|0)<(w|0));_i(n[f>>2]|0)|0;n[A>>2]=e;w=0;return w|0}function zi(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}e=A;f=7452;i=e+20|0;do{r[e>>1]=r[f>>1]|0;e=e+2|0;f=f+2|0}while((e|0)<(i|0));e=A+20|0;f=7452;i=e+20|0;do{r[e>>1]=r[f>>1]|0;e=e+2|0;f=f+2|0}while((e|0)<(i|0));_i(n[A+40>>2]|0)|0;i=0;return i|0}function Xi(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;qi(e+40|0);Br(n[A>>2]|0);n[A>>2]=0;return}function Ji(A,e,f,i,t,w,l,B){A=A|0;e=e|0;f=f|0;i=i|0;t=t|0;w=w|0;l=l|0;B=B|0;var a=0,o=0,Q=0,g=0,v=0;v=s;s=s+64|0;g=v+44|0;a=v+24|0;o=v+4|0;Q=v;if((e|0)==7){Di(i+22|0,a,A,B);Di(i+66|0,w,a,B);Mi(A,a,w,i,B);if((f|0)==8)i=6;else{Wi(n[A+40>>2]|0,a,w,o,g,n[l>>2]|0,B);Ii(A+20|0,o,g,t,B);t=(n[l>>2]|0)+10|0;i=7}}else{Di(i+66|0,w,A,B);Hi(A,w,i,B);if((f|0)==8)i=6;else{ji(n[A+40>>2]|0,e,w,g,n[l>>2]|0,Q,B);Fi(A+20|0,g,t,B);t=(n[l>>2]|0)+6|0;i=7}}if((i|0)==6){lA()}else if((i|0)==7){n[l>>2]=t;i=A;t=i+20|0;do{r[i>>1]=r[w>>1]|0;i=i+2|0;w=w+2|0}while((i|0)<(t|0));i=A+20|0;w=g;t=i+20|0;do{r[i>>1]=r[w>>1]|0;i=i+2|0;w=w+2|0}while((i|0)<(t|0));s=v;return}}function Oi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0;if(f<<16>>16>0)i=0;else return;do{t=r[A+(i<<1)>>1]|0;w=t>>8;n=r[7194+(w<<1)>>1]|0;r[e+(i<<1)>>1]=((AA((r[7194+(w+1<<1)>>1]|0)-n|0,t&255)|0)>>>8)+n;i=i+1|0}while((i&65535)<<16>>16!=f<<16>>16);return}function mi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0;i=(f<<16>>16)+-1|0;f=i&65535;if(f<<16>>16<=-1)return;n=63;w=e+(i<<1)|0;t=A+(i<<1)|0;while(1){A=r[t>>1]|0;e=n;while(1){i=e<<16>>16;n=r[7194+(i<<1)>>1]|0;if(A<<16>>16>n<<16>>16)e=e+-1<<16>>16;else break}r[w>>1]=(((AA(r[7324+(i<<1)>>1]|0,(A<<16>>16)-(n<<16>>16)|0)|0)+2048|0)>>>12)+(i<<8);f=f+-1<<16>>16;if(f<<16>>16>-1){n=e;w=w+-2|0;t=t+-2|0}else break}return}function Ni(A){A=A|0;var e=0;A:do{if((A|0)!=0?(e=A-(A>>>31)|0,e=e>>31^e,(e&1073741824|0)==0):0){A=e;e=0;while(1){if(A&536870912){A=7;break}if(A&268435456){A=8;break}if(A&134217728){A=9;break}e=e+4<<16>>16;A=A<<4;if(A&1073741824)break A}if((A|0)==7){e=e|1;break}else if((A|0)==8){e=e|2;break}else if((A|0)==9){e=e|3;break}}else e=0}while(0);return e|0}function Ki(A){A=A|0;var e=0,f=0;if(!(A<<16>>16)){f=0;return f|0}e=(A&65535)-((A&65535)>>>15&65535)|0;e=(e<<16>>31^e)<<16;A=e>>16;if(!(A&16384)){f=e;e=0}else{f=0;return f|0}while(1){if(A&8192){A=e;f=7;break}if(A&4096){A=e;f=8;break}if(A&2048){A=e;f=9;break}e=e+4<<16>>16;f=f<<4;A=f>>16;if(A&16384){A=e;f=10;break}}if((f|0)==7){f=A|1;return f|0}else if((f|0)==8){f=A|2;return f|0}else if((f|0)==9){f=A|3;return f|0}else if((f|0)==10)return A|0;return 0}function xi(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,t=0,l=0;e=e<<16>>16;if((e&134217727|0)==33554432){n[f>>2]=1;e=2147483647}else e=e<<6;i=e>>>16&31;l=r[7792+(i<<1)>>1]|0;t=l<<16;e=AA(l-(w[7792+(i+1<<1)>>1]|0)<<16>>16,e>>>1&32767)|0;if((e|0)==1073741824){n[f>>2]=1;i=2147483647}else i=e<<1;e=t-i|0;if(((e^t)&(i^t)|0)>=0){l=e;A=A&65535;A=30-A|0;A=A&65535;f=Ti(l,A,f)|0;return f|0}n[f>>2]=1;l=(l>>>15&1)+2147483647|0;A=A&65535;A=30-A|0;A=A&65535;f=Ti(l,A,f)|0;return f|0}function Si(A,e,f,i,n,t){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0;g=s;s=s+48|0;Q=g;o=0-(f&65535)|0;o=n<<16>>16==0?o:o<<1&131070;f=o&65535;o=(f<<16>>16<0?o+6|0:o)<<16>>16;t=6-o|0;r[Q>>1]=r[7858+(o<<1)>>1]|0;r[Q+2>>1]=r[7858+(t<<1)>>1]|0;r[Q+4>>1]=r[7858+(o+6<<1)>>1]|0;r[Q+6>>1]=r[7858+(t+6<<1)>>1]|0;r[Q+8>>1]=r[7858+(o+12<<1)>>1]|0;r[Q+10>>1]=r[7858+(t+12<<1)>>1]|0;r[Q+12>>1]=r[7858+(o+18<<1)>>1]|0;r[Q+14>>1]=r[7858+(t+18<<1)>>1]|0;r[Q+16>>1]=r[7858+(o+24<<1)>>1]|0;r[Q+18>>1]=r[7858+(t+24<<1)>>1]|0;r[Q+20>>1]=r[7858+(o+30<<1)>>1]|0;r[Q+22>>1]=r[7858+(t+30<<1)>>1]|0;r[Q+24>>1]=r[7858+(o+36<<1)>>1]|0;r[Q+26>>1]=r[7858+(t+36<<1)>>1]|0;r[Q+28>>1]=r[7858+(o+42<<1)>>1]|0;r[Q+30>>1]=r[7858+(t+42<<1)>>1]|0;r[Q+32>>1]=r[7858+(o+48<<1)>>1]|0;r[Q+34>>1]=r[7858+(t+48<<1)>>1]|0;r[Q+36>>1]=r[7858+(o+54<<1)>>1]|0;r[Q+38>>1]=r[7858+(t+54<<1)>>1]|0;t=i<<16>>16>>>1&65535;if(!(t<<16>>16)){s=g;return}o=A+((f<<16>>16>>15<<16>>16)-(e<<16>>16)<<1)|0;while(1){a=o+2|0;w=r[a>>1]|0;e=w;i=o;l=5;B=Q;n=16384;f=16384;while(1){u=r[B>>1]|0;c=(AA(u,e<<16>>16)|0)+f|0;v=r[a+-2>>1]|0;f=(AA(v,u)|0)+n|0;u=i;i=i+4|0;C=r[B+2>>1]|0;f=f+(AA(C,w<<16>>16)|0)|0;n=r[i>>1]|0;C=c+(AA(n,C)|0)|0;a=a+-4|0;c=r[B+4>>1]|0;v=C+(AA(c,v)|0)|0;e=r[a>>1]|0;c=f+(AA(e<<16>>16,c)|0)|0;f=r[B+6>>1]|0;n=c+(AA(f,n)|0)|0;w=r[u+6>>1]|0;f=v+(AA(w<<16>>16,f)|0)|0;if(l<<16>>16<=1)break;else{l=l+-1<<16>>16;B=B+8|0}}r[A>>1]=n>>>15;r[A+2>>1]=f>>>15;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{o=o+4|0;A=A+4|0}}s=g;return}function ji(A,e,f,i,n,t,l){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;l=l|0;var B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0;F=s;s=s+144|0;D=F+120|0;b=F+100|0;I=F+80|0;M=F+60|0;d=F+40|0;u=F+20|0;c=F;mi(f,D,10,l);Ri(D,b,l);if((e|0)==8){lA()}else{f=0;do{k=AA(r[8160+(f<<1)>>1]|0,r[A+(f<<1)>>1]|0)|0;k=(k>>>15)+(w[8140+(f<<1)>>1]|0)|0;r[I+(f<<1)>>1]=k;r[M+(f<<1)>>1]=(w[D+(f<<1)>>1]|0)-k;f=f+1|0}while((f|0)!=10)}do{if(e>>>0>=2){k=M+2|0;P=M+4|0;h=w[M>>1]|0;E=r[b>>1]<<1;D=w[k>>1]|0;u=r[b+2>>1]<<1;v=w[P>>1]|0;g=r[b+4>>1]<<1;if((e|0)==5){c=2147483647;t=0;f=0;C=17908;while(1){o=(AA(h-(w[C>>1]|0)<<16>>16,E)|0)>>16;o=AA(o,o)|0;Q=(AA(D-(w[C+2>>1]|0)<<16>>16,u)|0)>>16;o=(AA(Q,Q)|0)+o|0;Q=(AA(v-(w[C+4>>1]|0)<<16>>16,g)|0)>>16;Q=o+(AA(Q,Q)|0)|0;o=(Q|0)<(c|0);f=o?t:f;t=t+1<<16>>16;if(t<<16>>16>=512)break;else{c=o?Q:c;C=C+6|0}}Q=(f<<16>>16)*3|0;r[M>>1]=r[17908+(Q<<1)>>1]|0;r[k>>1]=r[17908+(Q+1<<1)>>1]|0;r[P>>1]=r[17908+(Q+2<<1)>>1]|0;r[n>>1]=f;Q=M+6|0;o=M+8|0;h=M+10|0;C=w[Q>>1]|0;t=r[b+6>>1]<<1;c=w[o>>1]|0;u=r[b+8>>1]<<1;v=w[h>>1]|0;g=r[b+10>>1]<<1;B=2147483647;D=0;f=0;E=9716;while(1){a=(AA(t,C-(w[E>>1]|0)<<16>>16)|0)>>16;a=AA(a,a)|0;e=(AA(u,c-(w[E+2>>1]|0)<<16>>16)|0)>>16;a=(AA(e,e)|0)+a|0;e=(AA(g,v-(w[E+4>>1]|0)<<16>>16)|0)>>16;e=a+(AA(e,e)|0)|0;a=(e|0)<(B|0);f=a?D:f;D=D+1<<16>>16;if(D<<16>>16>=512)break;else{B=a?e:B;E=E+6|0}}B=(f<<16>>16)*3|0;r[Q>>1]=r[9716+(B<<1)>>1]|0;r[o>>1]=r[9716+(B+1<<1)>>1]|0;r[h>>1]=r[9716+(B+2<<1)>>1]|0;r[n+2>>1]=f;B=M+12|0;r[n+4>>1]=pi(B,12788,b+12|0,512)|0;D=k;C=P;f=h;a=M;break}else{c=2147483647;t=0;f=0;C=8180;while(1){o=(AA(h-(w[C>>1]|0)<<16>>16,E)|0)>>16;o=AA(o,o)|0;Q=(AA(D-(w[C+2>>1]|0)<<16>>16,u)|0)>>16;o=(AA(Q,Q)|0)+o|0;Q=(AA(v-(w[C+4>>1]|0)<<16>>16,g)|0)>>16;Q=o+(AA(Q,Q)|0)|0;o=(Q|0)<(c|0);f=o?t:f;t=t+1<<16>>16;if(t<<16>>16>=256)break;else{c=o?Q:c;C=C+6|0}}Q=(f<<16>>16)*3|0;r[M>>1]=r[8180+(Q<<1)>>1]|0;r[k>>1]=r[8180+(Q+1<<1)>>1]|0;r[P>>1]=r[8180+(Q+2<<1)>>1]|0;r[n>>1]=f;Q=M+6|0;o=M+8|0;h=M+10|0;C=w[Q>>1]|0;t=r[b+6>>1]<<1;c=w[o>>1]|0;u=r[b+8>>1]<<1;v=w[h>>1]|0;g=r[b+10>>1]<<1;B=2147483647;D=0;f=0;E=9716;while(1){a=(AA(t,C-(w[E>>1]|0)<<16>>16)|0)>>16;a=AA(a,a)|0;e=(AA(u,c-(w[E+2>>1]|0)<<16>>16)|0)>>16;a=(AA(e,e)|0)+a|0;e=(AA(g,v-(w[E+4>>1]|0)<<16>>16)|0)>>16;e=a+(AA(e,e)|0)|0;a=(e|0)<(B|0);f=a?D:f;D=D+1<<16>>16;if(D<<16>>16>=512)break;else{B=a?e:B;E=E+6|0}}B=(f<<16>>16)*3|0;r[Q>>1]=r[9716+(B<<1)>>1]|0;r[o>>1]=r[9716+(B+1<<1)>>1]|0;r[h>>1]=r[9716+(B+2<<1)>>1]|0;r[n+2>>1]=f;B=M+12|0;r[n+4>>1]=pi(B,12788,b+12|0,512)|0;D=k;C=P;f=h;a=M;break}}else{P=M+2|0;k=M+4|0;Q=w[M>>1]|0;o=r[b>>1]<<1;a=w[P>>1]|0;B=r[b+2>>1]<<1;e=w[k>>1]|0;g=r[b+4>>1]<<1;c=2147483647;t=0;f=0;C=8180;while(1){u=(AA(o,Q-(w[C>>1]|0)<<16>>16)|0)>>16;u=AA(u,u)|0;v=(AA(B,a-(w[C+2>>1]|0)<<16>>16)|0)>>16;u=(AA(v,v)|0)+u|0;v=(AA(g,e-(w[C+4>>1]|0)<<16>>16)|0)>>16;v=u+(AA(v,v)|0)|0;u=(v|0)<(c|0);f=u?t:f;t=t+1<<16>>16;if(t<<16>>16>=256)break;else{c=u?v:c;C=C+6|0}}Q=(f<<16>>16)*3|0;r[M>>1]=r[8180+(Q<<1)>>1]|0;r[P>>1]=r[8180+(Q+1<<1)>>1]|0;r[k>>1]=r[8180+(Q+2<<1)>>1]|0;r[n>>1]=f;Q=M+6|0;o=M+8|0;h=M+10|0;C=w[Q>>1]|0;t=r[b+6>>1]<<1;c=w[o>>1]|0;u=r[b+8>>1]<<1;v=w[h>>1]|0;g=r[b+10>>1]<<1;B=2147483647;D=0;f=0;E=9716;while(1){a=(AA(t,C-(w[E>>1]|0)<<16>>16)|0)>>16;a=AA(a,a)|0;e=(AA(u,c-(w[E+2>>1]|0)<<16>>16)|0)>>16;a=(AA(e,e)|0)+a|0;e=(AA(g,v-(w[E+4>>1]|0)<<16>>16)|0)>>16;e=a+(AA(e,e)|0)|0;a=(e|0)<(B|0);f=a?D:f;D=D+1<<16>>16;if(D<<16>>16>=256)break;else{B=a?e:B;E=E+12|0}}B=(f<<16>>16)*6|0;r[Q>>1]=r[9716+(B<<1)>>1]|0;r[o>>1]=r[9716+((B|1)<<1)>>1]|0;r[h>>1]=r[9716+(B+2<<1)>>1]|0;r[n+2>>1]=f;B=M+12|0;r[n+4>>1]=pi(B,16884,b+12|0,128)|0;D=P;C=k;f=h;a=M}}while(0);E=A;v=M;g=E+20|0;do{r[E>>1]=r[v>>1]|0;E=E+2|0;v=v+2|0}while((E|0)<(g|0));r[d>>1]=(w[I>>1]|0)+(w[a>>1]|0);r[d+2>>1]=(w[I+2>>1]|0)+(w[D>>1]|0);r[d+4>>1]=(w[I+4>>1]|0)+(w[C>>1]|0);r[d+6>>1]=(w[I+6>>1]|0)+(w[Q>>1]|0);r[d+8>>1]=(w[I+8>>1]|0)+(w[o>>1]|0);r[d+10>>1]=(w[I+10>>1]|0)+(w[f>>1]|0);r[d+12>>1]=(w[I+12>>1]|0)+(w[B>>1]|0);r[d+14>>1]=(w[I+14>>1]|0)+(w[M+14>>1]|0);r[d+16>>1]=(w[I+16>>1]|0)+(w[M+16>>1]|0);r[d+18>>1]=(w[I+18>>1]|0)+(w[M+18>>1]|0);$i(d,205,10,l);Oi(d,i,10,l);s=F;return}function pi(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0;D=A+2|0;E=A+4|0;h=A+6|0;if(i<<16>>16>0){o=w[A>>1]|0;Q=r[f>>1]<<1;g=w[D>>1]|0;v=r[f+2>>1]<<1;u=w[E>>1]|0;c=r[f+4>>1]<<1;C=w[h>>1]|0;n=r[f+6>>1]<<1;B=2147483647;a=0;f=0;s=e;while(1){t=(AA(Q,o-(w[s>>1]|0)<<16>>16)|0)>>16;t=AA(t,t)|0;l=(AA(v,g-(w[s+2>>1]|0)<<16>>16)|0)>>16;t=(AA(l,l)|0)+t|0;l=(AA(c,u-(w[s+4>>1]|0)<<16>>16)|0)>>16;l=t+(AA(l,l)|0)|0;t=(AA(n,C-(w[s+6>>1]|0)<<16>>16)|0)>>16;t=l+(AA(t,t)|0)|0;l=(t|0)<(B|0);f=l?a:f;a=a+1<<16>>16;if(a<<16>>16>=i<<16>>16)break;else{B=l?t:B;s=s+8|0}}}else f=0;i=f<<16>>16<<2;C=i|1;r[A>>1]=r[e+(i<<1)>>1]|0;r[D>>1]=r[e+(C<<1)>>1]|0;r[E>>1]=r[e+(C+1<<1)>>1]|0;r[h>>1]=r[e+((i|3)<<1)>>1]|0;return f|0}function Wi(A,e,f,i,n,t,l){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;l=l|0;var B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0;T=s;s=s+192|0;o=T+160|0;a=T+140|0;F=T+120|0;H=T+100|0;U=T+80|0;G=T+60|0;B=T+40|0;L=T+20|0;R=T;mi(e,o,10,l);mi(f,a,10,l);Ri(o,F,l);Ri(a,H,l);Q=0;f=U;e=G;g=B;while(1){M=(((r[A+(Q<<1)>>1]|0)*21299|0)>>>15)+(w[20980+(Q<<1)>>1]|0)|0;r[f>>1]=M;r[e>>1]=(w[o>>1]|0)-M;r[g>>1]=(w[a>>1]|0)-M;Q=Q+1|0;if((Q|0)==10)break;else{o=o+2|0;a=a+2|0;f=f+2|0;e=e+2|0;g=g+2|0}}r[t>>1]=Vi(G,B,21e3,r[F>>1]|0,r[F+2>>1]|0,r[H>>1]|0,r[H+2>>1]|0,128)|0;r[t+2>>1]=Vi(G+4|0,B+4|0,22024,r[F+4>>1]|0,r[F+6>>1]|0,r[H+4>>1]|0,r[H+6>>1]|0,256)|0;b=G+8|0;d=B+8|0;I=G+10|0;M=B+10|0;f=r[b>>1]|0;v=r[F+8>>1]<<1;u=r[I>>1]|0;c=r[F+10>>1]<<1;C=r[d>>1]|0;D=r[H+8>>1]<<1;E=r[M>>1]|0;h=r[H+10>>1]<<1;a=2147483647;P=0;g=0;k=24072;e=0;while(1){o=r[k>>1]|0;Q=(AA(f-o<<16>>16,v)|0)>>16;Q=AA(Q,Q)|0;o=(AA(o+f<<16>>16,v)|0)>>16;o=AA(o,o)|0;y=r[k+2>>1]|0;Y=(AA(u-y<<16>>16,c)|0)>>16;Q=(AA(Y,Y)|0)+Q|0;y=(AA(y+u<<16>>16,c)|0)>>16;o=(AA(y,y)|0)+o|0;if((Q|0)<(a|0)|(o|0)<(a|0)){Y=r[k+4>>1]|0;y=(AA(C-Y<<16>>16,D)|0)>>16;y=(AA(y,y)|0)+Q|0;Y=(AA(Y+C<<16>>16,D)|0)>>16;Y=(AA(Y,Y)|0)+o|0;o=r[k+6>>1]|0;Q=(AA(E-o<<16>>16,h)|0)>>16;Q=y+(AA(Q,Q)|0)|0;o=(AA(o+E<<16>>16,h)|0)>>16;o=Y+(AA(o,o)|0)|0;Y=(Q|0)<(a|0);Q=Y?Q:a;y=(o|0)<(Q|0);Q=y?o:Q;g=Y|y?P:g;e=y?1:Y?0:e}else Q=a;P=P+1<<16>>16;if(P<<16>>16>=256)break;else{a=Q;k=k+8|0}}Q=g<<16>>16;o=Q<<2;g=o|1;a=24072+(g<<1)|0;f=r[24072+(o<<1)>>1]|0;if(!(e<<16>>16)){r[b>>1]=f;r[I>>1]=r[a>>1]|0;r[d>>1]=r[24072+(g+1<<1)>>1]|0;r[M>>1]=r[24072+((o|3)<<1)>>1]|0;e=Q<<1}else{r[b>>1]=0-(f&65535);r[I>>1]=0-(w[a>>1]|0);r[d>>1]=0-(w[24072+(g+1<<1)>>1]|0);r[M>>1]=0-(w[24072+((o|3)<<1)>>1]|0);e=Q<<1&65534|1}r[t+4>>1]=e;r[t+6>>1]=Vi(G+12|0,B+12|0,26120,r[F+12>>1]|0,r[F+14>>1]|0,r[H+12>>1]|0,r[H+14>>1]|0,256)|0;r[t+8>>1]=Vi(G+16|0,B+16|0,28168,r[F+16>>1]|0,r[F+18>>1]|0,r[H+16>>1]|0,r[H+18>>1]|0,64)|0;a=0;o=L;Q=R;f=U;e=G;while(1){y=w[f>>1]|0;r[o>>1]=y+(w[e>>1]|0);Y=r[B>>1]|0;r[Q>>1]=y+(Y&65535);r[A+(a<<1)>>1]=Y;a=a+1|0;if((a|0)==10)break;else{o=o+2|0;Q=Q+2|0;f=f+2|0;e=e+2|0;B=B+2|0}}$i(L,205,10,l);$i(R,205,10,l);Oi(L,i,10,l);Oi(R,n,10,l);s=T;return}function Vi(A,e,f,i,n,t,w,l){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;w=w|0;l=l|0;var B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0;g=r[A>>1]|0;E=A+2|0;u=r[E>>1]|0;C=r[e>>1]|0;h=e+2|0;D=r[h>>1]|0;if(l<<16>>16>0){Q=i<<16>>16<<1;o=n<<16>>16<<1;s=t<<16>>16<<1;n=w<<16>>16<<1;t=2147483647;B=0;i=0;a=f;while(1){w=(AA(Q,g-(r[a>>1]|0)|0)|0)>>16;w=AA(w,w)|0;if(((w|0)<(t|0)?(v=(AA(o,u-(r[a+2>>1]|0)|0)|0)>>16,v=(AA(v,v)|0)+w|0,(v|0)<(t|0)):0)?(c=(AA(s,C-(r[a+4>>1]|0)|0)|0)>>16,c=(AA(c,c)|0)+v|0,(c|0)<(t|0)):0){w=(AA(n,D-(r[a+6>>1]|0)|0)|0)>>16;w=(AA(w,w)|0)+c|0;P=(w|0)<(t|0);w=P?w:t;i=P?B:i}else w=t;B=B+1<<16>>16;if(B<<16>>16>=l<<16>>16)break;else{t=w;a=a+8|0}}}else i=0;P=i<<16>>16<<2;l=P|1;r[A>>1]=r[f+(P<<1)>>1]|0;r[E>>1]=r[f+(l<<1)>>1]|0;r[e>>1]=r[f+(l+1<<1)>>1]|0;r[h>>1]=r[f+((P|3)<<1)>>1]|0;return i|0}function Zi(A){A=A|0;var e=0,f=0,i=0;if(!A){i=-1;return i|0}n[A>>2]=0;e=lr(20)|0;if(!e){i=-1;return i|0}f=e;i=f+20|0;do{r[f>>1]=0;f=f+2|0}while((f|0)<(i|0));n[A>>2]=e;i=0;return i|0}function _i(A){A=A|0;var e=0;if(!A){e=-1;return e|0}e=A+20|0;do{r[A>>1]=0;A=A+2|0}while((A|0)<(e|0));e=0;return e|0}function qi(A){A=A|0;var e=0;if(!A)return;e=n[A>>2]|0;if(!e)return;Br(e);n[A>>2]=0;return}function $i(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0;if(f<<16>>16<=0)return;n=e<<16>>16;t=e&65535;w=0;while(1){i=r[A>>1]|0;if(i<<16>>16<e<<16>>16){r[A>>1]=e;i=(e<<16>>16)+n|0}else i=(i&65535)+t|0;w=w+1<<16>>16;if(w<<16>>16>=f<<16>>16)break;else{e=i&65535;A=A+2|0}}return}function Ar(A,e,f,i){A=A|0;e=e|0;f=f|0;i=i|0;var n=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0;n=i<<16>>16;i=n>>>2&65535;if(!(i<<16>>16))return;Q=n+-1|0;D=A+20|0;v=e+(n+-4<<1)|0;u=e+(n+-3<<1)|0;c=e+(n+-2<<1)|0;C=e+(Q<<1)|0;g=e+(n+-11<<1)|0;Q=f+(Q<<1)|0;while(1){e=r[D>>1]|0;w=5;l=D;B=g;a=g+-2|0;s=g+-4|0;o=g+-6|0;t=2048;A=2048;n=2048;f=2048;while(1){t=(AA(r[B>>1]|0,e)|0)+t|0;A=(AA(r[a>>1]|0,e)|0)+A|0;n=(AA(r[s>>1]|0,e)|0)+n|0;e=(AA(r[o>>1]|0,e)|0)+f|0;f=r[l+-2>>1]|0;t=t+(AA(r[B+2>>1]|0,f)|0)|0;A=A+(AA(r[a+2>>1]|0,f)|0)|0;n=n+(AA(r[s+2>>1]|0,f)|0)|0;l=l+-4|0;f=e+(AA(r[o+2>>1]|0,f)|0)|0;w=w+-1<<16>>16;e=r[l>>1]|0;if(!(w<<16>>16))break;else{B=B+4|0;a=a+4|0;s=s+4|0;o=o+4|0}}B=(AA(r[C>>1]|0,e)|0)+t|0;a=(AA(r[c>>1]|0,e)|0)+A|0;s=(AA(r[u>>1]|0,e)|0)+n|0;o=(AA(r[v>>1]|0,e)|0)+f|0;r[Q>>1]=B>>>12;r[Q+-2>>1]=a>>>12;r[Q+-4>>1]=s>>>12;r[Q+-6>>1]=o>>>12;i=i+-1<<16>>16;if(!(i<<16>>16))break;else{v=v+-8|0;u=u+-8|0;c=c+-8|0;C=C+-8|0;g=g+-8|0;Q=Q+-8|0}}return}function er(A,e){A=A|0;e=e|0;var f=0;f=A+32768|0;if((A|0)>-1&(f^A|0)<0){n[e>>2]=1;f=(A>>>31)+2147483647|0}return f>>>16&65535|0}function fr(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,r=0;i=e<<16>>16;if(!(e<<16>>16))return A|0;if(e<<16>>16>0){A=A<<16>>16>>(e<<16>>16>15?15:i)&65535;return A|0}r=0-i|0;e=A<<16>>16;r=(r&65535)<<16>>16>15?15:r<<16>>16;i=e<<r;if((i<<16>>16>>r|0)==(e|0)){r=i&65535;return r|0}n[f>>2]=1;r=A<<16>>16>0?32767:-32768;return r|0}function ir(A,e,f){A=A|0;e=e|0;f=f|0;if(e<<16>>16>15){e=0;return e|0}f=fr(A,e,f)|0;if(e<<16>>16>0)return f+((1<<(e<<16>>16)+-1&A<<16>>16|0)!=0&1)<<16>>16|0;else{e=f;return e|0}return 0}function rr(A,e,f){A=A|0;e=e|0;f=f|0;var i=0,t=0,l=0;if((A|0)<1){r[e>>1]=0;f=0;return f|0}t=(Ni(A)|0)&65534;l=t&65535;t=t<<16>>16;if(l<<16>>16>0){i=A<<t;if((i>>t|0)!=(A|0))i=A>>31^2147483647}else{t=0-t<<16;if((t|0)<2031616)i=A>>(t>>16);else i=0}r[e>>1]=l;e=i>>>25&63;e=e>>>0>15?e+-16|0:e;l=r[30216+(e<<1)>>1]|0;A=l<<16;i=AA(l-(w[30216+(e+1<<1)>>1]|0)<<16>>16,i>>>10&32767)|0;if((i|0)==1073741824){n[f>>2]=1;t=2147483647}else t=i<<1;i=A-t|0;if(((i^A)&(t^A)|0)>=0){f=i;return f|0}n[f>>2]=1;f=(l>>>15&1)+2147483647|0;return f|0}function nr(A,e,f){A=A|0;e=e|0;f=f|0;A=(A<<16>>16)-(e<<16>>16)|0;if((A+32768|0)>>>0<=65535){f=A;f=f&65535;return f|0}n[f>>2]=1;f=(A|0)>32767?32767:-32768;f=f&65535;return f|0}function tr(A,e,f,i,n,t){A=A|0;e=e|0;f=f|0;i=i|0;n=n|0;t=t|0;var w=0,l=0,B=0,a=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0;I=s;s=s+48|0;g=I;B=g;w=n;l=B+20|0;do{r[B>>1]=r[w>>1]|0;B=B+2|0;w=w+2|0}while((B|0)<(l|0));Q=g+18|0;C=A+2|0;D=A+4|0;v=e+20|0;E=A+6|0;h=A+8|0;P=A+10|0;k=A+12|0;b=A+14|0;d=A+16|0;u=A+18|0;c=A+20|0;l=r[Q>>1]|0;w=5;a=e;o=f;B=g+20|0;while(1){H=r[A>>1]|0;F=(AA(H,r[a>>1]|0)|0)+2048|0;H=(AA(r[a+2>>1]|0,H)|0)+2048|0;g=l<<16>>16;F=F-(AA(g,r[C>>1]|0)|0)|0;M=r[D>>1]|0;g=H-(AA(g,M)|0)|0;H=r[Q+-2>>1]|0;M=F-(AA(H,M)|0)|0;F=r[E>>1]|0;H=g-(AA(F,H)|0)|0;g=r[Q+-4>>1]|0;F=M-(AA(g,F)|0)|0;M=r[h>>1]|0;g=H-(AA(M,g)|0)|0;H=r[Q+-6>>1]|0;M=F-(AA(H,M)|0)|0;F=r[P>>1]|0;H=g-(AA(H,F)|0)|0;g=r[Q+-8>>1]|0;F=M-(AA(g,F)|0)|0;M=r[k>>1]|0;g=H-(AA(M,g)|0)|0;H=r[Q+-10>>1]|0;M=F-(AA(H,M)|0)|0;F=r[b>>1]|0;H=g-(AA(F,H)|0)|0;g=r[Q+-12>>1]|0;F=M-(AA(g,F)|0)|0;M=r[d>>1]|0;g=H-(AA(g,M)|0)|0;H=r[Q+-14>>1]|0;M=F-(AA(H,M)|0)|0;F=r[u>>1]|0;H=g-(AA(F,H)|0)|0;g=r[Q+-16>>1]|0;F=M-(AA(g,F)|0)|0;M=r[c>>1]|0;g=H-(AA(M,g)|0)|0;M=F-(AA(r[Q+-18>>1]|0,M)|0)|0;M=(M+134217728|0)>>>0<268435455?M>>>12&65535:(M|0)>134217727?32767:-32768;g=g-(AA(r[C>>1]|0,M<<16>>16)|0)|0;Q=B+2|0;r[B>>1]=M;r[o>>1]=M;l=(g+134217728|0)>>>0<268435455?g>>>12&65535:(g|0)>134217727?32767:-32768;r[Q>>1]=l;r[o+2>>1]=l;w=w+-1<<16>>16;if(!(w<<16>>16))break;else{a=a+4|0;o=o+4|0;B=B+4|0}}i=(i<<16>>16)+-10|0;B=i>>>1&65535;if(B<<16>>16){g=f+18|0;l=e+16|0;Q=r[g>>1]|0;a=v;w=f+20|0;while(1){M=r[A>>1]|0;o=(AA(M,r[a>>1]|0)|0)+2048|0;M=(AA(r[l+6>>1]|0,M)|0)+2048|0;l=r[C>>1]|0;F=Q<<16>>16;o=o-(AA(F,l)|0)|0;H=r[D>>1]|0;F=M-(AA(F,H)|0)|0;M=r[g+-2>>1]|0;H=o-(AA(M,H)|0)|0;o=r[E>>1]|0;M=F-(AA(o,M)|0)|0;F=r[g+-4>>1]|0;o=H-(AA(F,o)|0)|0;H=r[h>>1]|0;F=M-(AA(H,F)|0)|0;M=r[g+-6>>1]|0;H=o-(AA(M,H)|0)|0;o=r[P>>1]|0;M=F-(AA(M,o)|0)|0;F=r[g+-8>>1]|0;o=H-(AA(F,o)|0)|0;H=r[k>>1]|0;F=M-(AA(H,F)|0)|0;M=r[g+-10>>1]|0;H=o-(AA(M,H)|0)|0;o=r[b>>1]|0;M=F-(AA(o,M)|0)|0;F=r[g+-12>>1]|0;o=H-(AA(F,o)|0)|0;H=r[d>>1]|0;F=M-(AA(F,H)|0)|0;M=r[g+-14>>1]|0;H=o-(AA(M,H)|0)|0;o=r[u>>1]|0;M=F-(AA(o,M)|0)|0;F=r[g+-16>>1]|0;o=H-(AA(F,o)|0)|0;H=r[c>>1]|0;F=M-(AA(H,F)|0)|0;H=o-(AA(r[g+-18>>1]|0,H)|0)|0;o=a+4|0;H=(H+134217728|0)>>>0<268435455?H>>>12&65535:(H|0)>134217727?32767:-32768;l=F-(AA(l,H<<16>>16)|0)|0;g=w+2|0;r[w>>1]=H;do{if((l+134217728|0)>>>0>=268435455){w=w+4|0;if((l|0)>134217727){r[g>>1]=32767;l=32767;break}else{r[g>>1]=-32768;l=-32768;break}}else{l=l>>>12&65535;r[g>>1]=l;w=w+4|0}}while(0);B=B+-1<<16>>16;if(!(B<<16>>16))break;else{H=a;Q=l;a=o;l=H}}}if(!(t<<16>>16)){s=I;return}B=n;w=f+(i<<1)|0;l=B+20|0;do{r[B>>1]=r[w>>1]|0;B=B+2|0;w=w+2|0}while((B|0)<(l|0));s=I;return}function wr(A,e,f){A=A|0;e=e|0;f=f|0;r[f>>1]=r[A>>1]|0;r[f+2>>1]=((AA(r[e>>1]|0,r[A+2>>1]|0)|0)+16384|0)>>>15;r[f+4>>1]=((AA(r[e+2>>1]|0,r[A+4>>1]|0)|0)+16384|0)>>>15;r[f+6>>1]=((AA(r[e+4>>1]|0,r[A+6>>1]|0)|0)+16384|0)>>>15;r[f+8>>1]=((AA(r[e+6>>1]|0,r[A+8>>1]|0)|0)+16384|0)>>>15;r[f+10>>1]=((AA(r[e+8>>1]|0,r[A+10>>1]|0)|0)+16384|0)>>>15;r[f+12>>1]=((AA(r[e+10>>1]|0,r[A+12>>1]|0)|0)+16384|0)>>>15;r[f+14>>1]=((AA(r[e+12>>1]|0,r[A+14>>1]|0)|0)+16384|0)>>>15;r[f+16>>1]=((AA(r[e+14>>1]|0,r[A+16>>1]|0)|0)+16384|0)>>>15;r[f+18>>1]=((AA(r[e+16>>1]|0,r[A+18>>1]|0)|0)+16384|0)>>>15;r[f+20>>1]=((AA(r[e+18>>1]|0,r[A+20>>1]|0)|0)+16384|0)>>>15;return}function lr(A){A=A|0;var e=0,f=0,i=0,r=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0,F=0,H=0,U=0,G=0,L=0,R=0,T=0,y=0,Y=0,z=0,X=0,J=0,O=0,m=0,N=0,K=0,x=0,S=0,j=0,p=0;do{if(A>>>0<245){C=A>>>0<11?16:A+11&-8;A=C>>>3;o=n[26]|0;B=o>>>A;if(B&3){lA()}e=n[28]|0;if(C>>>0>e>>>0){if(B){lA()}A=n[27]|0;if(A){lA()}else p=154}else p=154}else if(A>>>0<=4294967231){A=A+11|0;P=A&-8;o=n[27]|0;if(o){lA()}else{C=P;p=154}}else{C=-1;p=154}}while(0);A:do{if((p|0)==154){A=n[28]|0;if(A>>>0>=C>>>0){lA()}A=n[29]|0;if(A>>>0>C>>>0){p=A-C|0;n[29]=p;t=n[32]|0;n[32]=t+C;n[t+(C+4)>>2]=p|1;n[t+4>>2]=C|3;t=t+8|0;break}if(!(n[144]|0))sr();o=C+48|0;f=n[146]|0;s=C+47|0;i=f+s|0;f=0-f|0;a=i&f;if(a>>>0>C>>>0){A=n[136]|0;if((A|0)!=0?(F=n[134]|0,R=F+a|0,R>>>0<=F>>>0|R>>>0>A>>>0):0){t=0;break}e:do{if(!(n[137]&4)){A=n[32]|0;f:do{if(A){l=552;while(1){B=n[l>>2]|0;if(B>>>0<=A>>>0?(k=l+4|0,(B+(n[k>>2]|0)|0)>>>0>A>>>0):0){t=l;A=k;break}l=n[l+8>>2]|0;if(!l){p=172;break f}}B=i-(n[29]|0)&f;if(B>>>0<2147483647){l=aA(B|0)|0;R=(l|0)==((n[t>>2]|0)+(n[A>>2]|0)|0);A=R?B:0;if(R){if((l|0)!=(-1|0)){I=l;D=A;p=192;break e}}else p=182}else A=0}else p=172}while(0);do{if((p|0)==172){t=aA(0)|0;if((t|0)!=(-1|0)){A=t;B=n[145]|0;l=B+-1|0;if(!(l&A))B=a;else B=a-A+(l+A&0-B)|0;A=n[134]|0;l=A+B|0;if(B>>>0>C>>>0&B>>>0<2147483647){R=n[136]|0;if((R|0)!=0?l>>>0<=A>>>0|l>>>0>R>>>0:0){A=0;break}l=aA(B|0)|0;p=(l|0)==(t|0);A=p?B:0;if(p){I=t;D=A;p=192;break e}else p=182}else A=0}else A=0}}while(0);f:do{if((p|0)==182){lA()}}while(0);n[137]=n[137]|4;p=189}else{A=0;p=189}}while(0);if((((p|0)==189?a>>>0<2147483647:0)?(H=aA(a|0)|0,U=aA(0)|0,H>>>0<U>>>0&((H|0)!=(-1|0)&(U|0)!=(-1|0))):0)?(G=U-H|0,L=G>>>0>(C+40|0)>>>0,L):0){I=H;D=L?G:A;p=192}if((p|0)==192){B=(n[134]|0)+D|0;n[134]=B;if(B>>>0>(n[135]|0)>>>0)n[135]=B;u=n[32]|0;e:do{if(u){t=552;do{A=n[t>>2]|0;B=t+4|0;l=n[B>>2]|0;if((I|0)==(A+l|0)){T=A;y=B;Y=l;z=t;p=202;break}t=n[t+8>>2]|0}while((t|0)!=0);if(((p|0)==202?(n[z+12>>2]&8|0)==0:0)?u>>>0<I>>>0&u>>>0>=T>>>0:0){n[y>>2]=Y+D;p=(n[29]|0)+D|0;j=u+8|0;j=(j&7|0)==0?0:0-j&7;S=p-j|0;n[32]=u+j;n[29]=S;n[u+(j+4)>>2]=S|1;n[u+(p+4)>>2]=40;n[33]=n[148];break}B=n[30]|0;if(I>>>0<B>>>0){n[30]=I;B=I}l=I+D|0;A=552;while(1){if((n[A>>2]|0)==(l|0)){lA()}A=n[A+8>>2]|0;if(!A){l=552;break}}if((p|0)==210)if(!(n[l+12>>2]&8)){lA()}else l=552;while(1){t=n[l>>2]|0;if(t>>>0<=u>>>0?(w=n[l+4>>2]|0,r=t+w|0,r>>>0>u>>>0):0)break;l=n[l+8>>2]|0}l=t+(w+-39)|0;l=t+(w+-47+((l&7|0)==0?0:0-l&7))|0;B=u+16|0;l=l>>>0<B>>>0?u:l;w=l+8|0;t=I+8|0;t=(t&7|0)==0?0:0-t&7;p=D+-40-t|0;n[32]=I+t;n[29]=p;n[I+(t+4)>>2]=p|1;n[I+(D+-36)>>2]=40;n[33]=n[148];t=l+4|0;n[t>>2]=27;n[w>>2]=n[138];n[w+4>>2]=n[139];n[w+8>>2]=n[140];n[w+12>>2]=n[141];n[138]=I;n[139]=D;n[141]=0;n[140]=w;w=l+28|0;n[w>>2]=7;if((l+32|0)>>>0<r>>>0)do{lA()}while((p+8|0)>>>0<r>>>0);if((l|0)!=(u|0)){lA()}}else{p=n[30]|0;if((p|0)==0|I>>>0<p>>>0)n[30]=I;n[138]=I;n[139]=D;n[141]=0;n[35]=n[144];n[34]=-1;f=0;do{p=f<<1;j=144+(p<<2)|0;n[144+(p+3<<2)>>2]=j;n[144+(p+2<<2)>>2]=j;f=f+1|0}while((f|0)!=32);p=I+8|0;p=(p&7|0)==0?0:0-p&7;j=D+-40-p|0;n[32]=I+p;n[29]=j;n[I+(p+4)>>2]=j|1;n[I+(D+-36)>>2]=40;n[33]=n[148]}}while(0);e=n[29]|0;if(e>>>0>C>>>0){p=e-C|0;n[29]=p;t=n[32]|0;n[32]=t+C;n[t+(C+4)>>2]=p|1;n[t+4>>2]=C|3;t=t+8|0;break}}n[(ar()|0)>>2]=12;t=0}else t=0}}while(0);return t|0}function Br(A){A=A|0;var e=0,f=0,i=0,r=0,t=0,w=0,l=0,B=0,a=0,s=0,o=0,Q=0,g=0,v=0,u=0,c=0,C=0,D=0,E=0,h=0,P=0,k=0,b=0,d=0,I=0,M=0;A:do{if(A){r=A+-8|0;a=n[30]|0;e:do{if(r>>>0>=a>>>0?(i=n[A+-4>>2]|0,f=i&3,(f|0)!=1):0){h=i&-8;P=A+(h+-8)|0;do{if(!(i&1)){r=n[r>>2]|0;if(!f)break A;s=-8-r|0;Q=A+s|0;g=r+h|0;if(Q>>>0<a>>>0)break e;if((Q|0)==(n[31]|0)){lA()}f=r>>>3;if(r>>>0<256){i=n[A+(s+8)>>2]|0;t=n[A+(s+12)>>2]|0;r=144+(f<<1<<2)|0;do{if((i|0)!=(r|0)){lA()}}while(0);if((t|0)==(i|0)){n[26]=n[26]&~(1<<f);M=Q;t=g;break}do{if((t|0)==(r|0))e=t+8|0;else{lA()}}while(0);n[i+12>>2]=t;n[e>>2]=i;M=Q;t=g;break}w=n[A+(s+24)>>2]|0;r=n[A+(s+12)>>2]|0;do{if((r|0)==(Q|0)){i=A+(s+20)|0;r=n[i>>2]|0;if(!r){i=A+(s+16)|0;r=n[i>>2]|0;if(!r){o=0;break}}while(1){f=r+20|0;e=n[f>>2]|0;if(e){r=e;i=f;continue}f=r+16|0;e=n[f>>2]|0;if(!e)break;else{r=e;i=f}}if(i>>>0<a>>>0)wA();else{n[i>>2]=0;o=r;break}}else{lA()}}while(0);if(w){r=n[A+(s+28)>>2]|0;i=408+(r<<2)|0;if((Q|0)==(n[i>>2]|0)){n[i>>2]=o;if(!o){n[27]=n[27]&~(1<<r);M=Q;t=g;break}}else{if(w>>>0<(n[30]|0)>>>0)wA();r=w+16|0;if((n[r>>2]|0)==(Q|0))n[r>>2]=o;else n[w+20>>2]=o;if(!o){M=Q;t=g;break}}i=n[30]|0;if(o>>>0<i>>>0)wA();n[o+24>>2]=w;r=n[A+(s+16)>>2]|0;do{if(r)if(r>>>0<i>>>0)wA();else{n[o+16>>2]=r;n[r+24>>2]=o;break}}while(0);r=n[A+(s+20)>>2]|0;if(r)if(r>>>0<(n[30]|0)>>>0)wA();else{lA()}else{M=Q;t=g}}else{M=Q;t=g}}else{M=r;t=h}}while(0);if(M>>>0<P>>>0?(v=A+(h+-4)|0,u=n[v>>2]|0,(u&1|0)!=0):0){if(!(u&2)){if((P|0)==(n[32]|0)){I=(n[29]|0)+t|0;n[29]=I;n[32]=M;n[M+4>>2]=I|1;if((M|0)!=(n[31]|0))break A;n[31]=0;n[28]=0;break A}if((P|0)==(n[31]|0)){lA()}B=(u&-8)+t|0;f=u>>>3;do{if(u>>>0>=256){e=n[A+(h+16)>>2]|0;t=n[A+(h|4)>>2]|0;do{if((t|0)==(P|0)){r=A+(h+12)|0;t=n[r>>2]|0;if(!t){r=A+(h+8)|0;t=n[r>>2]|0;if(!t){k=0;break}}while(1){i=t+20|0;f=n[i>>2]|0;if(f){t=f;r=i;continue}i=t+16|0;f=n[i>>2]|0;if(!f)break;else{t=f;r=i}}if(r>>>0<(n[30]|0)>>>0)wA();else{n[r>>2]=0;k=t;break}}else{lA()}}while(0);if(e){t=n[A+(h+20)>>2]|0;r=408+(t<<2)|0;if((P|0)==(n[r>>2]|0)){n[r>>2]=k;if(!k){n[27]=n[27]&~(1<<t);break}}else{if(e>>>0<(n[30]|0)>>>0)wA();t=e+16|0;if((n[t>>2]|0)==(P|0))n[t>>2]=k;else n[e+20>>2]=k;if(!k)break}t=n[30]|0;if(k>>>0<t>>>0)wA();n[k+24>>2]=e;r=n[A+(h+8)>>2]|0;do{if(r)if(r>>>0<t>>>0)wA();else{lA()}}while(0);f=n[A+(h+12)>>2]|0;if(f)if(f>>>0<(n[30]|0)>>>0)wA();else{lA()}}}else{i=n[A+h>>2]|0;t=n[A+(h|4)>>2]|0;r=144+(f<<1<<2)|0;do{if((i|0)!=(r|0)){lA()}}while(0);if((t|0)==(i|0)){n[26]=n[26]&~(1<<f);break}do{if((t|0)==(r|0))c=t+8|0;else{lA()}}while(0);n[i+12>>2]=t;n[c>>2]=i}}while(0);n[M+4>>2]=B|1;n[M+B>>2]=B;if((M|0)==(n[31]|0)){n[28]=B;break A}else t=B}else{n[v>>2]=u&-2;n[M+4>>2]=t|1;n[M+t>>2]=t}r=t>>>3;if(t>>>0<256){i=r<<1;t=144+(i<<2)|0;e=n[26]|0;f=1<<r;if(e&f){lA()}else{n[26]=e|f;b=144+(i+2<<2)|0;d=t}n[b>>2]=M;n[d+12>>2]=M;n[M+8>>2]=d;n[M+12>>2]=t;break A}e=t>>>8;if(e)if(t>>>0>16777215)r=31;else{b=(e+1048320|0)>>>16&8;d=e<<b;A=(d+520192|0)>>>16&4;d=d<<A;r=(d+245760|0)>>>16&2;r=14-(A|b|r)+(d<<r>>>15)|0;r=t>>>(r+7|0)&1|r<<1}else r=0;f=408+(r<<2)|0;n[M+28>>2]=r;n[M+20>>2]=0;n[M+16>>2]=0;e=n[27]|0;i=1<<r;f:do{if(e&i){f=n[f>>2]|0;i:do{if((n[f+4>>2]&-8|0)!=(t|0)){r=t<<((r|0)==31?0:25-(r>>>1)|0);while(1){e=f+16+(r>>>31<<2)|0;i=n[e>>2]|0;if(!i)break;if((n[i+4>>2]&-8|0)==(t|0)){I=i;break i}else{r=r<<1;f=i}}if(e>>>0<(n[30]|0)>>>0)wA();else{n[e>>2]=M;n[M+24>>2]=f;n[M+12>>2]=M;n[M+8>>2]=M;break f}}else I=f}while(0);e=I+8|0;f=n[e>>2]|0;d=n[30]|0;if(f>>>0>=d>>>0&I>>>0>=d>>>0){lA()}else wA()}else{n[27]=e|i;n[f>>2]=M;n[M+24>>2]=f;n[M+12>>2]=M;n[M+8>>2]=M}}while(0);M=(n[34]|0)+-1|0;n[34]=M;if(!M)e=560;else break A;while(1){e=n[e>>2]|0;if(!e)break;else e=e+8|0}n[34]=-1;break A}}}while(0);wA()}}while(0);return}function ar(){var A=0;if(!0)A=600;else A=n[(tA()|0)+60>>2]|0;return A|0}function sr(){var A=0;do{if(!(n[144]|0)){A=nA(30)|0;if(!(A+-1&A)){n[146]=A;n[145]=A;n[147]=-1;n[148]=-1;n[149]=0;n[137]=0;n[144]=(sA(0)|0)&-16^1431655768;break}else wA()}}while(0);return}function or(A,e,f){A=A|0;e=e|0;f=f|0;var r=0;if((f|0)>=4096)return QA(A|0,e|0,f|0)|0;r=A|0;if((A&3)==(e&3)){while(A&3){if(!f)return r|0;i[A>>0]=i[e>>0]|0;A=A+1|0;e=e+1|0;f=f-1|0}while((f|0)>=4){n[A>>2]=n[e>>2];A=A+4|0;e=e+4|0;f=f-4|0}}while((f|0)>0){i[A>>0]=i[e>>0]|0;A=A+1|0;e=e+1|0;f=f-1|0}return r|0}function Qr(A,e,f){A=A|0;e=e|0;f=f|0;var i=0;if((e|0)<(A|0)&(A|0)<(e+f|0)){lA()}else or(A,e,f)|0;return A|0}function gr(A,e,f){A=A|0;e=e|0;f=f|0;var r=0,t=0,w=0,l=0;r=A+f|0;if((f|0)>=20){e=e&255;w=A&3;l=e|e<<8|e<<16|e<<24;t=r&~3;if(w){w=A+4-w|0;while((A|0)<(w|0)){i[A>>0]=e;A=A+1|0}}while((A|0)<(t|0)){n[A>>2]=l;A=A+4|0}}while((A|0)<(r|0)){i[A>>0]=e;A=A+1|0}return A-f|0}return{_free:Br,___errno_location:ar,_memmove:Qr,_Decoder_Interface_Decode:CA,_Decoder_Interface_exit:cA,_Encoder_Interface_init:DA,_memset:gr,_malloc:lr,_memcpy:or,_Encoder_Interface_exit:EA,_Decoder_Interface_init:uA,_Encoder_Interface_Encode:hA}}(r.asmGlobalArg,r.asmLibraryArg,M),K=(r._Encoder_Interface_Encode=N._Encoder_Interface_Encode,r._free=N._free,r._memmove=N._memmove),x=(r._Decoder_Interface_exit=N._Decoder_Interface_exit,r._Encoder_Interface_init=N._Encoder_Interface_init,r._memset=N._memset),S=(r._malloc=N._malloc,r._memcpy=N._memcpy);return r._Decoder_Interface_Decode=N._Decoder_Interface_Decode,r._Decoder_Interface_init=N._Decoder_Interface_init,r._Encoder_Interface_exit=N._Encoder_Interface_exit,r.___errno_location=N.___errno_location,r._main(),i.Create=e,i}A.AMR=e()}(("object"==typeof window&&window.document?window:Object).Recorder),function(){var A="object"==typeof window&&!!window.document,e=(A?window:Object).Recorder,f=e.i18n;!function(A,e,f){"use strict";A.prototype.enc_wav={stable:!0,fast:!0,getTestMsg:function(){return f("gPSE::支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）")}};var i=function(e){var i=e.bitRate,r=8==i?8:16;i!=r&&A.CLog(f("wyw9::WAV Info: 不支持{1}位，已更新成{2}位",0,i,r),3),e.bitRate=r};A.prototype.wav=function(e,f,r){var n=this.set;i(n);var t=e.length,w=n.sampleRate,l=n.bitRate,B=t*(l/8),a=A.wav_header(1,1,w,l,B),s=a.length,o=new Uint8Array(s+B);if(o.set(a),8==l)for(var Q=0;Q<t;Q++){var g=128+(e[Q]>>8);o[s++]=g}else(o=new Int16Array(o.buffer)).set(e,s/2);f(o.buffer,"audio/wav")},A.wav_header=function(A,e,f,i,r){var n=1==A?0:2,t=new ArrayBuffer(44+n),w=new DataView(t),l=0,B=function(A){for(var e=0;e<A.length;e++,l++)w.setUint8(l,A.charCodeAt(e))},a=function(A){w.setUint16(l,A,!0),l+=2},s=function(A){w.setUint32(l,A,!0),l+=4};return B("RIFF"),s(36+n+r),B("WAVE"),B("fmt "),s(16+n),a(A),a(e),s(f),s(f*(e*i/8)),a(e*i/8),a(i),1!=A&&a(0),B("data"),s(r),new Uint8Array(t)}}(e,0,f.$T)}();