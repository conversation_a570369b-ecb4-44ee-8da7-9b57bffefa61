/*
錄音
https://github.com/xiangyuecn/Recorder
src: extensions/frequency.histogram.view.js
*/
!function(){var e="object"==typeof window&&!!window.document,r=(e?window:Object).Recorder,t=r.i18n;!function(e,r,t,a){"use strict";var i=function(e){return new l(e)},o="FrequencyHistogramView",l=function(r){var i=this,l={scale:2,fps:20,lineCount:30,widthRatio:.6,spaceWidth:0,minHeight:0,position:-1,mirrorEnable:!1,stripeEnable:!0,stripeHeight:3,stripeMargin:6,fallDuration:1e3,stripeFallDuration:3500,linear:[0,"rgba(0,187,17,1)",.5,"rgba(255,215,0,1)",1,"rgba(255,102,0,1)"],stripeLinear:null,shadowBlur:0,shadowColor:"#bbb",stripeShadowBlur:-1,stripeShadowColor:"",fullFreq:!1,onDraw:function(e,r){}};for(var n in r)l[n]=r[n];i.set=r=l;var s="compatibleCanvas";if(r[s])var h=i.canvas=r[s];else{if(!a)throw new Error(t.G("NonBrowser-1",[o]));var f=r.elem;f&&("string"==typeof f?f=document.querySelector(f):f.length&&(f=f[0])),f&&(r.width=f.offsetWidth,r.height=f.offsetHeight);var c=i.elem=document.createElement("div");c.style.fontSize=0,c.innerHTML='<canvas style="width:100%;height:100%;"/>';h=i.canvas=c.querySelector("canvas");f&&(f.innerHTML="",f.appendChild(c))}var p=r.scale,d=r.width*p,u=r.height*p;if(!d||!u)throw new Error(t.G("IllegalArgs-1",[o+" width=0 height=0"]));h.width=d,h.height=u;i.ctx=h.getContext("2d");if(!e.LibFFT)throw new Error(t.G("NeedImport-2",[o,"src/extensions/lib.fft.js"]));i.fft=e.LibFFT(1024),i.lastH=[],i.stripesH=[]};l.prototype=i.prototype={genLinear:function(e,r,t,a){for(var i=e.createLinearGradient(0,t,0,a),o=0;o<r.length;)i.addColorStop(r[o++],r[o++]);return i},input:function(e,r,t){var a=this;a.sampleRate=t,a.pcmData=e,a.pcmPos=0,a.inputTime=Date.now(),a.schedule()},schedule:function(){var e=this,r=e.set,t=Math.floor(1e3/r.fps);e.timer||(e.timer=setInterval(function(){e.schedule()},t));var a=Date.now(),i=e.drawTime||0;if(a-e.inputTime>1.3*r.stripeFallDuration)return clearInterval(e.timer),e.timer=0,e.lastH=[],e.stripesH=[],void e.draw(null,e.sampleRate);if(!(a-i<t)){e.drawTime=a;for(var o=e.fft.bufferSize,l=e.pcmData,n=e.pcmPos,s=new Int16Array(o),h=0;h<o&&n<l.length;h++,n++)s[h]=l[n];e.pcmPos=n;var f=e.fft.transform(s);e.draw(f,e.sampleRate)}},draw:function(e,r){var t=this,a=t.set,i=t.ctx,o=a.scale,l=a.width*o,n=a.height*o,s=a.lineCount,h=t.fft.bufferSize,f=a.position,c=Math.abs(a.position),p=1==f?0:n,d=n;c<1&&(p=d/=2,d=Math.floor(d*(1+c)),p=Math.floor(f>0?p*(1-c):p*(1+c)));var u=t.lastH,w=t.stripesH,v=Math.ceil(d/(a.fallDuration/(1e3/a.fps))),g=Math.ceil(d/(a.stripeFallDuration/(1e3/a.fps))),m=a.stripeMargin*o,M=1<<(Math.round(Math.log(h)/Math.log(2)+3)<<1),b=Math.log(M)/Math.log(10),L=20*Math.log(32767)/Math.log(10),y=h/2,H=y;a.fullFreq||(H=Math.min(y,Math.floor(5e3*y/(r/2))));for(var S=H==y,C=S?s:Math.round(.8*s),D=H/C,R=S?0:(y-H)/(s-C),F=0,T=0;T<s;T++){var B=Math.ceil(F);F+=T<C?D:R;var E=Math.ceil(F);E==B&&E++,E=Math.min(E,y);var x=0;if(e)for(var q=B;q<E;q++)x=Math.max(x,Math.abs(e[q]));var I=x>M?Math.floor(17*(Math.log(x)/Math.log(10)-b)):0,G=d*Math.min(I/L,1);u[T]=(u[T]||0)-v,G<u[T]&&(G=u[T]),G<0&&(G=0),u[T]=G;var j=w[T]||0;if(G&&G+m>j)w[T]=G+m;else{var z=j-g;z<0&&(z=0),w[T]=z}}i.clearRect(0,0,l,n);var P=t.genLinear(i,a.linear,p,p-d),W=a.stripeLinear&&t.genLinear(i,a.stripeLinear,p,p-d)||P,k=t.genLinear(i,a.linear,p,p+d),A=a.stripeLinear&&t.genLinear(i,a.stripeLinear,p,p+d)||k,N=a.mirrorEnable,O=N?2*s-1:s,V=a.widthRatio,$=a.spaceWidth*o;0!=$&&(V=(l-$*(O+1))/l);for(T=0;T<2;T++){var J=Math.max(1*o,l*V/O),K=Math.floor(J),Q=J-K,U=(l-O*J)/(O+1);if(!(U>0&&U<1))break;V=1,U=0}for(var X=a.minHeight*o,Y=N?(l-K)/2-U:0,Z=0;Z<2;Z++){Z&&(i.save(),i.scale(-1,1));var _=Z?l:0;i.shadowBlur=a.shadowBlur*o,i.shadowColor=a.shadowColor;T=0;for(var ee=Y,re=0;T<s;T++)ee+=U,ae=Math.floor(ee)-_,oe=K,(re+=Q)>=1&&(oe++,re--),G=Math.max(u[T],X),0!=p&&(ie=p-G,i.fillStyle=P,i.fillRect(ae,ie,oe,G)),p!=n&&(i.fillStyle=k,i.fillRect(ae,p,oe,G)),ee+=oe;if(a.stripeEnable){var te=a.stripeShadowBlur;i.shadowBlur=(-1==te?a.shadowBlur:te)*o,i.shadowColor=a.stripeShadowColor||a.shadowColor;var ae,ie,oe,le=a.stripeHeight*o;for(T=0,ee=Y,re=0;T<s;T++)ee+=U,ae=Math.floor(ee)-_,oe=K,(re+=Q)>=1&&(oe++,re--),G=w[T],0!=p&&((ie=p-G-le)<0&&(ie=0),i.fillStyle=W,i.fillRect(ae,ie,oe,le)),p!=n&&((ie=p+G)+le>n&&(ie=n-le),i.fillStyle=A,i.fillRect(ae,ie,oe,le)),ee+=oe}if(Z&&i.restore(),!N)break}e&&a.onDraw(e,r)}},e[o]=i}(r,0,t.$T,e)}();