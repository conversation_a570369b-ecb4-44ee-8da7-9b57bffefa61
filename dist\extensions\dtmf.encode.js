/*
錄音
https://github.com/xiangyuecn/Recorder
src: extensions/dtmf.encode.js
*/
!function(){var t="object"==typeof window&&!!window.document,e=(t?window:Object).Recorder,n=e.i18n;!function(t){"use strict";t.DTMF_Encode=function(t,e,i,a){for(var o=Math.floor(e*(i||100)/1e3),s=Math.floor(e*(null==a?50:a)/1e3),u=new Int16Array(o+2*s),c=new Int16Array(o+2*s),d=r[t][0],h=r[t][1],f=.3,k=0;k<o;k++){var l=f*Math.sin(2*Math.PI*d*(k/e)),x=f*Math.sin(2*Math.PI*h*(k/e));u[k+s]=32767*Math.max(-1,Math.min(1,l)),c[k+s]=32767*Math.max(-1,Math.min(1,x))}return n(u,0,c,0),u},t.DTMF_EncodeMix=function(t){return new e(t)};var e=function(t){var e=this;for(var n in e.set={duration:100,mute:25,interval:200},t)e.set[n]=t[n];e.keys="",e.idx=0,e.state={keyIdx:-1,skip:0}};e.prototype={add:function(t){this.keys+=t},mix:function(e,r,i){i||(i=0);var a=this,o=a.set,s=[],u=a.state,c=0;t:for(var d=i;d<e.length;d++){var h=e[d],f=a.keys.charAt(a.idx);if(f)for(;f;){if(u.skip){var k=h.length-c;if(k<=u.skip){u.skip-=k,c=0;continue t}c+=u.skip,u.skip=0}var l=u.keyPcm;u.keyIdx==a.idx&&u.cur>=l.length&&(u.keyIdx=-1),u.keyIdx!=a.idx&&(l=t.DTMF_Encode(f,r,o.duration,o.mute),u.keyIdx=a.idx,u.cur=0,u.keyPcm=l,s.push({key:f,data:l}));var x=n(h,c,l,u.cur,!0);if(u.cur=x.cur,c=x.last,x.cur>=l.length&&(a.idx++,f=a.keys.charAt(a.idx),u.skip=Math.floor(r*(o.interval-o.duration-2*o.mute)/1e3)),x.last>=h.length){c=0;continue t}}else u.skip=Math.max(0,u.skip-h.length)}return{newEncodes:s,hasNext:a.idx<a.keys.length}}};var n=function(t,e,n,r,i){for(var a=e,o=r;;a++,o++){if(a>=t.length||o>=n.length)return{last:a,cur:o};i&&(t[a]=0);var s,u=t[a],c=n[o];s=u<0&&c<0?u+c-u*c/-32767:u+c-u*c/32767,t[a]=s}},r={1:[697,1209],2:[697,1336],3:[697,1477],A:[697,1633],4:[770,1209],5:[770,1336],6:[770,1477],B:[770,1633],7:[852,1209],8:[852,1336],9:[852,1477],C:[852,1633],"*":[941,1209],0:[941,1336],"#":[941,1477],D:[941,1633]}}(e,0,n.$T)}();