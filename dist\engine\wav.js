/*
錄音
https://github.com/xiangyuecn/Recorder
src: engine/wav.js
*/
!function(){var t="object"==typeof window&&!!window.document,e=(t?window:Object).Recorder,n=e.i18n;!function(t,e,n){"use strict";t.prototype.enc_wav={stable:!0,fast:!0,getTestMsg:function(){return n("gPSE::支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）")}};var a=function(e){var a=e.bitRate,r=8==a?8:16;a!=r&&t.CLog(n("wyw9::WAV Info: 不支持{1}位，已更新成{2}位",0,a,r),3),e.bitRate=r};t.prototype.wav=function(e,n,r){var i=this.set;a(i);var o=e.length,f=i.sampleRate,w=i.bitRate,c=o*(w/8),u=t.wav_header(1,1,f,w,c),v=u.length,s=new Uint8Array(v+c);if(s.set(u),8==w)for(var d=0;d<o;d++){var b=128+(e[d]>>8);s[v++]=b}else(s=new Int16Array(s.buffer)).set(e,v/2);n(s.buffer,"audio/wav")},t.wav_header=function(t,e,n,a,r){var i=1==t?0:2,o=new ArrayBuffer(44+i),f=new DataView(o),w=0,c=function(t){for(var e=0;e<t.length;e++,w++)f.setUint8(w,t.charCodeAt(e))},u=function(t){f.setUint16(w,t,!0),w+=2},v=function(t){f.setUint32(w,t,!0),w+=4};return c("RIFF"),v(36+i+r),c("WAVE"),c("fmt "),v(16+i),u(t),u(e),v(n),v(n*(e*a/8)),u(e*a/8),u(a),1!=t&&u(0),c("data"),v(r),new Uint8Array(o)}}(e,0,n.$T)}();