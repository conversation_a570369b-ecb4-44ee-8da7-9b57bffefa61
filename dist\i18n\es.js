/*
錄音
https://github.com/xiangyuecn/Recorder
src: i18n/es.js
*/
!function(){var e="object"==typeof window&&!!window.document;!function(e){"use strict";var a=e.i18n;e.CLog('Import Recorder i18n lang="es"');var o={lang:"es"};a.data.rtl$es=!1,a.data.desc$es="Spanish, Español, 西班牙语。Esta traducción proviene principalmente de: traducción de google + traducción de Baidu, traducida del chino al español. 此翻译主要来自：google翻译+百度翻译，由中文翻译成西班牙语。",a.put(o,["K8zP:Importación duplicada {1}","mSxV:Los {1} GetContext restantes no han sido close","nMIy: (Nota: ctx no está en estado running. Se debe llamar al menos a uno de rec.open y start durante la operación del usuario (tocar, hacer clic, etc.); de lo contrario, se intentará ctx.resume durante rec.start, lo que puede causar compatibilidad problemas (solo iOS), consulte la configuración de runningContext en la documentación) ","eS8i:El sampleRate {1} de la transmisión no es igual a {2} y se realizará la conversión de sampleRate (nota: la calidad del sonido no mejorará o incluso puede empeorar). Este fenómeno ocurre principalmente cuando echoCancellation no está desactivado en el terminal móvil. Cuando el navegador tiene echoCancellation, es posible que solo se devuelvan datos de audio con una frecuencia de muestreo de 16k. ","ZGlf:. Debido a las 375 devoluciones de llamadas por segundo dentro de {1}, puede haber problemas de rendimiento en el lado móvil que pueden provocar que se pierdan las devoluciones de llamadas y que la grabación sea más corta. No hay ningún impacto en el lado de la PC. No se recomienda habilitar {1} por el momento.","7TU0:Connect utiliza el antiguo {1}, ","JwCL:Pero {1} está configurado para intentar habilitar {2}","VGjB:Puedes configurar {1} para intentar habilitar {2}","MxX1:{1} no devolvió ningún audio, continúe usando {2}","XUap:{1} devolución de llamada redundante","yOta:Connect usa {1}, configurar {2} puede restaurar el antiguo {3}","VwPd: (Este navegador no soporta {1}) ","vHnb:{1} no devuelve audio, se ha degradado para usar {2}","O9P7:{1} devolución de llamada redundante","LMEm:Connect usa {1}, configure {2} para volver a {3} o al antiguo {4}","d48C:La frecuencia de muestreo del filtro de {1} ha cambiado y el filtro se ha restablecido","tlbC:{1} parece haber introducido chunk {2} sin restablecer","VtS4:{1} y {2} deben ser valores numéricos","5tWi:Error al grabar open: ","dFm8:open fue cancelado","VtJO:open fue interrumpido","EMJq:, puedes probar la solución RecordApp","A5bm:No se puede grabar: ","1iU7:Este navegador no admite la recuperación de grabaciones de transmisiones","BTW2:No se pudo abrir la grabación desde la transmisión: ","Nclz:Sin permiso para grabar (entre dominios, intente agregar una política de acceso al micrófono al iframe, como {1})","jBa9:, no hay micrófono disponible","gyO5:Usuario denegado permiso de grabación","oWNo:El navegador prohíbe el registro de páginas no seguras, lo que se puede solucionar activando https","COxc:Este navegador no admite la grabación","upb8:Descubrí que se llamó a open varias veces al mismo tiempo","Q1GA:La función de grabación no funciona: no hay transmisión de audio","KxE2:, intentaré deshabilitar echoCancellation y volveré a intentarlo","xEQR:Error al solicitar permiso de grabación","bDOG:No se puede grabar: ","IjL3:Nota: Se ha configurado el parámetro {1}, lo que puede provocar que el navegador no seleccione correctamente el micrófono, o que el terminal móvil no habilite echoCancellation, etc. ","RiWe:, cuando no se configura {1}, el navegador puede habilitar automáticamente la cancelación de eco. Cuando la cancelación de eco no está deshabilitada en el terminal móvil, el volumen de reproducción del sistema puede reducirse (se puede restaurar después de cerrar la grabación) y solo se proporciona una transmisión de audio con una frecuencia de muestreo de 16 k (cuando no se requiere la cancelación de eco, se puede configurar explícitamente para que se deshabilite y se obtenga una transmisión de alta calidad de 48 k). Consulte la configuración {2} en el documento","hWVz:close se ignora (debido a que se abren varios recs al mismo tiempo, solo el último será realmente close)","UHvm:descuido","Essp:No es compatible con la arquitectura {1}","2XBl:El tipo {1} no admite la configuración de takeoffEncodeChunk","LG7e:(sin codificador cargado)","7uMV:El entorno {1} no admite el procesamiento en tiempo real","4Kfd:Compensación {1}ms","bM5i:{1} ms sin compensar","gFUF:No se permiten errores en las devoluciones de llamada y se debe garantizar que no se produzcan excepciones","2ghS:Bajo rendimiento, tarda {1} ms","ufqH:Los buffers no se pueden borrar antes de entrar asíncrono","6WmN:start falló: no open","kLDN:start, comenzar a grabar, ","Bp2y:start fue interrumpido","upkE:, es posible que no sea posible grabar: ","Xq4s:stop, diferencia horaria con start: ","3CQP:compensar: ","u8JG:No se pudo finalizar la grabación: ","1skY:, por favor establece {1}","Wv7l:Finalizar la grabación. La codificación tarda {1} ms. La duración del audio es de {2} ms. El tamaño del archivo es {3}b","Vkbd:El codificador {1} no devuelve {2}","QWnr:Después de habilitar takeoffEncodeChunk, la longitud del blob devuelta por stop es 0 y no se proporcionan datos de audio","Sz2H:El {1} generado no es válido","wf9t:Grabación no iniciada","Dl2c:, no hay interacción del usuario antes de comenzar a grabar, lo que hace que AudioContext no se ejecute","Ltz3:No se recopiló ninguna grabación","xGuI:El codificador de {1} no está cargado. Intente encontrar el codificador de {1} en src/engine de {2} y cárguelo","AxOH:Error de grabación: ","xkKd:Se liberan los buffers de audio","CxeT:Muestra: {1} Flor: {2}ms","NonBrowser-1:Entorno sin navegador, no es compatible con {1}","IllegalArgs-1:Error de parámetro: {1}","NeedImport-2:Para llamar a {1}, primero debes importar {2}","NotSupport-1:No compatible: {1}","8HO5:Anular importación {1}"]),a.put(o,["b2mN:AMR-NB (NarrowBand), la configuración sampleRate no es válida (solo se proporcionan 8000 hz), rango bitRate: {1} (predeterminado 12.2 kbps), un cuadro de 20 ms, {2} bytes; los navegadores generalmente no admiten la reproducción en formato amr, disponible Recorder.amr2wav() Transcodifica a wav para reproducción","tQBv:AMR Info: no coincide con el conjunto {1}, se ha actualizado a {2}","q12D:Los datos sampleRate están por debajo de {1}","TxjV:La versión actual del navegador es demasiado baja y no se puede procesar en tiempo real","Q7p7:takeoffEncodeChunk se hace cargo de la salida de datos binarios del codificador AMR. Solo los primeros datos de devolución de llamada (primer cuadro) contienen el encabezado AMR; al fusionarlos en un archivo AMR, si los datos del primer cuadro no están incluidos, el encabezado AMR debe agregarse en el comienzo del archivo: Recorder.AMR.AMR_HEADER (convertido a binario), de lo contrario no se puede reproducir","6o9Z:El entorno actual no es compatible con Web Worker y el codificador en tiempo real amr se ejecuta en el hilo principal","yYWs:a amr worker le quedan {1} no stop","jOi8:codificador amr no start"]),a.put(o,["O8Gn:Ogg Vorbis, el valor de bitRate es de 16-100 kbps, el valor de sampleRate es ilimitado","5si6:La versión actual del navegador es demasiado baja y no se puede procesar en tiempo real","R8yz:takeoffEncodeChunk se hace cargo de la salida de datos binarios del codificador OggVorbis. Ogg se compone de páginas de datos. Una página contiene múltiples fotogramas de datos de audio (incluidos varios segundos de audio. Una página de datos no se puede decodificar ni reproducir por separado). Cada salida de este codificador está completo. Una página de datos, por lo que el rendimiento en tiempo real será relativamente bajo; al fusionar en un archivo ogg completo, todos los datos de salida deben fusionarse; de ​​lo contrario, es posible que no se reproduzca y no se admita intercepte la parte media y decodifíquela y reprodúzcala por separado","hB9D:El entorno actual no admite Web Workers y el codificador en tiempo real OggVorbis se ejecuta en el hilo principal","oTiy:a ogg worker le quedan {1} no stop","dIpw:codificador ogg no start"]),a.put(o,["L49q:Este navegador no admite la codificación webm y MediaRecorder no está implementado","tsTW:Sólo los navegadores más nuevos lo admiten y la tasa de compresión es similar a la de mp3. Dado que no hay forma de codificar rápidamente los datos pcm existentes, los datos sólo se pueden importar a MediaRecorder de forma similar a la reproducción y escucha, y hay que esperar unos segundos. Aunque el tamaño del archivo de audio de salida se puede controlar mediante la velocidad de bits, la velocidad de bits en el archivo de audio no es la velocidad de bits establecida. Dado que la frecuencia de muestreo la probamos nosotros mismos, podemos hacer lo que queramos con este codificador","aG4z:Este navegador no admite la conversión de grabaciones al formato webm","PIX0:Error al transcodificar webm: {1}"]),a.put(o,["d8YX:{1}; {2} El archivo de audio no se puede reproducir directamente. Puede utilizar Recorder.{2}2wav() para transcodificarlo a wav para su reproducción; la configuración de velocidad de bits de frecuencia de muestreo no es válida y está fijada en 8000 hz de muestreo velocidad, 16 bits, y cada muestra está comprimida. en un almacenamiento de 8 bits, el tamaño del archivo de audio es 8000 bytes/segundo; si necesita compatibilidad con cualquier frecuencia de muestreo, utilice el método Recorder.{2}_encode()","29UK:Los datos sampleRate están por debajo de {1}","quVJ:codificador {1} no start"]),a.put(o,["Zm7L:rango sampleRate: {1}; rango bitRate: {2} (diferentes bitRate admiten diferentes rangos sampleRate. Cuando es inferior a 32 kbps, sampleRate debe ser inferior a 32000)","eGB9:{1} no está en el rango de valores soportado por mp3: {2}","zLTa:sampleRate se ha actualizado a {1} porque {2} no está en el rango de valores admitido por mp3: {3}","yhUs:La versión actual del navegador es demasiado baja y no se puede procesar en tiempo real","k9PT:El entorno actual no es compatible con Web Worker y el codificador en tiempo real mp3 se ejecuta en el hilo principal","fT6M:a mp3 worker le quedan {1} no stop","mPxH:codificador mp3 no start","uY9i:No coincide con la configuración {1}, se ha actualizado a {2}","iMSm:Fix elimina {1} fotogramas","b9zm:Eliminar demasiados fotogramas"]),a.put(o,["fWsN:pcm son datos de audio originales no encapsulados. Los archivos de audio Pcm no se pueden reproducir directamente. Recorder.pcm2wav() se puede utilizar para transcodificar a wav para su reproducción. Admite dígitos de 8 y 16 bits (rellene bitRate) y el valor de sampleRate es ilimitado","uMUJ:PCM Info: El bit {1} no es compatible y se ha actualizado al bit {2}","KmRz:pcm2wav debe proporcionar sampleRate y bitRate","sDkA:codificador pcm no start"]),a.put(o,["gPSE:Admite dígitos de 8 y 16 bits (completados en bitRate) y el valor de sampleRate es ilimitado; este codificador solo agrega un encabezado wav de 44 bytes antes de los datos pcm, y el archivo wav codificado de 16 bits elimina los 44 bits iniciales. Bytes para obtener pcm (nota: es posible que otros codificadores WAV no tengan 44 bytes)","wyw9:WAV Info: El bit {1} no es compatible y se ha actualizado al bit {2}"]),a.put(o,["0XYC:El método getAudioSrc está obsoleto: utilice getMediaStream directamente y asígnelo a audio.srcObject. Solo se permite llamar a este método en navegadores que no admiten srcObject y asignarlo a audio.src por compatibilidad","6DDt:start es cancelado por stop","I4h4:{1} se repite start","P6Gs:El navegador no admite la apertura de {1}","JwDm: (Nota: ctx no está en estado running. Es necesario llamar a start cuando el usuario opera (tocar, hacer clic, etc.); de lo contrario, se intentará ctx.resume, lo que puede causar problemas de compatibilidad (solo iOS). Consulte la configuración runningContext en el documento) ","qx6X:La implementación AudioBuffer de este navegador no admite funciones dinámicas y utiliza el modo de compatibilidad","cdOx:Tiempo de espera de detección del entorno","S2Bu:No puede jugar: {1}","ZfGG:Falló la llamada input: no PCM [int16,...] al ingresar, se debe decodificar o usar la conversión transform","N4ke:Falló la llamada input: no se proporcionó sampleRate","IHZd:Falló la llamada a input: sampleRate={1} de los datos es diferente de la anterior ={2}","L8sC:El retraso es demasiado grande, se han descartado {1}ms, {2}","TZPq:{1} no se llama al método start","iCFC:El navegador no admite decodificación de audio","wE2k:Los datos de decodificación de audio deben ser ArrayBuffer","mOaT:Falló la decodificación de audio: {1}"]),a.put(o,["3RBa:Símbolo [{1}] no válido: {2}","U212:Nota [{1}] no válido: {2}","7qAD:Hay que alinearse a la hora de múltiples sonidos, con diferencias {1}ms","QGsW:Happy Birthday to You","emJR:For Elise","GsYy:Canon - símbolo de la mano derecha","bSFZ:Canon"]),a.put(o,["Ikdz:El entorno actual no admite Web Worker, no admite llamadas a Sonic.Async","IC5Y:sonic worker deja {1} sin flush"]),a.put(o,["WWoj:El método {2} en {1} no se implementa, por favor implemente este método en el archivo {3} o en el archivo de configuración","rCAM:No se inició la grabación, pero se recibieron los datos de Native PCM","t2OF:Detectado iframe entre dominios, NativeRecordReceivePCM no se puede inyectar en el nivel superior, se han monitoreado los datos de transmisión compatibles con el reenvío de postMessage, por favor, realice por sí mismo el reenvío de los datos recibidos en la capa superior a este iframe (capa ilimitada), de lo contrario no se pueden recibir los datos de grabación","Z2y2:Grabación no iniciada"]),a.put(o,["uXtA:Importación duplicada {1}","kIBu:Nota: Debido a que se llaman simultáneamente otros métodos relacionados con la grabación, el resultado de la llamada actual de {1} se ha descartado y no habrá devolución de llamada","ha2K:Doble registro {1}","wpTL:Solo limpiar recursos","bpvP:Grabación no iniciada","fLJD:El entorno actual no admite devoluciones de llamada en tiempo real y no se puede realizar {1}","YnzX:La solicitud de permiso de grabación falló: ","nwKR:Primero hay que llamar a {1}","citA:Actualmente no es un entorno de navegador, es necesario introducir un archivo de soporte para esta plataforma ({1}), o llamar al {2} para lograr su propio acceso.","ecp9:Falló al iniciar la grabación: ","EKmS:No se puede grabar: ","k7Qo:Se ha iniciado la grabación","Douz:Falló al finalizar la grabación: ","wqSH:Diferencia horaria con start: {1}ms","g3VX:Terminar la grabación lleva tiempo {1}ms , duración del audio {2}ms , tamaño del archivo {3}b , {4}"])}((e?window:Object).Recorder)}();