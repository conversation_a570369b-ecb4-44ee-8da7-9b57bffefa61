/*
錄音
https://github.com/xiangyuecn/Recorder
src: app-support/app-native-support.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,r=t.i18n;!function(e,t,r,n){"use strict";var a=e.RecordApp,i=a.CLog,o={Support:function(e){a.AlwaysAppUseH5?e(!1):s.<PERSON>pp(e)},CanProcess:function(){return!0},Config:{IsApp:function(e){c("IsApp"),e(!1)},JsBridgeRequestPermission:function(e,t){t(c("JsBridgeRequestPermission"))},JsBridgeStart:function(e,t,r){r(c("JsBridgeStart"))},JsBridgeStop:function(e,t){t(c("JsBridgeStop"))}}};a.<PERSON>latform("Native",o);var s=o.Config,c=function(e){var t=r("WWoj::{1}中的{2}方法未实现，请在{3}文件中或配置文件中实现此方法",0,"RecordApp.Platforms.Native.Config",e,"app-native-support.js");return i(t,3),t},f=function(e,t){var n=f.rec;if(n){n._appStart||n.envStart({envName:o.Key,canProcess:o.CanProcess()},t),n._appStart=1;var a=0;if(e instanceof Int16Array)for(var s=new Int16Array(e),c=0;c<s.length;c++)a+=Math.abs(s[c]);else{var p,u=atob(e),v=u.length,d=(s=new Int16Array(v/2),0);for(c=0;c+2<=v;d++,c+=2)p=(u.charCodeAt(c)|u.charCodeAt(c+1)<<8)<<16>>16,s[d]=p,a+=Math.abs(p)}n.envIn(s,a)}else i(r("rCAM::未开始录音，但收到Native PCM数据"),3)};n||(a.NativeRecordReceivePCM=f);if(n){window.NativeRecordReceivePCM=f;try{window.top.NativeRecordReceivePCM=f}catch(e){var p=function(){i(r("t2OF::检测到跨域iframe，NativeRecordReceivePCM无法注入到顶层，已监听postMessage转发兼容传输数据，请自行实现将top层接收到数据转发到本iframe（不限层），不然无法接收到录音数据"),3)};setTimeout(p,8e3),p(),addEventListener("message",function(e){var t=e.data;t&&"NativeRecordReceivePCM"==t.type&&(t=t.data,f(t.pcmDataBase64,t.sampleRate))})}}o.RequestPermission=function(e,t,r){s.JsBridgeRequestPermission(t,r)},o.Start=function(t,r,n,i){f.param=r;var o=e(r);o.set.disableEnvInFix=!0,o.dataType="arraybuffer",f.rec=o,a.__Rec=o,s.JsBridgeStart(r,n,i)},o.Stop=function(e,t,n){var o=function(t){a.__Sync(e)&&(f.rec=null),n(t)};s.JsBridgeStop(function(){if(a.__Sync(e)){var n=f.rec;f.rec=null;var s=t?"":a.__StopOnlyClearMsg();if(n){i("rec encode: pcm:"+n.recSize+" srcSR:"+n.srcSampleRate+" set:"+JSON.stringify(f.param));var c=function(){if(a.__Sync(e))for(var t in n.set)f.param[t]=n.set[t]};if(!t)return c(),void o(s);n.stop(function(e,r,n){c(),t(e,r,n)},function(e){c(),o(e)})}else o(r("Z2y2::未开始录音")+(s?" ("+s+")":""))}else o("Incorrect sync status")},o)}}(t,0,r.$T,e)}();