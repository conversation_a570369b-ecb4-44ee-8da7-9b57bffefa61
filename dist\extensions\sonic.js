/*
錄音
https://github.com/xiangyuecn/Recorder
src: extensions/sonic.js
*/
!function(){var t="object"==typeof window&&!!window.document,r=(t?window:Object).Recorder,o=r.i18n;!function(t,r,o,n){"use strict";var e,i=n&&"function"==typeof Worker;function a(t){var r=function(t){this.set=t;var r=n(this);this.sonic=r,r.New(t.sampleRate,1)};r.prototype=a.prototype={input:function(t){return this.sonic.writeShortToStream(t),this.sonic.readShortFromStream()},flush:function(){return this.sonic.flushStream(),this.sonic.readShortFromStream()}};var o={arraycopy:function(t,r,o,n,e){for(var i=0;i<e;i++)o[n+i]=t[r+i]}};function n(t){var r,n,e,i,a=65,c=400,f=4e3,s=12,u=601,l=[0,0,0,0,0,0,0,-1,-1,-2,-2,-3,-4,-6,-7,-9,-10,-12,-14,-17,-19,-21,-24,-26,-29,-32,-34,-37,-40,-42,-44,-47,-48,-50,-51,-52,-53,-53,-53,-52,-50,-48,-46,-43,-39,-34,-29,-22,-16,-8,0,9,19,29,41,53,65,79,92,107,121,137,152,168,184,200,215,231,247,262,276,291,304,317,328,339,348,357,363,369,372,374,375,373,369,363,355,345,332,318,300,281,259,234,208,178,147,113,77,39,0,-41,-85,-130,-177,-225,-274,-324,-375,-426,-478,-530,-581,-632,-682,-731,-779,-825,-870,-912,-951,-989,-1023,-1053,-1080,-1104,-1123,-1138,-1149,-1154,-1155,-1151,-1141,-1125,-1105,-1078,-1046,-1007,-963,-913,-857,-796,-728,-655,-576,-492,-403,-309,-210,-107,0,111,225,342,462,584,708,833,958,1084,1209,1333,1455,1575,1693,1807,1916,2022,2122,2216,2304,2384,2457,2522,2579,2625,2663,2689,2706,2711,2705,2687,2657,2614,2559,2491,2411,2317,2211,2092,1960,1815,1658,1489,1308,1115,912,698,474,241,0,-249,-506,-769,-1037,-1310,-1586,-1864,-2144,-2424,-2703,-2980,-3254,-3523,-3787,-4043,-4291,-4529,-4757,-4972,-5174,-5360,-5531,-5685,-5819,-5935,-6029,-6101,-6150,-6175,-6175,-6149,-6096,-6015,-5905,-5767,-5599,-5401,-5172,-4912,-4621,-4298,-3944,-3558,-3141,-2693,-2214,-1705,-1166,-597,0,625,1277,1955,2658,3386,4135,4906,5697,6506,7332,8173,9027,9893,10769,11654,12544,13439,14335,15232,16128,17019,17904,18782,19649,20504,21345,22170,22977,23763,24527,25268,25982,26669,27327,27953,28547,29107,29632,30119,30569,30979,31349,31678,31964,32208,32408,32565,32677,32744,32767,32744,32677,32565,32408,32208,31964,31678,31349,30979,30569,30119,29632,29107,28547,27953,27327,26669,25982,25268,24527,23763,22977,22170,21345,20504,19649,18782,17904,17019,16128,15232,14335,13439,12544,11654,10769,9893,9027,8173,7332,6506,5697,4906,4135,3386,2658,1955,1277,625,0,-597,-1166,-1705,-2214,-2693,-3141,-3558,-3944,-4298,-4621,-4912,-5172,-5401,-5599,-5767,-5905,-6015,-6096,-6149,-6175,-6175,-6150,-6101,-6029,-5935,-5819,-5685,-5531,-5360,-5174,-4972,-4757,-4529,-4291,-4043,-3787,-3523,-3254,-2980,-2703,-2424,-2144,-1864,-1586,-1310,-1037,-769,-506,-249,0,241,474,698,912,1115,1308,1489,1658,1815,1960,2092,2211,2317,2411,2491,2559,2614,2657,2687,2705,2711,2706,2689,2663,2625,2579,2522,2457,2384,2304,2216,2122,2022,1916,1807,1693,1575,1455,1333,1209,1084,958,833,708,584,462,342,225,111,0,-107,-210,-309,-403,-492,-576,-655,-728,-796,-857,-913,-963,-1007,-1046,-1078,-1105,-1125,-1141,-1151,-1155,-1154,-1149,-1138,-1123,-1104,-1080,-1053,-1023,-989,-951,-912,-870,-825,-779,-731,-682,-632,-581,-530,-478,-426,-375,-324,-274,-225,-177,-130,-85,-41,0,39,77,113,147,178,208,234,259,281,300,318,332,345,355,363,369,373,375,374,372,369,363,357,348,339,328,317,304,291,276,262,247,231,215,200,184,168,152,137,121,107,92,79,65,53,41,29,19,9,0,-8,-16,-22,-29,-34,-39,-43,-46,-48,-50,-52,-53,-53,-53,-52,-51,-50,-48,-47,-44,-42,-40,-37,-34,-32,-29,-26,-24,-21,-19,-17,-14,-12,-10,-9,-7,-6,-4,-3,-2,-2,-1,-1,0,0,0,0,0,0,0],h=0,v=0,w=0,d=0,p=0,m=0,M=!1,y=0,k=0,b=0,S=0,R=0,g=0,j=0,A=0,I=0,L=0,O=0,x=0,C=0,U=0,W=0,_=0,P=0;function T(t,r){r*=k;var n=new Int16Array(r),e=t.length<=r?t.length:r;return o.arraycopy(t,0,n,0,e),n}function F(t,r,n,e,i){o.arraycopy(n,e*k,t,r*k,i*k)}function B(t,r,o,n){for(var e=Math.floor(4096*n),i=r*k,a=i+o*k,c=i;c<a;c++){var f=t[c]*e>>12;f>32767?f=32767:f<-32767&&(f=-32767),t[c]=f}}function D(t){h=t}function N(t){w=t}function Q(t){d!=t&&(d=t,p=0,m=0)}function V(t){M=t}function z(t){y=t}function E(t){v=t}function Y(t,o){I=Math.floor(t/c),L=Math.floor(t/a),b=O=2*L,r=new Int16Array(O*o),R=O,n=new Int16Array(O*o),S=O,e=new Int16Array(O*o),i=new Int16Array(O),C=t,k=o,p=0,m=0,U=0}function $(t,r){Y(t,r),h=1,w=1,v=1,d=1,p=0,m=0,M=!1,y=0}function q(t){j+t>R&&(n=T(n,R+=(R>>1)+t))}function G(t){g+t>b&&(r=T(r,b+=(b>>1)+t))}function H(t,o){0!=o&&(G(o),F(r,g,t,0,o),g+=o)}function J(t){var o=g-t;F(r,0,r,t,o),g=o}function K(t,r,o){q(o),F(n,j,t,r,o),j+=o}function X(t){var o=x;return o>O&&(o=O),K(r,t,o),x-=o,o}function Z(){var t=j,r=new Int16Array(t),o=0;return 0==t||(F(r,0,n,0,t),F(n,0,n,t,o),j=o),r}function tt(){var t=g,o=h/w,n=d*w,e=Math.floor(j+Math.floor((t/o+A)/n+.5));G(t+2*O);for(var i=0;i<2*O*k;i++)r[t*k+i]=0;g+=2*O,Mt(null,0),j>e&&(j=e),g=0,x=0,A=0}function rt(t,r,o){var n,e=Math.floor(O/o),a=k*o;r*=k;for(var c=0;c<e;c++){n=0;for(var f=0;f<a;f++)n+=t[r+c*a+f];n=Math.floor(n/a),i[c]=n}}function ot(t,r,o,n){var e=0,i=255,a=1,c=0;r*=k;for(var f=o;f<=n;f++){for(var s=0,u=0;u<f;u++){var l=t[r+u],h=t[r+f+u];s+=l>=h?l-h:h-l}s*e<a*f&&(a=s,e=f),s*i>c*f&&(c=s,i=f)}return _=Math.floor(a/e),P=Math.floor(c/i),e}function nt(t,r,o){if(0==t||0==U)return!1;if(o){if(r>3*t)return!1;if(2*t<=3*W)return!1}else if(t<=W)return!1;return!0}function et(t,r,o){var n,e,a=1;if(C>f&&0==y&&(a=Math.floor(C/f)),1==k&&1==a)n=ot(t,r,I,L);else if(rt(t,r,a),n=ot(i,0,Math.floor(I/a),Math.floor(L/a)),1!=a){var c=(n*=a)-(a<<2),s=n+(a<<2);c<I&&(c=I),s>L&&(s=L),1==k?n=ot(t,r,c,s):(rt(t,r,1),n=ot(i,0,c,s))}return e=nt(_,P,o)?U:n,W=_,U=n,e}function it(t,r,o,n,e,i,a,c){for(var f=0;f<r;f++)for(var s=n*r+f,u=c*r+f,l=i*r+f,h=0;h<t;h++)o[s]=Math.floor((e[l]*(t-h)+a[u]*h)/t),s+=r,l+=r,u+=r}function at(t,r,o,n,e,i,a,c,f){for(var s=0;s<r;s++)for(var u=e*r+s,l=f*r+s,h=a*r+s,v=0;v<t+o;v++)v<o?(n[u]=Math.floor(i[h]*(t-v)/t),h+=r):v<t?(n[u]=Math.floor((i[h]*(t-v)+c[l]*(v-o))/t),h+=r,l+=r):(n[u]=Math.floor(c[l]*(v-o)/t),l+=r),u+=r}function ct(t){var r=j-t;A+r>S&&(e=T(e,S+=(S>>1)+r)),F(e,A,n,t,r),j=t,A+=r}function ft(t){0!=t&&(F(e,0,e,t,A-t),A-=t)}function st(t){var r,o,i=0;if(j!=t){for(ct(t);A-i>=O;)r=et(e,i,!1),q(o=Math.floor(r/w)),w>=1?it(o,k,n,j,e,i,e,i+r-o):at(r,k,o-r,n,j,e,i,e,i),j+=o,i+=r;ft(i)}}function ut(t,r,o){var n=Math.floor((u-1)/s),e=Math.floor(t*n+r*n/o),i=e+1,a=t*n*o+r*n-e*o,c=l[e],f=l[i];return Math.floor((c*(o-a)+f*a<<1)/o)}function lt(t){return t>=0?1:-1}function ht(t,r,o,n){var e,i,a,c,f=0,u=(p+1)*n,l=u-m*o-1,h=u-p*n,v=0;for(e=0;e<s;e++)i=ut(e,l,h),a=t[r+e*k]*i,(c=lt(f))!=lt(f+=a)&&lt(a)==c&&(v+=c);return v>0?32767:v<0?-32768:f>>16&65535}function vt(t,r){for(var o,i=Math.floor(C/t),a=C;i>16384||a>16384;)i>>=1,a>>=1;if(j!=r){for(ct(r),o=0;o<A-1;o++){for(;(p+1)*i>m*a;){q(1);for(var c=0;c<k;c++)n[j*k+c]=ht(e,o*k+c,a,i);m++,j++}if(++p==a){if(p=0,m!=i)throw new Error("Assertion failed: newRatePosition != newSampleRate\n");m=0}}ft(o)}}function wt(t,r,o,e){var i;return o>=2?i=Math.floor(e/(o-1)):(i=e,x=Math.floor(e*(2-o)/(o-1))),q(i),it(i,k,n,j,t,r,t,r+e),j+=i,i}function dt(t,r,o,e){var i;return o<.5?i=Math.floor(e*o/(1-o)):(i=e,x=Math.floor(e*(2*o-1)/(1-o))),q(e+i),F(n,j,t,r,e),it(i,k,n,j+e,t,r+e,t,r),j+=e+i,i}function pt(t){var o,n=g,e=0;if(!(g<O)){do{x>0?e+=X(e):(o=et(r,e,!0),e+=t>1?o+wt(r,e,t,o):dt(r,e,t,o))}while(e+O<=n);J(e)}}function mt(){var t=j,o=h/w,e=d;M||(e*=w),o>1.00001||o<.99999?pt(o):(K(r,0,g),g=0),M?1!=w&&st(t):1!=e&&vt(e,t),1!=v&&B(n,t,j-t,v)}function Mt(t){H(t,t?t.length:0),mt()}return t.setPitch=N,t.setRate=Q,t.setSpeed=D,t.setVolume=E,t.setChordPitch=V,t.setQuality=z,{New:$,flushStream:tt,writeShortToStream:Mt,readShortFromStream:Z}}return new r(t)}t.Sonic=a,t.BindDestroy("sonicWorker",function(){e&&(t.CLog("sonicWorker Destroy"),e&&e.terminate(),e=null)});var c={id:0};a.Async=function(r){if(!i)return t.CLog(o("Ikdz::当前环境不支持Web Worker，不支持调用Sonic.Async"),3),null;var n=e;try{if(!n){var a=");var wk_ctxs={};self.onmessage="+function(t){var r=t.data,o=wk_ctxs[r.id];if("init"==r.action)wk_ctxs[r.id]={sampleRate:r.sampleRate,sonicObj:wk_sonic({sampleRate:r.sampleRate})};else if(!o)return;switch(r.action){case"flush":var n=o.sonicObj.flush();self.postMessage({action:r.action,id:r.id,call:r.call,pcm:n}),o.sonicObj=null,delete wk_ctxs[r.id];break;case"input":n=o.sonicObj.input(r.pcm);self.postMessage({action:r.action,id:r.id,call:r.call,pcm:n});break;default:/^set/.test(r.action)&&o.sonicObj[r.action](r.param)}},s=t.Sonic.toString(),u=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_sonic=(",s,a],{type:"text/javascript"}));n=new Worker(u),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(u)},1e4),n.onmessage=function(t){var r=c[t.data.id];if(r){var o=r.cbs[t.data.call];o&&o(t.data)}}}var l=new f(n,r);return l.id=++c.id,c[l.id]=l,n.postMessage({action:"init",id:l.id,sampleRate:r.sampleRate,x:new Int16Array(5)}),e=n,l}catch(t){return n&&n.terminate(),console.error(t),null}};var f=function(t,r){this.worker=t,this.set=r,this.cbs={i:0}};f.prototype={cb:function(t){var r=this,o="cb"+ ++r.cbs.i;return r.cbs[o]=function(n){delete r.cbs[o],t(n)},o},flush:function(r){var n=this;n.worker&&n.worker.postMessage({action:"flush",id:n.id,call:n.cb(function(e){r&&r(e.pcm),n.worker=null,delete c[n.id];var i=-1;for(var a in c)i++;i&&t.CLog(o("IC5Y::sonic worker剩{1}个未flush",0,i),3)})})},input:function(t,r){var o=this;o.worker&&o.worker.postMessage({action:"input",id:o.id,pcm:t,call:o.cb(function(t){r&&r(t.pcm)})})}};var s=function(t){f.prototype[t]=function(r){var o=this;o.worker&&o.worker.postMessage({action:t,id:o.id,param:r})}};s("setPitch"),s("setRate"),s("setSpeed"),s("setVolume"),s("setChordPitch"),s("setQuality")}(r,0,o.$T,t)}();