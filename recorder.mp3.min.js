/*
錄音
https://github.com/xiangyuecn/Recorder
src: recorder-core.js,engine/mp3.js,engine/mp3-engine.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=e?window:Object;!function(e,t){"use strict";var a=function(){},n=function(e){return"number"==typeof e},s=function(e){return JSON.stringify(e)},r=function(e){return new I(e)},i=r.LM="2025-01-11 09:28",o="https://github.com/xiangyuecn/Recorder",_="Recorder",l="getUserMedia",f="srcSampleRate",c="sampleRate",u="bitRate",h="catch",b=e[_];if(b&&b.LM==i)return void b.CLog(b.i18n.$T("K8zP::重复导入{1}",0,_),3);r.<PERSON>=function(){var e=r.Stream;if(e){var t=B(e)[0];if(t){var a=t.readyState;return"live"==a||a==t.LIVE}}return!1},r.BufferSize=4096,r.Destroy=function(){for(var e in T(_+" Destroy"),R(),p)p[e]()};var p={};r.BindDestroy=function(e,t){p[e]=t},r.Support=function(){if(!t)return!1;var e=navigator.mediaDevices||{};return e[l]||(e=navigator)[l]||(e[l]=e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia),!!e[l]&&(r.Scope=e,!!r.GetContext())},r.GetContext=function(e){if(!t)return null;var a=window.AudioContext;if(a||(a=window.webkitAudioContext),!a)return null;var n=r.Ctx,s=0;return n||(n=r.Ctx=new a,s=1,r.NewCtxs=r.NewCtxs||[],r.BindDestroy("Ctx",function(){var e=r.Ctx;e&&e.close&&(m(e),r.Ctx=0);var t=r.NewCtxs;r.NewCtxs=[];for(var a=0;a<t.length;a++)m(t[a])})),e&&n.close&&(s||(n._useC||m(n),n=new a),n._useC=1,r.NewCtxs.push(n)),n},r.CloseNewCtx=function(e){if(e&&e.close){m(e);for(var t=r.NewCtxs||[],a=t.length,n=0;n<t.length;n++)if(t[n]==e){t.splice(n,1);break}T(V("mSxV::剩{1}个GetContext未close",0,a+"-1="+t.length),t.length?3:0)}};var m=function(e){if(e&&e.close&&!e._isC&&(e._isC=1,"closed"!=e.state))try{e.close()}catch(e){T("ctx close err",1,e)}},v=r.ResumeCtx=function(e,t,a,n){var s=0,r=0,i=0,o=0,_="EventListener",l="ResumeCtx ",f=function(t,l){r&&c(),s||(s=1,t&&n(t,o),l&&a(o)),l&&(!e._LsSC&&e["add"+_]&&e["add"+_]("statechange",u),e._LsSC=1,i=1)},c=function(e){if(!e||!r){r=e?1:0;for(var t=["focus","mousedown","mouseup","touchstart","touchend"],a=0;a<t.length;a++)window[(e?"add":"remove")+_](t[a],u,!0)}},u=function(){var a=e.state,n=d(a);if(!s&&!t(n?++o:o))return f();n?(i&&T(l+"sc "+a,3),c(1),e.resume().then(function(){i&&T(l+"sc "+e.state),f(0,1)})[h](function(t){T(l+"error",1,t),d(e.state)||f(t.message||"error")})):"closed"==a?(i&&!e._isC&&T(l+"sc "+a,1),f("ctx closed")):f(0,1)};u()},d=r.CtxSpEnd=function(e){return"suspended"==e||"interrupted"==e},g=function(e){var t=e.state,a="ctx.state="+t;return d(t)&&(a+=V("nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）")),a},S="ConnectEnableWebM";r[S]=!0;var w="ConnectEnableWorklet";r[w]=!1;var M=function(e){var t=e.BufferSize||r.BufferSize,a=e.Stream,n=a._c,i=n[c],o={},l=B(a)[0],f=null,u="";if(l&&l.getSettings){var b=(f=l.getSettings())[c];b&&b!=i&&(u=V("eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，",0,b,i))}a._ts=f,T(u+"Stream TrackSet: "+s(f),u?3:0);var p,m,d,g=function(e){var t=a._m=n.createMediaStreamSource(a),s=n.destination,r="createMediaStreamDestination";n[r]&&(s=a._d=n[r]()),t.connect(e),e.connect(s)},M="",y=a._call,R=function(e,t){for(var a in y){if(t!=i){o.index=0;var n=(o=r.SampleData([e],t,i,o,{_sum:1})).data,s=o._sum}else{o={};for(var _=e.length,l=(n=new Int16Array(_),s=0,0);l<_;l++){var f=Math.max(-1,Math.min(1,e[l]));f=f<0?32768*f:32767*f,n[l]=f,s+=Math.abs(f)}}for(var c in y)y[c](n,s);return}},x="ScriptProcessor",E="audioWorklet",C=_+" "+E,I="RecProc",P="MediaRecorder",H=P+".WebM.PCM",O=n.createScriptProcessor||n.createJavaScriptNode,N=V("ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。",0,E),F=function(){m=a.isWorklet=!1,A(a),T(V("7TU0::Connect采用老的{1}，",0,x)+D.get(r[w]?V("JwCL::但已设置{1}尝试启用{2}",2):V("VGjB::可设置{1}尝试启用{2}",2),[_+"."+w+"=true",E])+M+N,3);var e=a._p=O.call(n,t,1,1);g(e),e.onaudioprocess=function(e){var t=e.inputBuffer.getChannelData(0);R(t,i)}},j=function(){p=a.isWebM=!1,k(a),m=a.isWorklet=!O||r[w];var e=window.AudioWorkletNode;if(m&&n[E]&&e){var s=function(){var e=function(e){return e.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,C)},t="class "+I+" extends AudioWorkletProcessor{";return t+="constructor "+e(function(e){DEL_super(e);var t=this,a=e.processorOptions.bufferSize;t.bufferSize=a,t.buffer=new Float32Array(2*a),t.pos=0,t.port.onmessage=function(e){e.data.kill&&(t.kill=!0,$C.log("$RA kill call"))},$C.log("$RA .ctor call",e)}),t+="process "+e(function(e,t,a){var n=this,s=n.bufferSize,r=n.buffer,i=n.pos;if((e=(e[0]||[])[0]||[]).length){r.set(e,i);var o=~~((i+=e.length)/s)*s;if(o){this.port.postMessage({val:r.slice(0,o)});var _=r.subarray(o,i);(r=new Float32Array(2*s)).set(_),i=_.length,n.buffer=r}n.pos=i}return!n.kill}),t=(t+='}try{registerProcessor("'+I+'", '+I+')}catch(e){$C.error("'+C+' Reg Error",e)}').replace(/\$C\./g,"console."),"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(t)))},o=function(){return m&&a._na},l=a._na=function(){""!==d&&(clearTimeout(d),d=setTimeout(function(){d=0,o()&&(T(V("MxX1::{1}未返回任何音频，恢复使用{2}",0,E,x),3),O&&F())},500))},f=function(){if(o()){var s=a._n=new e(n,I,{processorOptions:{bufferSize:t}});g(s),s.port.onmessage=function(e){d&&(clearTimeout(d),d=""),o()?R(e.data.val,i):m||T(V("XUap::{1}多余回调",0,E),3)},T(V("yOta::Connect采用{1}，设置{2}可恢复老式{3}",0,E,_+"."+w+"=false",x)+M+N,3)}},c=function(){if(o())if(n[I])f();else{var e=s();n[E].addModule(e).then(function(e){o()&&(n[I]=1,f(),d&&l())})[h](function(e){T(E+".addModule Error",1,e),o()&&F()})}};v(n,function(){return o()},c,c)}else F()};(function(){var e=window[P],n="ondataavailable",s="audio/webm; codecs=pcm";p=a.isWebM=r[S];var i=e&&n in e.prototype&&e.isTypeSupported(s);if(M=i?"":V("VwPd::（此浏览器不支持{1}）",0,H),p&&i){var o=function(){return p&&a._ra};a._ra=function(){""!==d&&(clearTimeout(d),d=setTimeout(function(){o()&&(T(V("vHnb::{1}未返回任何音频，降级使用{2}",0,P,E),3),j())},500))};var l=Object.assign({mimeType:s},r.ConnectWebMOptions),f=a._r=new e(a,l),c=a._rd={};f[n]=function(e){var t=new FileReader;t.onloadend=function(){if(o()){var e=L(new Uint8Array(t.result),c);if(!e)return;if(-1==e)return void j();d&&(clearTimeout(d),d=""),R(e,c.webmSR)}else p||T(V("O9P7::{1}多余回调",0,P),3)},t.readAsArrayBuffer(e.data)};try{f.start(~~(t/48)),T(V("LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}",0,H,_+"."+S+"=false",E,x))}catch(e){T("mr start err",1,e),j()}}else j()})()},y=function(e){e._na&&e._na(),e._ra&&e._ra()},A=function(e){e._na=null,e._n&&(e._n.port.postMessage({kill:!0}),e._n.disconnect(),e._n=null)},k=function(e){if(e._ra=null,e._r){try{e._r.stop()}catch(e){T("mr stop err",1,e)}e._r=null}},R=function(e){var t=(e=e||r)==r,a=e.Stream;a&&(a._m&&(a._m.disconnect(),a._m=null),!a._RC&&a._c&&r.CloseNewCtx(a._c),a._RC=null,a._c=null,a._d&&(x(a._d.stream),a._d=null),a._p&&(a._p.disconnect(),a._p.onaudioprocess=a._p=null),A(a),k(a),t&&x(a)),e.Stream=0},x=r.StopS_=function(e){for(var t=B(e),a=0;a<t.length;a++){var n=t[a];n.stop&&n.stop()}e.stop&&e.stop()},B=function(e){var t=0,a=0,n=[];e.getAudioTracks&&(t=e.getAudioTracks(),a=e.getVideoTracks()),t||(t=e.audioTracks,a=e.videoTracks);for(var s=0,r=t?t.length:0;s<r;s++)n.push(t[s]);for(s=0,r=a?a.length:0;s<r;s++)n.push(a[s]);return n};r.SampleData=function(e,t,a,n,s){var i="SampleData";n||(n={});var o,_=n.index||0,l=n.offset||0,f=n.raisePrev||0,c=n.filter;(c&&c.fn&&(c.sr&&c.sr!=t||c.srn&&c.srn!=a)&&(c=null,T(V("d48C::{1}的filter采样率变了，重设滤波",0,i),3)),c)||(c=a<=t?{fn:(o=a>3*t/4?0:a/2*3/4)?r.IIRFilter(!0,t,o):0}:{fn:(o=t>3*a/4?0:t/2*3/4)?r.IIRFilter(!0,a,o):0});c.sr=t,c.srn=a;var u=c.fn,h=n.frameNext||[];s||(s={});var b=s.frameSize||1;s.frameType&&(b="mp3"==s.frameType?1152:1);var p=s._sum,m=0,v=e.length;_>v+1&&T(V("tlbC::{1}似乎传入了未重置chunk {2}",0,i,_+">"+v),3);for(var d=0,g=_;g<v;g++)d+=e[g].length;var S=t/a;if(S>1)d=Math.max(0,d-Math.floor(l)),d=Math.floor(d/S);else if(S<1){var w=1/S;d=Math.floor(d*w)}d+=h.length;var M=new Int16Array(d),y=0;for(g=0;g<h.length;g++)M[y]=h[g],y++;for(;_<v;_++){var A=e[_],k=A instanceof Float32Array,R=(g=l,A.length),x=u&&u.Embed,B=0,E=0,C=0,I=0;if(S<1){for(var L=y+g,P=f,H=0;H<R;H++){var O=A[H];k&&(O=(O=Math.max(-1,Math.min(1,O)))<0?32768*O:32767*O);var N=Math.floor(L);L+=w;for(var D=Math.floor(L),F=(O-P)/(D-N),j=1;N<D;N++,j++){var X=Math.floor(P+j*F);x?(C=X,I=x.b0*C+x.b1*x.x1+x.b0*x.x2-x.a1*x.y1-x.a2*x.y2,x.x2=x.x1,x.x1=C,x.y2=x.y1,x.y1=I,X=I):X=u?u(X):X,X>32767?X=32767:X<-32768&&(X=-32768),p&&(m+=Math.abs(X)),M[N]=X,y++}P=f=O,g+=w}l=g%1}else{H=0;for(var Y=0;H<R;H++,Y++){if(Y<R){O=A[Y];k&&(O=(O=Math.max(-1,Math.min(1,O)))<0?32768*O:32767*O),x?(C=O,I=x.b0*C+x.b1*x.x1+x.b0*x.x2-x.a1*x.y1-x.a2*x.y2,x.x2=x.x1,x.x1=C,x.y2=x.y1,x.y1=I):I=u?u(O):O}if(B=E,E=I,0!=Y){var z=Math.floor(g);if(H==z){var q=B+((Math.ceil(g)<R?E:B)-B)*(g-z);q>32767?q=32767:q<-32768&&(q=-32768),p&&(m+=Math.abs(q)),M[y]=q,y++,g+=S}}else H--}l=Math.max(0,g-R)}}S<1&&y+1==d&&(d--,M=new Int16Array(M.buffer.slice(0,2*d))),y-1!=d&&y!=d&&T(i+" idx:"+y+" != size:"+d,3),h=null;var G=d%b;if(G>0){var U=2*(d-G);h=new Int16Array(M.buffer.slice(U)),M=new Int16Array(M.buffer.slice(0,U))}var K={index:_,offset:l,raisePrev:f,filter:c,frameNext:h,sampleRate:a,data:M};return p&&(K._sum=m),K},r.IIRFilter=function(e,t,a){var n=2*Math.PI*a/t,s=Math.sin(n),r=Math.cos(n),i=s/2,o=1+i,_=-2*r/o,l=(1-i)/o;if(e)var f=(1-r)/2/o,c=(1-r)/o;else f=(1+r)/2/o,c=-(1+r)/o;var u=0,h=0,b=0,p=0,m=0,v=function(e){return b=f*e+c*u+f*h-_*p-l*m,h=u,u=e,m=p,p=b,b};return v.Embed={x1:0,x2:0,y1:0,y2:0,b0:f,b1:c,a1:_,a2:l},v},r.PowerLevel=function(e,t){var a=e/t||0;return a<1251?Math.round(a/1250*10):Math.round(Math.min(100,Math.max(0,100*(1+Math.log(a/1e4)/Math.log(10)))))},r.PowerDBFS=function(e){var t=Math.max(.1,e||0),a=32767;return t=Math.min(t,a),t=20*Math.log(t/a)/Math.log(10),Math.max(-100,Math.round(t))},r.CLog=function(e,t){if("object"==typeof console){var a=new Date,s=("0"+a.getMinutes()).substr(-2)+":"+("0"+a.getSeconds()).substr(-2)+"."+("00"+a.getMilliseconds()).substr(-3),i=this&&this.envIn&&this.envCheck&&this.id,o=["["+s+" "+_+(i?":"+i:"")+"]"+e],l=arguments,f=r.CLog,c=2,u=f.log||console.log;for(n(t)?u=1==t?f.error||console.error:3==t?f.warn||console.warn:u:c=1;c<l.length;c++)o.push(l[c]);E?u&&u("[IsLoser]"+o[0],o.length>1?o:""):u.apply(console,o)}};var T=function(){r.CLog.apply(this,arguments)},E=!0;try{E=!console.log.apply}catch(e){}var C=0;function I(e){var t=this;t.id=++C;var s={type:"mp3",onProcess:a};for(var r in e)s[r]=e[r];t.set=s;var i=s[u],o=s[c];(i&&!n(i)||o&&!n(o))&&t.CLog(V.G("IllegalArgs-1",[V("VtS4::{1}和{2}必须是数值",0,c,u)]),1,e),s[u]=+i||16,s[c]=+o||16e3,t.state=0,t._S=9,t.Sync={O:9,C:9}}r.Sync={O:9,C:9},r.prototype=I.prototype={CLog:T,_streamStore:function(){return this.set.sourceStream?this:r},_streamGet:function(){return this._streamStore().Stream},_streamCtx:function(){var e=this._streamGet();return e&&e._c},open:function(e,n){var _=this,f=_.set,u=_._streamStore(),b=0;e=e||a;var p=function(e,t){t=!!t,_.CLog(V("5tWi::录音open失败：")+e+",isUserNotAllow:"+t,1),b&&r.CloseNewCtx(b),n&&n(e,t)};_._streamTag=l;var m=function(){_.CLog("open ok, id:"+_.id+" stream:"+_._streamTag),e(),_._SO=0},v=u.Sync,d=++v.O,S=v.C;_._O=_._O_=d,_._SO=_._S;var w=function(){if(S!=v.C||!_._O){var e=V("dFm8::open被取消");return d==v.O?_.close():e=V("VtJO::open被中断"),p(e),!0}};if(t){var y=_.envCheck({envName:"H5",canProcess:!0});if(y)p(V("A5bm::不能录音：")+y);else{var A,k=function(){(A=f.runningContext)||(A=b=r.GetContext(!0))};if(f.sourceStream){if(_._streamTag="set.sourceStream",!r.GetContext())return void p(V("1iU7::不支持此浏览器从流中获取录音"));k(),R(u);var x=_.Stream=f.sourceStream;x._c=A,x._RC=f.runningContext,x._call={};try{M(u)}catch(e){return R(u),void p(V("BTW2::从流中打开录音失败：")+e.message)}m()}else{var B=function(e,t){try{window.top.a}catch(e){return void p(V("Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})",0,'allow="camera;microphone"'))}T(1,e)&&(/Found/i.test(e)?p(t+V("jBa9::，无可用麦克风")):p(t))},T=function(e,t){if(/Permission|Allow/i.test(t))e&&p(V("gyO5::用户拒绝了录音权限"),!0);else{if(!1!==window.isSecureContext)return 1;e&&p(V("oWNo::浏览器禁止不安全页面录音，可开启https解决"))}};if(r.IsOpen())m();else if(r.Support()){k();var E,C,I=function(e){setTimeout(function(){e._call={};var t=r.Stream;t&&(R(),e._call=t._call),r.Stream=e,e._c=A,e._RC=f.runningContext,w()||(r.IsOpen()?(t&&_.CLog(V("upb8::发现同时多次调用open"),1),M(u),m()):p(V("Q1GA::录音功能无效：无音频流")))},100)},L=function(e){var t=e.name||e.message||e.code+":"+e,a="";1==P&&T(0,t)&&(a=V("KxE2::，将尝试禁用回声消除后重试"));var n=V("xEQR::请求录音权限错误"),s=V("bDOG::无法录音：");_.CLog(n+a+"|"+e,a||C?3:1,e),a?(E=t,C=e,H(1)):C?(_.CLog(n+"|"+C,1,C),B(E,s+C)):B(t,s+e)},P=0,H=function(e){P++;var t="audioTrackSet",a="autoGainControl",n="echoCancellation",u="noiseSuppression",b=t+":{"+n+","+u+","+a+"}",p=JSON.parse(s(f[t]||!0));_.CLog("open... "+P+" "+t+":"+s(p)),e&&("object"!=typeof p&&(p={}),p[a]=!1,p[n]=!1,p[u]=!1),p[c]&&_.CLog(V("IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象",0,t+"."+c),3);var m={audio:p,video:f.videoTrackSet||!1};try{var v=r.Scope[l](m,I,L)}catch(e){_.CLog(l,3,e),m={audio:!0,video:!1},v=r.Scope[l](m,I,L)}_.CLog(l+"("+s(m)+") "+g(A)+V("RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置",0,b,t)+"("+o+") LM:"+i+" UA:"+navigator.userAgent),v&&v.then&&v.then(I)[h](L)};H()}else B("",V("COxc::此浏览器不支持录音"))}}}else p(V.G("NonBrowser-1",["open"])+V("EMJq::，可尝试使用RecordApp解决方案")+"("+o+"/tree/master/app-support-sample)")},close:function(e){e=e||a;var t=this,n=t._streamStore();t._stop();var s=" stream:"+t._streamTag,r=n.Sync;if(t._O=0,t._O_!=r.O)return t.CLog(V("hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）")+s,3),void e();r.C++,R(n),t.CLog("close,"+s),e()},mock:function(e,t){var a=this;return a._stop(),a.isMock=1,a.mockEnvInfo=null,a.buffers=[e],a.recSize=e.length,a._setSrcSR(t),a._streamTag="mock",a},_setSrcSR:function(e){var t=this,a=t.set,n=a[c];n>e?a[c]=e:n=0,t[f]=e,t.CLog(f+": "+e+" set."+c+": "+a[c]+(n?" "+V("UHvm::忽略")+": "+n:""),n?3:0)},envCheck:function(e){var t,a=this,n=a.set,s="CPU_BE";if(t||r[s]||"function"!=typeof Int8Array||new Int8Array(new Int32Array([1]).buffer)[0]||(t=V("Essp::不支持{1}架构",0,s)),!t){var i=n.type,o=a[i+"_envCheck"];n.takeoffEncodeChunk&&(o?e.canProcess||(t=V("7uMV::{1}环境不支持实时处理",0,e.envName)):t=V("2XBl::{1}类型不支持设置takeoffEncodeChunk",0,i)+(a[i]?"":V("LG7e::(未加载编码器)"))),!t&&o&&(t=a[i+"_envCheck"](e,n))}return t||""},envStart:function(e,t){var a=this,n=a.set;if(a.isMock=e?1:0,a.mockEnvInfo=e,a.buffers=[],a.recSize=0,e&&(a._streamTag="env$"+e.envName),a.state=1,a.envInLast=0,a.envInFirst=0,a.envInFix=0,a.envInFixTs=[],a._setSrcSR(t),a.engineCtx=0,a[n.type+"_start"]){var s=a.engineCtx=a[n.type+"_start"](n);s&&(s.pcmDatas=[],s.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(e,t){var a=this,n=a.set,s=a.engineCtx;if(1==a.state){var i=a[f],o=e.length,_=r.PowerLevel(t,o),l=a.buffers,u=l.length;l.push(e);var h=l,b=u,p=Date.now(),m=Math.round(o/i*1e3);a.envInLast=p,1==a.buffers.length&&(a.envInFirst=p-m);var v=a.envInFixTs;v.splice(0,0,{t:p,d:m});for(var d=p,g=0,S=0;S<v.length;S++){var w=v[S];if(p-w.t>3e3){v.length=S;break}d=w.t,g+=w.d}var M=v[1],y=p-d;if(y-g>y/3&&(M&&y>1e3||v.length>=6)){var A=p-M.t-m;if(A>m/5){var k=!n.disableEnvInFix;if(a.CLog("["+p+"]"+D.get(V(k?"4Kfd::补偿{1}ms":"bM5i::未补偿{1}ms",1),[A]),3),a.envInFix+=A,k){var R=new Int16Array(A*i/1e3);o+=R.length,l.push(R)}}}var x=a.recSize,B=o,T=x+B;if(a.recSize=T,s){var E=r.SampleData(l,i,n[c],s.chunkInfo);s.chunkInfo=E,T=(x=s.pcmSize)+(B=E.data.length),s.pcmSize=T,l=s.pcmDatas,u=l.length,l.push(E.data),i=E[c]}var C=Math.round(T/i*1e3),I=l.length,L=h.length,P=function(){for(var e=H?0:-B,t=null==l[0],r=u;r<I;r++){var i=l[r];null==i?t=1:(e+=i.length,s&&i.length&&a[n.type+"_encode"](s,i))}if(t&&s){r=b;for(h[0]&&(r=0);r<L;r++)h[r]=null}t&&(e=H?B:0,l[0]=null),s?s.pcmSize+=e:a.recSize+=e},H=0,O="rec.set.onProcess";try{H=!0===(H=n.onProcess(l,_,C,i,u,P))}catch(e){console.error(O+V("gFUF::回调出错是不允许的，需保证不会抛异常"),e)}var N=Date.now()-p;if(N>10&&a.envInFirst-p>1e3&&a.CLog(O+V("2ghS::低性能，耗时{1}ms",0,N),3),H){var F=0;for(S=u;S<I;S++)null==l[S]?F=1:l[S]=new Int16Array(0);F?a.CLog(V("ufqH::未进入异步前不能清除buffers"),3):s?s.pcmSize-=B:a.recSize-=B}else P()}else a.state||a.CLog("envIn at state=0",3)},start:function(){var e=this,t=1;if(e.set.sourceStream?e.Stream||(t=0):r.IsOpen()||(t=0),t){var a=e._streamCtx();if(e.CLog(V("kLDN::start 开始录音，")+g(a)+" stream:"+e._streamTag),e._stop(),e.envStart(null,a[c]),e.state=3,e._SO&&e._SO+1!=e._S)e.CLog(V("Bp2y::start被中断"),3);else{e._SO=0;var n=function(){3==e.state&&(e.state=1,e.resume())},s="AudioContext resume: ";e._streamGet()._call[e.id]=function(){e.CLog(s+a.state+"|stream ok"),n()},v(a,function(t){return t&&e.CLog(s+"wait..."),3==e.state},function(t){t&&e.CLog(s+a.state),n()},function(t){e.CLog(s+a.state+V("upkE::，可能无法录音：")+t,1),n()})}}else e.CLog(V("6WmN::start失败：未open"),1)},pause:function(){var e=this,t=e._streamGet();e.state&&(e.state=2,e.CLog("pause"),t&&delete t._call[e.id])},resume:function(){var e=this,t=e._streamGet(),a="resume",n=a+"(wait ctx)";if(3==e.state)e.CLog(n);else if(e.state){e.state=1,e.CLog(a),e.envResume(),t&&(t._call[e.id]=function(t,a){1==e.state&&e.envIn(t,a)},y(t));var s=e._streamCtx();s&&v(s,function(t){return t&&e.CLog(n+"..."),1==e.state},function(a){a&&e.CLog(n+s.state),y(t)},function(t){e.CLog(n+s.state+"[err]"+t,1)})}},_stop:function(e){var t=this,a=t.set;t.isMock||t._S++,t.state&&(t.pause(),t.state=0),!e&&t[a.type+"_stop"]&&(t[a.type+"_stop"](t.engineCtx),t.engineCtx=0)},stop:function(e,t,a){var n,s=this,l=s.set,u=s.envInLast-s.envInFirst,h=u&&s.buffers.length;s.CLog(V("Xq4s::stop 和start时差:")+(u?u+"ms "+V("3CQP::补偿:")+s.envInFix+"ms envIn:"+h+" fps:"+(h/u*1e3).toFixed(1):"-")+" stream:"+s._streamTag+" ("+o+") LM:"+i);var b=function(){s._stop(),a&&s.close()},p=function(e){s.CLog(V("u8JG::结束录音失败：")+e,1),t&&t(e),b()},m=function(t,a,i){var o="blob",f="arraybuffer",c="dataType",u="DefaultDataType",h=s[c]||r[u]||o,m=c+"="+h,v=t instanceof ArrayBuffer,d=0,g=v?t.byteLength:t.size;if(h==f?v||(d=1):h==o?"function"!=typeof Blob?d=V.G("NonBrowser-1",[m])+V("1skY::，请设置{1}",0,_+"."+u+'="'+f+'"'):(v&&(t=new Blob([t],{type:a})),t instanceof Blob||(d=1),a=t.type||a):d=V.G("NotSupport-1",[m]),s.CLog(V("Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b",0,Date.now()-n,i,g)+" "+m+","+a),d)p(1!=d?d:V("Vkbd::{1}编码器返回的不是{2}",0,l.type,h)+", "+m);else{if(l.takeoffEncodeChunk)s.CLog(V("QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"),3);else if(g<Math.max(50,i/5))return void p(V("Sz2H::生成的{1}无效",0,l.type));e&&e(t,i,a),b()}};if(!s.isMock){var v=3==s.state;if(!s.state||v)return void p(V("wf9t::未开始录音")+(v?V("Dl2c::，开始录音前无用户交互导致AudioContext未运行"):""))}s._stop(!0);var d=s.recSize;if(d)if(s[l.type]){if(s.isMock){var g=s.envCheck(s.mockEnvInfo||{envName:"mock",canProcess:!1});if(g)return void p(V("AxOH::录音错误：")+g)}var S=s.engineCtx;if(s[l.type+"_complete"]&&S){var w=Math.round(S.pcmSize/l[c]*1e3);return n=Date.now(),void s[l.type+"_complete"](S,function(e,t){m(e,t,w)},p)}if(n=Date.now(),s.buffers[0]){var M=r.SampleData(s.buffers,s[f],l[c]);l[c]=M[c];var y=M.data;w=Math.round(y.length/l[c]*1e3);s.CLog(V("CxeT::采样:{1} 花:{2}ms",0,d+"->"+y.length,Date.now()-n)),setTimeout(function(){n=Date.now(),s[l.type](y,function(e,t){m(e,t,w)},function(e){p(e)})})}else p(V("xkKd::音频buffers被释放"))}else p(V("xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载",0,l.type,_));else p(V("Ltz3::未采集到录音"))}};var L=function(e,t){t.pos||(t.pos=[0],t.tracks={},t.bytes=[]);var a=t.tracks,n=[t.pos[0]],s=function(){t.pos[0]=n[0]},r=t.bytes.length,i=new Uint8Array(r+e.length);if(i.set(t.bytes),i.set(e,r),t.bytes=i,!t._ht){if(O(i,n),N(i,n),!P(O(i,n),[24,83,128,103]))return;for(O(i,n);n[0]<i.length;){var o=O(i,n),_=N(i,n),l=[0],f=0;if(!_)return;if(P(o,[22,84,174,107])){for(;l[0]<_.length;){var u=O(_,l),h=N(_,l),b=[0],p={channels:0,sampleRate:0};if(P(u,[174]))for(;b[0]<h.length;){var m=O(h,b),v=N(h,b),d=[0];if(P(m,[215])){var g=H(v);p.number=g,a[g]=p}else if(P(m,[131])){1==(g=H(v))?p.type="video":2==g?(p.type="audio",f||(t.track0=p),p.idx=f++):p.type="Type-"+g}else if(P(m,[134])){for(var S="",w=0;w<v.length;w++)S+=String.fromCharCode(v[w]);p.codec=S}else if(P(m,[225]))for(;d[0]<v.length;){var M=O(v,d),y=N(v,d);if(P(M,[181])){g=0;var A=new Uint8Array(y.reverse()).buffer;4==y.length?g=new Float32Array(A)[0]:8==y.length?g=new Float64Array(A)[0]:T("WebM Track !Float",1,y),p[c]=Math.round(g)}else P(M,[98,100])?p.bitDepth=H(y):P(M,[159])&&(p.channels=H(y))}}}t._ht=1,T("WebM Tracks",a),s();break}}}var k=t.track0;if(k){var R=k[c];if(t.webmSR=R,16==k.bitDepth&&/FLOAT/i.test(k.codec)&&(k.bitDepth=32,T("WebM 16->32 bit",3)),R<8e3||32!=k.bitDepth||k.channels<1||!/(\b|_)PCM\b/i.test(k.codec))return t.bytes=[],t.bad||T("WebM Track Unexpected",3,t),t.bad=1,-1;for(var x=[],B=0;n[0]<i.length;){u=O(i,n);if(!(h=N(i,n)))break;if(P(u,[163])){var E=15&h[0];if(!(p=a[E]))return T("WebM !Track"+E,1,a),-1;if(0===p.idx){var C=new Uint8Array(h.length-4);for(w=4;w<h.length;w++)C[w-4]=h[w];x.push(C),B+=C.length}}s()}if(B){var I=new Uint8Array(i.length-t.pos[0]);I.set(i.subarray(t.pos[0])),t.bytes=I,t.pos[0]=0;C=new Uint8Array(B),w=0;for(var L=0;w<x.length;w++)C.set(x[w],L),L+=x[w].length;A=new Float32Array(C.buffer);if(k.channels>1){var D=[];for(w=0;w<A.length;)D.push(A[w]),w+=k.channels;A=new Float32Array(D)}return A}}},P=function(e,t){if(!e||e.length!=t.length)return!1;if(1==e.length)return e[0]==t[0];for(var a=0;a<e.length;a++)if(e[a]!=t[a])return!1;return!0},H=function(e){for(var t="",a=0;a<e.length;a++){var n=e[a];t+=(n<16?"0":"")+n.toString(16)}return parseInt(t,16)||0},O=function(e,t,a){var n=t[0];if(!(n>=e.length)){var s=("0000000"+e[n].toString(2)).substr(-8),r=/^(0*1)(\d*)$/.exec(s);if(r){var i=r[1].length,o=[];if(!(n+i>e.length)){for(var _=0;_<i;_++)o[_]=e[n],n++;return a&&(o[0]=parseInt(r[2]||"0",2)),t[0]=n,o}}}},N=function(e,t){var a=O(e,t,1);if(a){var n=H(a),s=t[0],r=[];if(n<2147483647){if(s+n>e.length)return;for(var i=0;i<n;i++)r[i]=e[s],s++}return t[0]=s,r}},D=r.i18n={lang:"zh-CN",alias:{"zh-CN":"zh","en-US":"en"},locales:{},data:{},put:function(e,t){var a=_+".i18n.put: ",n=e.overwrite;n=null==n||n;var s=e.lang;if(!(s=D.alias[s]||s))throw new Error(a+"set.lang?");var r=D.locales[s];r||(r={},D.locales[s]=r);for(var i,o=/^([\w\-]+):/,l=0;l<t.length;l++){var f=t[l];if(i=o.exec(f)){var c=i[1];f=f.substr(c.length+1);!n&&r[c]||(r[c]=f)}else T(a+"'key:'? "+f,3,e)}},get:function(){return D.v_G.apply(null,arguments)},v_G:function(e,t,a){t=t||[],a=a||D.lang,a=D.alias[a]||a;var n=D.locales[a],s=n&&n[e]||"";return s||"zh"==a?(D.lastLang=a,"=Empty"==s?"":s.replace(/\{(\d+)(\!?)\}/g,function(a,n,r){return a=t[(n=+n||0)-1],(n<1||n>t.length)&&(a="{?}",T("i18n["+e+"] no {"+n+"}: "+s,3)),r?"":a})):"en"==a?D.v_G(e,t,"zh"):D.v_G(e,t,"en")},$T:function(){return D.v_T.apply(null,arguments)},v_T:function(){for(var e,t=arguments,a="",s=[],r=0,i=_+".i18n.$T:",o=/^([\w\-]*):/,l=0;l<t.length;l++){var f=t[l];if(0==l){if(!(a=(e=o.exec(f))&&e[1]))throw new Error(i+"0 'key:'?");f=f.substr(a.length+1)}if(-1===r)s.push(f);else{if(r)throw new Error(i+" bad args");if(0===f)r=-1;else if(n(f)){if(f<1)throw new Error(i+" bad args");r=f}else{var c=1==l?"en":l?"":"zh";if((e=o.exec(f))&&(c=e[1]||c,f=f.substr(e[1].length+1)),!e||!c)throw new Error(i+l+" 'lang:'?");D.put({lang:c,overwrite:!1},[a+":"+f])}}}return a?r>0?a:D.v_G(a,s):""}},V=D.$T;V.G=D.get,V("NonBrowser-1::非浏览器环境，不支持{1}",1),V("IllegalArgs-1::参数错误：{1}",1),V("NeedImport-2::调用{1}需要先导入{2}",2),V("NotSupport-1::不支持：{1}",1),r.TrafficImgUrl="";r.Traffic=function(e){if(t){e=e?"/"+_+"/Report/"+e:"";var a=r.TrafficImgUrl;if(a){var n=r.Traffic,s=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],i=s[1]||"http://file/",o=(s[0]||i)+e;if(0==a.indexOf("//")&&(a=/^https:/i.test(o)?"https:"+a:"http:"+a),e&&(a=a+"&cu="+encodeURIComponent(i+e)),!n[o])n[o]=1,(new Image).src=a,T("Traffic Analysis Image: "+(e||_+".TrafficImgUrl="+r.TrafficImgUrl))}}};b&&(T(V("8HO5::覆盖导入{1}",0,_),1),b.Destroy());e[_]=r}(t,e),"function"==typeof define&&define.amd&&define(function(){return t.Recorder}),"object"==typeof module&&module.exports&&(module.exports=t.Recorder)}(),function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,a=t.i18n;!function(e,t,a,n){"use strict";var s="48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000",r="8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160, 192, 224, 256, 320";e.prototype.enc_mp3={stable:!0,takeEC:"full",getTestMsg:function(){return a("Zm7L::采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）",0,s,r)}};var i,o=function(t){var n=t.bitRate,i=t.sampleRate,o=i;if(-1==(" "+r+",").indexOf(" "+n+",")&&e.CLog(a("eGB9::{1}不在mp3支持的取值范围：{2}",0,"bitRate="+n,r),3),-1==(" "+s+",").indexOf(" "+i+",")){for(var _=s.split(", "),l=[],f=0;f<_.length;f++)l.push({v:+_[f],s:Math.abs(_[f]-i)});l.sort(function(e,t){return e.s-t.s}),o=l[0].v,t.sampleRate=o,e.CLog(a("zLTa::sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}",0,o,i,s),3)}},_=function(){return a.G("NeedImport-2",["mp3.js","src/engine/mp3-engine.js"])},l=n&&"function"==typeof Worker;e.prototype.mp3=function(t,a,n){var s=this,r=s.set,i=t.length;if(e.lamejs){if(l){var f=s.mp3_start(r);if(f){if(f.isW)return s.mp3_encode(f,t),void s.mp3_complete(f,a,n,1);s.mp3_stop(f)}}o(r);var c=new e.lamejs.Mp3Encoder(1,r.sampleRate,r.bitRate),b=57600,p=new Int8Array(5e5),m=0,v=0,d=0,g=function(){try{if(v<i)var e=c.encodeBuffer(t.subarray(v,v+b));else{d=1;e=c.flush()}}catch(e){if(console.error(e),!d)try{c.flush()}catch(e){console.error(e)}return void n("MP3 Encoder: "+e.message)}var s=e.length;if(s>0){if(m+s>p.length){var o=new Int8Array(p.length+Math.max(5e5,s));o.set(p.subarray(0,m)),p=o}p.set(e,m),m+=s}if(v<i)v+=b,setTimeout(g);else{var _=[p.buffer.slice(0,m)],l=u.fn(_,m,i,r.sampleRate);h(l,r),a(_[0]||new ArrayBuffer(0),"audio/mp3")}};g()}else n(_())},e.BindDestroy("mp3Worker",function(){i&&(e.CLog("mp3Worker Destroy"),i.terminate(),i=null)}),e.prototype.mp3_envCheck=function(t,n){var s="";return n.takeoffEncodeChunk&&(c()||(s=a("yhUs::当前浏览器版本太低，无法实时处理"))),s||e.lamejs||(s=_()),s},e.prototype.mp3_start=function(e){return c(e)};var f={id:0},c=function(t,n){var s,r=function(e){var t=e.data,a=s.wkScope.wk_ctxs,n=s.wkScope.wk_lame,r=s.wkScope.wk_mp3TrimFix,i=a[t.id];if("init"==t.action)a[t.id]={sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:t.takeoff,pcmSize:0,memory:new Int8Array(5e5),mOffset:0,encObj:new n.Mp3Encoder(1,t.sampleRate,t.bitRate)};else if(!i)return;var o=function(e){var t=e.length;if(i.mOffset+t>i.memory.length){var a=new Int8Array(i.memory.length+Math.max(5e5,t));a.set(i.memory.subarray(0,i.mOffset)),i.memory=a}i.memory.set(e,i.mOffset),i.mOffset+=t};switch(t.action){case"stop":if(!i.isCp)try{i.encObj.flush()}catch(e){console.error(e)}i.encObj=null,delete a[t.id];break;case"encode":if(i.isCp)break;i.pcmSize+=t.pcm.length;try{var _=i.encObj.encodeBuffer(t.pcm)}catch(e){i.err=e,console.error(e)}_&&_.length>0&&(i.takeoff?b.onmessage({action:"takeoff",id:t.id,chunk:_}):o(_));break;case"complete":i.isCp=1;try{_=i.encObj.flush()}catch(e){i.err=e,console.error(e)}if(_&&_.length>0&&(i.takeoff?b.onmessage({action:"takeoff",id:t.id,chunk:_}):o(_)),i.err){b.onmessage({action:t.action,id:t.id,err:"MP3 Encoder: "+i.err.message});break}var l=[i.memory.buffer.slice(0,i.mOffset)],f=r.fn(l,i.mOffset,i.pcmSize,i.sampleRate);b.onmessage({action:t.action,id:t.id,blob:l[0]||new ArrayBuffer(0),meta:f})}},_=function(e){b.onmessage=function(t){var a=t;e&&(a=t.data);var n=f[a.id];n&&("takeoff"==a.action?n.set.takeoffEncodeChunk(new Uint8Array(a.chunk.buffer)):(n.call&&n.call(a),n.call=null))}},h=function(){var e={worker:b,set:t};return t?(e.id=++f.id,f[e.id]=e,o(t),b.postMessage({action:"init",id:e.id,sampleRate:t.sampleRate,bitRate:t.bitRate,takeoff:!!t.takeoffEncodeChunk,x:new Int16Array(5)})):b.postMessage({x:new Int16Array(5)}),e},b=i;if(n||!l)return e.CLog(a("k9PT::当前环境不支持Web Worker，mp3实时编码器运行在主线程中"),3),b={postMessage:function(e){r({data:e})}},s={wkScope:{wk_ctxs:{},wk_lame:e.lamejs,wk_mp3TrimFix:u}},_(),h();try{if(!b){var p=(r+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),m=");wk_lame();self.onmessage="+(p=p.replace(/[\w\$]+\.wkScope/g,"wkScope"));m+=";var wkScope={ wk_ctxs:{},wk_lame:wk_lame",m+=",wk_mp3TrimFix:{rm:"+u.rm+",fn:"+u.fn+"} }";var v=e.lamejs.toString(),d=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_lame=(",v,m],{type:"text/javascript"}));b=new Worker(d),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(d)},1e4),_(1)}var g=h();return g.isW=1,i=b,g}catch(e){return b&&b.terminate(),console.error(e),c(t,1)}};e.prototype.mp3_stop=function(t){if(t&&t.worker){t.worker.postMessage({action:"stop",id:t.id}),t.worker=null,delete f[t.id];var n=-1;for(var s in f)n++;n&&e.CLog(a("fT6M::mp3 worker剩{1}个未stop",0,n),3)}},e.prototype.mp3_encode=function(e,t){e&&e.worker&&e.worker.postMessage({action:"encode",id:e.id,pcm:t})},e.prototype.mp3_complete=function(e,t,n,s){var r=this;e&&e.worker?(e.call=function(a){s&&r.mp3_stop(e),a.err?n(a.err):(h(a.meta,e.set),t(a.blob,"audio/mp3"))},e.worker.postMessage({action:"complete",id:e.id})):n(a("mPxH::mp3编码器未start"))},e.mp3ReadMeta=function(e,t){var a="undefined"!=typeof window&&window.parseInt||"undefined"!=typeof self&&self.parseInt||parseInt,n=new Uint8Array(e[0]||[]);if(n.length<4)return null;var s=function(e,t){return("0000000"+((t||n)[e]||0).toString(2)).substr(-8)},r=s(0)+s(1),i=s(2)+s(3);if(!/^1{11}/.test(r))return null;var o={"00":2.5,10:2,11:1}[r.substr(11,2)],_={"01":3}[r.substr(13,2)],l={1:[44100,48e3,32e3],2:[22050,24e3,16e3],2.5:[11025,12e3,8e3]}[o];l&&(l=l[a(i.substr(4,2),2)]);var f=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320]][1==o?1:0][a(i.substr(0,4),2)];if(!(o&&_&&f&&l))return null;for(var c=Math.round(8*t/f),u=1==_?384:2==_||1==o?1152:576,h=u/l*1e3,b=Math.floor(u*f/8/l*1e3),p=0,m=0,v=0;v<e.length;v++){var d=e[v];if((m+=d.byteLength)>=b+3){var g=new Uint8Array(d);p="1"==s(d.byteLength-(m-(b+3)+1),g).charAt(6);break}}return p&&b++,{version:o,layer:_,sampleRate:l,bitRate:f,duration:c,size:t,hasPadding:p,frameSize:b,frameDurationFloat:h}};var u={rm:e.mp3ReadMeta,fn:function(e,t,a,n){var s=this.rm(e,t);if(!s)return{size:t,err:"mp3 unknown format"};var r=Math.round(a/n*1e3),i=Math.floor((s.duration-r)/s.frameDurationFloat);if(i>0){var o=i*s.frameSize-(s.hasPadding?1:0);t-=o;for(var _=0,l=[],f=0;f<e.length;f++){var c=e[f];if(o<=0)break;o>=c.byteLength?(o-=c.byteLength,l.push(c),e.splice(f,1),f--):(e[f]=c.slice(o),_=c,o=0)}if(!this.rm(e,t)){_&&(e[0]=_);for(f=0;f<l.length;f++)e.splice(f,0,l[f]);s.err="mp3 fix error: 已还原，错误原因不明"}var u=s.trimFix={};u.remove=i,u.removeDuration=Math.round(i*s.frameDurationFloat),u.duration=Math.round(8*t/s.bitRate)}return s}},h=function(t,n){var s="MP3 Info: ";(t.sampleRate&&t.sampleRate!=n.sampleRate||t.bitRate&&t.bitRate!=n.bitRate)&&(e.CLog(s+a("uY9i::和设置的不匹配{1}，已更新成{2}",0,"set:"+n.bitRate+"kbps "+n.sampleRate+"hz","set:"+t.bitRate+"kbps "+t.sampleRate+"hz"),3,n),n.sampleRate=t.sampleRate,n.bitRate=t.bitRate);var r=t.trimFix;r?(s+=a("iMSm::Fix移除{1}帧",0,r.remove)+" "+r.removeDuration+"ms -> "+r.duration+"ms",r.remove>2&&(t.err=(t.err?t.err+", ":"")+a("b9zm::移除帧数过多"))):s+=(t.duration||"-")+"ms",t.err?e.CLog(s,t.size?1:0,t.err,t):e.CLog(s,t)}}(t,0,a.$T,e)}(),function(e){"use strict";function t(){var e=function(e){return Math.log(e)/Math.log(10)},a=function(e){throw new Error("abort("+e+")")};function n(e){return new Int8Array(e)}function s(e){return new Int16Array(e)}function r(e){return new Int32Array(e)}function i(e){return new Float32Array(e)}function o(e){return new Float64Array(e)}function _(e){if(1==e.length)return i(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(_(e));return a}function l(e){if(1==e.length)return r(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(l(e));return a}function f(e){if(1==e.length)return s(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(f(e));return a}function c(e){if(1==e.length)return new Array(e[0]);var t=e[0];e=e.slice(1);for(var a=[],n=0;n<t;n++)a.push(c(e));return a}var u={fill:function(e,t,a,n){if(2==arguments.length)for(var s=0;s<e.length;s++)e[s]=arguments[1];else for(s=t;s<a;s++)e[s]=n}},h={arraycopy:function(e,t,a,n,s){for(var r=t+s;t<r;)a[n++]=e[t++]}},b={};function p(e){this.ordinal=e}b.SQRT2=1.4142135623730951,b.FAST_LOG10=function(t){return e(t)},b.FAST_LOG10_X=function(t,a){return e(t)*a},p.short_block_allowed=new p(0),p.short_block_coupled=new p(1),p.short_block_dispensed=new p(2),p.short_block_forced=new p(3);var m={};function v(e){this.ordinal=e}function d(e){var t=e;this.ordinal=function(){return t}}function g(){var e=3,t=98,a=4;this.getLameShortVersion=function(){return e+"."+t+"."+a}}function S(){var e=null;function t(e){this.bits=0|e}this.qupvt=null,this.setModules=function(t){this.qupvt=t,e=t};var n=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function s(e,t,a,n,s,r){var i=.5946/t;for(e>>=1;0!=e--;)s[r++]=i>a[n++]?0:1,s[r++]=i>a[n++]?0:1}function i(t,a,n,s,r,i){var o=(t>>=1)%2;for(t>>=1;0!=t--;){var _,l,f,c,u,h,b,p;_=n[s++]*a,l=n[s++]*a,u=0|_,f=n[s++]*a,h=0|l,c=n[s++]*a,b=0|f,_+=e.adj43[u],p=0|c,l+=e.adj43[h],r[i++]=0|_,f+=e.adj43[b],r[i++]=0|l,c+=e.adj43[p],r[i++]=0|f,r[i++]=0|c}0!=o&&(u=0|(_=n[s++]*a),h=0|(l=n[s++]*a),_+=e.adj43[u],l+=e.adj43[h],r[i++]=0|_,r[i++]=0|l)}function o(t,n,r,o,_){var l,f,c,h=0,b=0,p=0,m=0,v=n,d=0,g=v,S=0,w=t,M=0;for(c=null!=_&&o.global_gain==_.global_gain,f=o.block_type==Y.SHORT_TYPE?38:21,l=0;l<=f;l++){var y=-1;if((c||o.block_type==Y.NORM_TYPE)&&(y=o.global_gain-(o.scalefac[l]+(0!=o.preflag?e.pretab[l]:0)<<o.scalefac_scale+1)-8*o.subblock_gain[o.window[l]]),c&&_.step[l]==y)0!=b&&(i(b,r,w,M,g,S),b=0),0!=p&&a();else{var A,k=o.width[l];if(h+o.width[l]>o.max_nonzero_coeff&&(A=o.max_nonzero_coeff-h+1,u.fill(n,o.max_nonzero_coeff,576,0),(k=A)<0&&(k=0),l=f+1),0==b&&0==p&&(g=v,S=d,w=t,M=m),null!=_&&_.sfb_count1>0&&l>=_.sfb_count1&&_.step[l]>0&&y>=_.step[l]?(0!=b&&(i(b,r,w,M,g,S),b=0,g=v,S=d,w=t,M=m),p+=k):(0!=p&&(s(p,r,w,M,g,S),p=0,g=v,S=d,w=t,M=m),b+=k),k<=0){0!=p&&a(),0!=b&&a();break}}l<=f&&(d+=o.width[l],m+=o.width[l],h+=o.width[l])}0!=b&&(i(b,r,w,M,g,S),b=0),0!=p&&a()}function _(e,t,a){var n=0,s=0;do{var r=e[t++],i=e[t++];n<r&&(n=r),s<i&&(s=i)}while(t<a);return n<s&&(n=s),n}function l(e,t,a,n,s,r){var i,o=65536*x.ht[n].xlen+x.ht[s].xlen,_=0;do{var l=e[t++],f=e[t++];0!=l&&(l>14&&(l=15,_+=o),l*=16),0!=f&&(f>14&&(f=15,_+=o),l+=f),_+=x.largetbl[l]}while(t<a);return i=65535&_,(_>>=16)>i&&(_=i,n=s),r.bits+=_,n}function f(e,t,a,n){var s=0,r=x.ht[1].hlen;do{var i=2*e[t+0]+e[t+1];t+=2,s+=r[i]}while(t<a);return n.bits+=s,1}function c(e,t,a,n,s){var r,i,o=0,_=x.ht[n].xlen;i=2==n?x.table23:x.table56;do{var l=e[t+0]*_+e[t+1];t+=2,o+=i[l]}while(t<a);return r=65535&o,(o>>=16)>r&&(o=r,n++),s.bits+=o,n}function h(e,t,a,n,s){var r=0,i=0,o=0,_=x.ht[n].xlen,l=x.ht[n].hlen,f=x.ht[n+1].hlen,c=x.ht[n+2].hlen;do{var u=e[t+0]*_+e[t+1];t+=2,r+=l[u],i+=f[u],o+=c[u]}while(t<a);var h=n;return r>i&&(r=i,h++),r>o&&(r=o,h=n+2),s.bits+=r,h}var b=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function p(e,t,n,s){var r=_(e,t,n);switch(r){case 0:return r;case 1:return f(e,t,n,s);case 2:case 3:return c(e,t,n,b[r-1],s);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return h(e,t,n,b[r-1],s);default:var i,o;for(r>O.IXMAX_VAL&&a(),r-=15,i=24;i<32&&!(x.ht[i].linmax>=r);i++);for(o=i-8;o<24&&!(x.ht[o].linmax>=r);o++);return l(e,t,n,o,i,s)}}function m(e,a,n,s,r,i,o){for(var _=a.big_values,l=0;l<=22;l++)s[l]=O.LARGE_BITS;for(l=0;l<16;l++){var f=e.scalefac_band.l[l+1];if(f>=_)break;var c=0,u=new t(c),h=p(n,0,f,u);c=u.bits;for(var b=0;b<8;b++){var m=e.scalefac_band.l[l+b+2];if(m>=_)break;var v=c,d=p(n,f,m,u=new t(v));v=u.bits,s[l+b]>v&&(s[l+b]=v,r[l+b]=l,i[l+b]=h,o[l+b]=d)}}}function v(e,a,n,s,r,i,o,_){for(var l=a.big_values,f=2;f<Y.SBMAX_l+1;f++){var c=e.scalefac_band.l[f];if(c>=l)break;var u=r[f-2]+a.count1bits;if(n.part2_3_length<=u)break;var h=new t(u),b=p(s,c,l,h);u=h.bits,n.part2_3_length<=u||(n.assign(a),n.part2_3_length=u,n.region0_count=i[f-2],n.region1_count=f-2-i[f-2],n.table_select[0]=o[f-2],n.table_select[1]=_[f-2],n.table_select[2]=b)}}this.noquant_count_bits=function(e,n,s){var r=n.l3_enc,i=Math.min(576,n.max_nonzero_coeff+2>>1<<1);for(null!=s&&(s.sfb_count1=0);i>1&&0==(r[i-1]|r[i-2]);i-=2);n.count1=i;for(var o=0,_=0;i>3;i-=4){var l;if((2147483647&(r[i-1]|r[i-2]|r[i-3]|r[i-4]))>1)break;l=2*(2*(2*r[i-4]+r[i-3])+r[i-2])+r[i-1],o+=x.t32l[l],_+=x.t33l[l]}var f=o;if(n.count1table_select=0,o>_&&(f=_,n.count1table_select=1),n.count1bits=f,n.big_values=i,0==i)return f;if(n.block_type==Y.SHORT_TYPE)(o=3*e.scalefac_band.s[3])>n.big_values&&(o=n.big_values),_=n.big_values;else if(n.block_type==Y.NORM_TYPE){if(o=n.region0_count=e.bv_scf[i-2],_=n.region1_count=e.bv_scf[i-1],_=e.scalefac_band.l[o+_+2],o=e.scalefac_band.l[o+1],_<i){var c=new t(f);n.table_select[2]=p(r,_,i,c),f=c.bits}}else n.region0_count=7,n.region1_count=Y.SBMAX_l-1-7-1,(o=e.scalefac_band.l[8])>(_=i)&&(o=_);if(o=Math.min(o,i),_=Math.min(_,i),0<o&&(c=new t(f),n.table_select[0]=p(r,0,o,c),f=c.bits),o<_&&(c=new t(f),n.table_select[1]=p(r,o,_,c),f=c.bits),2==e.use_best_huffman&&a(),null!=s&&n.block_type==Y.NORM_TYPE){for(var u=0;e.scalefac_band.l[u]<n.big_values;)u++;s.sfb_count1=u}return f},this.count_bits=function(t,n,s,r){var i=s.l3_enc,_=O.IXMAX_VAL/e.IPOW20(s.global_gain);return s.xrpow_max>_?O.LARGE_BITS:(o(n,i,e.IPOW20(s.global_gain),s,r),2&t.substep_shaping&&a(),this.noquant_count_bits(t,s,r))},this.best_huffman_divide=function(e,a){var n=new D,s=a.l3_enc,i=r(23),o=r(23),_=r(23),l=r(23);if(a.block_type!=Y.SHORT_TYPE||1!=e.mode_gr){n.assign(a),a.block_type==Y.NORM_TYPE&&(m(e,a,s,i,o,_,l),v(e,n,a,s,i,o,_,l));var f=n.big_values;if(!(0==f||(s[f-2]|s[f-1])>1||(f=a.count1+2)>576)){n.assign(a),n.count1=f;for(var c=0,u=0;f>n.big_values;f-=4){var h=2*(2*(2*s[f-4]+s[f-3])+s[f-2])+s[f-1];c+=x.t32l[h],u+=x.t33l[h]}if(n.big_values=f,n.count1table_select=0,c>u&&(c=u,n.count1table_select=1),n.count1bits=c,n.block_type==Y.NORM_TYPE)v(e,n,a,s,i,o,_,l);else{if(n.part2_3_length=c,(c=e.scalefac_band.l[8])>f&&(c=f),c>0){var b=new t(n.part2_3_length);n.table_select[0]=p(s,0,c,b),n.part2_3_length=b.bits}f>c&&(b=new t(n.part2_3_length),n.table_select[1]=p(s,c,f,b),n.part2_3_length=b.bits),a.part2_3_length>n.part2_3_length&&a.assign(n)}}}};var d=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],g=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],w=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],M=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];function y(e,t){for(var a,n=t.tt[1][e],s=t.tt[0][e],r=0;r<x.scfsi_band.length-1;r++){for(a=x.scfsi_band[r];a<x.scfsi_band[r+1]&&!(s.scalefac[a]!=n.scalefac[a]&&n.scalefac[a]>=0);a++);if(a==x.scfsi_band[r+1]){for(a=x.scfsi_band[r];a<x.scfsi_band[r+1];a++)n.scalefac[a]=-1;t.scfsi[e][r]=1}}var i=0,o=0;for(a=0;a<11;a++)-1!=n.scalefac[a]&&(o++,i<n.scalefac[a]&&(i=n.scalefac[a]));for(var _=0,l=0;a<Y.SBPSY_l;a++)-1!=n.scalefac[a]&&(l++,_<n.scalefac[a]&&(_=n.scalefac[a]));for(r=0;r<16;r++)if(i<d[r]&&_<g[r]){var f=w[r]*o+M[r]*l;n.part2_length>f&&(n.part2_length=f,n.scalefac_compress=r)}}S.slen1_tab=w,S.slen2_tab=M,this.best_scalefac_store=function(t,a,n,s){var r,i,o,_,l=s.tt[a][n],f=0;for(o=0,r=0;r<l.sfbmax;r++){var c=l.width[r];for(o+=c,_=-c;_<0&&0==l.l3_enc[_+o];_++);0==_&&(l.scalefac[r]=f=-2)}if(0==l.scalefac_scale&&0==l.preflag){var u=0;for(r=0;r<l.sfbmax;r++)l.scalefac[r]>0&&(u|=l.scalefac[r]);if(!(1&u)&&0!=u){for(r=0;r<l.sfbmax;r++)l.scalefac[r]>0&&(l.scalefac[r]>>=1);l.scalefac_scale=f=1}}if(0==l.preflag&&l.block_type!=Y.SHORT_TYPE&&2==t.mode_gr){for(r=11;r<Y.SBPSY_l&&!(l.scalefac[r]<e.pretab[r]&&-2!=l.scalefac[r]);r++);if(r==Y.SBPSY_l){for(r=11;r<Y.SBPSY_l;r++)l.scalefac[r]>0&&(l.scalefac[r]-=e.pretab[r]);l.preflag=f=1}}for(i=0;i<4;i++)s.scfsi[n][i]=0;for(2==t.mode_gr&&1==a&&s.tt[0][n].block_type!=Y.SHORT_TYPE&&s.tt[1][n].block_type!=Y.SHORT_TYPE&&(y(n,s),f=0),r=0;r<l.sfbmax;r++)-2==l.scalefac[r]&&(l.scalefac[r]=0);0!=f&&(2==t.mode_gr?this.scale_bitcount(l):this.scale_bitcount_lsf(t,l))};var A=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],k=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],R=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(t){var a,n,s,r=0,i=0,o=t.scalefac;if(t.block_type==Y.SHORT_TYPE)s=A,0!=t.mixed_block_flag&&(s=k);else if(s=R,0==t.preflag){for(n=11;n<Y.SBPSY_l&&!(o[n]<e.pretab[n]);n++);if(n==Y.SBPSY_l)for(t.preflag=1,n=11;n<Y.SBPSY_l;n++)o[n]-=e.pretab[n]}for(n=0;n<t.sfbdivide;n++)r<o[n]&&(r=o[n]);for(;n<t.sfbmax;n++)i<o[n]&&(i=o[n]);for(t.part2_length=O.LARGE_BITS,a=0;a<16;a++)r<d[a]&&i<g[a]&&t.part2_length>s[a]&&(t.part2_length=s[a],t.scalefac_compress=a);return t.part2_length==O.LARGE_BITS};var B=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(t,a){var n,s,i,o,_,l,f,c,u=r(4),h=a.scalefac;for(n=0!=a.preflag?2:0,f=0;f<4;f++)u[f]=0;if(a.block_type==Y.SHORT_TYPE){s=1;var b=e.nr_of_sfb_block[n][s];for(c=0,i=0;i<4;i++)for(o=b[i]/3,f=0;f<o;f++,c++)for(_=0;_<3;_++)h[3*c+_]>u[i]&&(u[i]=h[3*c+_])}else for(s=0,b=e.nr_of_sfb_block[n][s],c=0,i=0;i<4;i++)for(o=b[i],f=0;f<o;f++,c++)h[c]>u[i]&&(u[i]=h[c]);for(l=!1,i=0;i<4;i++)u[i]>B[n][i]&&(l=!0);if(!l){var p,m,v,d;for(a.sfb_partition_table=e.nr_of_sfb_block[n][s],i=0;i<4;i++)a.slen[i]=T[u[i]];switch(p=a.slen[0],m=a.slen[1],v=a.slen[2],d=a.slen[3],n){case 0:a.scalefac_compress=(5*p+m<<4)+(v<<2)+d;break;case 1:a.scalefac_compress=400+(5*p+m<<2)+v;break;case 2:a.scalefac_compress=500+3*p+m}}if(!l)for(a.part2_length=0,i=0;i<4;i++)a.part2_length+=a.slen[i]*a.sfb_partition_table[i];return l};var T=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(e){for(var t=2;t<=576;t+=2){for(var a,s=0;e.scalefac_band.l[++s]<t;);for(a=n[s][0];e.scalefac_band.l[a+1]>t;)a--;for(a<0&&(a=n[s][0]),e.bv_scf[t-2]=a,a=n[s][1];e.scalefac_band.l[a+e.bv_scf[t-2]+2]>t;)a--;a<0&&(a=n[s][1]),e.bv_scf[t-1]=a}}}function w(){}function M(){function e(e,t,a,n,s,r,i,o,_,l,f,c,u,h){this.quant_comp=t,this.quant_comp_s=a,this.safejoint=n,this.nsmsfix=s,this.st_lrm=r,this.st_s=i,this.nsbass=o,this.scale=_,this.masking_adj=l,this.ath_lower=f,this.ath_curve=c,this.interch=u,this.sfscale=h}var t;function n(e,t,n){a()}this.setModules=function(e){t=e};var s=[new e(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new e(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new e(24,9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new e(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new e(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new e(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8e-4,1),new e(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8e-4,1),new e(80,9,9,0,0,6.6,145,0,.95,0,0,8,7e-4,1),new e(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6e-4,1),new e(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5e-4,1),new e(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2e-4,1),new e(160,9,9,1,1.79,6,135,0,.95,-2,5,3.5,0,1),new e(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new e(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new e(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new e(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];function r(e,a,n){var r=a,i=t.nearestBitrateFullIndex(a);if(e.VBR=v.vbr_abr,e.VBR_mean_bitrate_kbps=r,e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320),e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.brate=e.VBR_mean_bitrate_kbps,e.VBR_mean_bitrate_kbps>320&&(e.disable_reservoir=!0),s[i].safejoint>0&&(e.exp_nspsytune=2|e.exp_nspsytune),s[i].sfscale>0&&(e.internal_flags.noise_shaping=2),Math.abs(s[i].nsbass)>0){var o=int(4*s[i].nsbass);o<0&&(o+=64),e.exp_nspsytune=e.exp_nspsytune|o<<2}return 0!=n?e.quant_comp=s[i].quant_comp:Math.abs(e.quant_comp- -1)>0||(e.quant_comp=s[i].quant_comp),0!=n?e.quant_comp_short=s[i].quant_comp_s:Math.abs(e.quant_comp_short- -1)>0||(e.quant_comp_short=s[i].quant_comp_s),0!=n?e.msfix=s[i].nsmsfix:Math.abs(e.msfix- -1)>0||(e.msfix=s[i].nsmsfix),0!=n?e.internal_flags.nsPsy.attackthre=s[i].st_lrm:Math.abs(e.internal_flags.nsPsy.attackthre- -1)>0||(e.internal_flags.nsPsy.attackthre=s[i].st_lrm),0!=n?e.internal_flags.nsPsy.attackthre_s=s[i].st_s:Math.abs(e.internal_flags.nsPsy.attackthre_s- -1)>0||(e.internal_flags.nsPsy.attackthre_s=s[i].st_s),0!=n?e.scale=s[i].scale:Math.abs(e.scale- -1)>0||(e.scale=s[i].scale),0!=n?e.maskingadjust=s[i].masking_adj:Math.abs(e.maskingadjust-0)>0||(e.maskingadjust=s[i].masking_adj),s[i].masking_adj>0?0!=n?e.maskingadjust_short=.9*s[i].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=.9*s[i].masking_adj):0!=n?e.maskingadjust_short=1.1*s[i].masking_adj:Math.abs(e.maskingadjust_short-0)>0||(e.maskingadjust_short=1.1*s[i].masking_adj),0!=n?e.ATHlower=-s[i].ath_lower/10:Math.abs(10*-e.ATHlower-0)>0||(e.ATHlower=-s[i].ath_lower/10),0!=n?e.ATHcurve=s[i].ath_curve:Math.abs(e.ATHcurve- -1)>0||(e.ATHcurve=s[i].ath_curve),0!=n?e.interChRatio=s[i].interch:Math.abs(e.interChRatio- -1)>0||(e.interChRatio=s[i].interch),a}this.apply_preset=function(e,t,a){switch(t){case Q.R3MIX:t=Q.V3,e.VBR=v.vbr_mtrh;break;case Q.MEDIUM:t=Q.V4,e.VBR=v.vbr_rh;break;case Q.MEDIUM_FAST:t=Q.V4,e.VBR=v.vbr_mtrh;break;case Q.STANDARD:t=Q.V2,e.VBR=v.vbr_rh;break;case Q.STANDARD_FAST:t=Q.V2,e.VBR=v.vbr_mtrh;break;case Q.EXTREME:t=Q.V0,e.VBR=v.vbr_rh;break;case Q.EXTREME_FAST:t=Q.V0,e.VBR=v.vbr_mtrh;break;case Q.INSANE:return t=320,e.preset=t,r(e,t,a),e.VBR=v.vbr_off,t}switch(e.preset=t,t){case Q.V9:return n(e,9,a),t;case Q.V8:return n(e,8,a),t;case Q.V7:return n(e,7,a),t;case Q.V6:return n(e,6,a),t;case Q.V5:return n(e,5,a),t;case Q.V4:return n(e,4,a),t;case Q.V3:return n(e,3,a),t;case Q.V2:return n(e,2,a),t;case Q.V1:return n(e,1,a),t;case Q.V0:return n(e,0,a),t}return 8<=t&&t<=320?r(e,t,a):(e.preset=0,t)}}function y(){var e;this.setModules=function(t){e=t},this.ResvFrameBegin=function(t,n){var s,r=t.internal_flags,i=r.l3_side,o=e.getframebits(t);n.bits=(o-8*r.sideinfo_len)/r.mode_gr;var _=2048*r.mode_gr-8;t.brate>320?a():(s=11520,t.strict_ISO&&a()),r.ResvMax=s-o,r.ResvMax>_&&(r.ResvMax=_),(r.ResvMax<0||t.disable_reservoir)&&(r.ResvMax=0);var l=n.bits*r.mode_gr+Math.min(r.ResvSize,r.ResvMax);return l>s&&(l=s),i.resvDrain_pre=0,null!=r.pinfo&&a(),l},this.ResvMaxBits=function(e,t,a,n){var s,r=e.internal_flags,i=r.ResvSize,o=r.ResvMax;0!=n&&(i+=t),1&r.substep_shaping&&(o*=.9),a.bits=t,10*i>9*o?(s=i-9*o/10,a.bits+=s,r.substep_shaping|=128):(s=0,r.substep_shaping&=127,e.disable_reservoir||1&r.substep_shaping||(a.bits-=.1*t));var _=i<6*r.ResvMax/10?i:6*r.ResvMax/10;return(_-=s)<0&&(_=0),_},this.ResvAdjust=function(e,t){e.ResvSize-=t.part2_3_length+t.part2_length},this.ResvFrameEnd=function(e,t){var a,n=e.l3_side;e.ResvSize+=t*e.mode_gr;var s=0;n.resvDrain_post=0,n.resvDrain_pre=0,0!=(a=e.ResvSize%8)&&(s+=a),(a=e.ResvSize-s-e.ResvMax)>0&&(s+=a);var r=Math.min(8*n.main_data_begin,s)/8;n.resvDrain_pre+=8*r,s-=8*r,e.ResvSize-=8*r,n.main_data_begin-=r,n.resvDrain_post+=s,e.ResvSize-=s}}function A(){this.setModules=function(e,t,a){};var e=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];function t(t,a){return a=a>>8^e[255&(a^t)]}this.updateMusicCRC=function(e,a,n,s){for(var r=0;r<s;++r)e[0]=t(a[n+r],e[0])}}function k(){var e=this,t=null,s=null;this.setModules=function(e,a,n,r){t=n,s=r};var i=null,o=0,_=0,l=0;function f(e){h.arraycopy(e.header[e.w_ptr].buf,0,i,_,e.sideinfo_len),_+=e.sideinfo_len,o+=8*e.sideinfo_len,e.w_ptr=e.w_ptr+1&K.MAX_HEADER_BUF-1}function c(e,t,a){for(;a>0;){var n;0==l&&(l=8,_++,e.header[e.w_ptr].write_timing==o&&f(e),i[_]=0),a-=n=Math.min(a,l),l-=n,i[_]|=t>>a<<l,o+=n}}function b(e,a){var n,s=e.internal_flags;if(a>=8&&(c(s,76,8),a-=8),a>=8&&(c(s,65,8),a-=8),a>=8&&(c(s,77,8),a-=8),a>=8&&(c(s,69,8),a-=8),a>=32){var r=t.getLameShortVersion();if(a>=32)for(n=0;n<r.length&&a>=8;++n)a-=8,c(s,r.charCodeAt(n),8)}for(;a>=1;a-=1)c(s,s.ancillary_flag,1),s.ancillary_flag^=e.disable_reservoir?0:1}function p(e,t,a){for(var n=e.header[e.h_ptr].ptr;a>0;){var s=Math.min(a,8-(7&n));a-=s,e.header[e.h_ptr].buf[n>>3]|=t>>a<<8-(7&n)-s,n+=s}e.header[e.h_ptr].ptr=n}function m(e,t){var n,s,r,i=e.internal_flags;if(n=i.l3_side,i.header[i.h_ptr].ptr=0,u.fill(i.header[i.h_ptr].buf,0,i.sideinfo_len,0),e.out_samplerate<16e3?p(i,4094,12):p(i,4095,12),p(i,e.version,1),p(i,1,2),p(i,e.error_protection?0:1,1),p(i,i.bitrate_index,4),p(i,i.samplerate_index,2),p(i,i.padding,1),p(i,e.extension,1),p(i,e.mode.ordinal(),2),p(i,i.mode_ext,2),p(i,e.copyright,1),p(i,e.original,1),p(i,e.emphasis,2),e.error_protection&&p(i,0,16),1==e.version){for(p(i,n.main_data_begin,9),2==i.channels_out?p(i,n.private_bits,3):p(i,n.private_bits,5),r=0;r<i.channels_out;r++){var o;for(o=0;o<4;o++)p(i,n.scfsi[r][o],1)}for(s=0;s<2;s++)for(r=0;r<i.channels_out;r++)p(i,(_=n.tt[s][r]).part2_3_length+_.part2_length,12),p(i,_.big_values/2,9),p(i,_.global_gain,8),p(i,_.scalefac_compress,4),_.block_type!=Y.NORM_TYPE?(p(i,1,1),p(i,_.block_type,2),p(i,_.mixed_block_flag,1),14==_.table_select[0]&&(_.table_select[0]=16),p(i,_.table_select[0],5),14==_.table_select[1]&&(_.table_select[1]=16),p(i,_.table_select[1],5),p(i,_.subblock_gain[0],3),p(i,_.subblock_gain[1],3),p(i,_.subblock_gain[2],3)):(p(i,0,1),14==_.table_select[0]&&(_.table_select[0]=16),p(i,_.table_select[0],5),14==_.table_select[1]&&(_.table_select[1]=16),p(i,_.table_select[1],5),14==_.table_select[2]&&(_.table_select[2]=16),p(i,_.table_select[2],5),p(i,_.region0_count,4),p(i,_.region1_count,3)),p(i,_.preflag,1),p(i,_.scalefac_scale,1),p(i,_.count1table_select,1)}else for(p(i,n.main_data_begin,8),p(i,n.private_bits,i.channels_out),s=0,r=0;r<i.channels_out;r++){var _;p(i,(_=n.tt[s][r]).part2_3_length+_.part2_length,12),p(i,_.big_values/2,9),p(i,_.global_gain,8),p(i,_.scalefac_compress,9),_.block_type!=Y.NORM_TYPE?(p(i,1,1),p(i,_.block_type,2),p(i,_.mixed_block_flag,1),14==_.table_select[0]&&(_.table_select[0]=16),p(i,_.table_select[0],5),14==_.table_select[1]&&(_.table_select[1]=16),p(i,_.table_select[1],5),p(i,_.subblock_gain[0],3),p(i,_.subblock_gain[1],3),p(i,_.subblock_gain[2],3)):(p(i,0,1),14==_.table_select[0]&&(_.table_select[0]=16),p(i,_.table_select[0],5),14==_.table_select[1]&&(_.table_select[1]=16),p(i,_.table_select[1],5),14==_.table_select[2]&&(_.table_select[2]=16),p(i,_.table_select[2],5),p(i,_.region0_count,4),p(i,_.region1_count,3)),p(i,_.scalefac_scale,1),p(i,_.count1table_select,1)}e.error_protection&&a();var l=i.h_ptr;i.h_ptr=l+1&K.MAX_HEADER_BUF-1,i.header[i.h_ptr].write_timing=i.header[l].write_timing+t,i.h_ptr,i.w_ptr}function v(e,t){var a,n=x.ht[t.count1table_select+32],s=0,r=t.big_values,i=t.big_values;for(a=(t.count1-t.big_values)/4;a>0;--a){var o=0,_=0;0!=t.l3_enc[r+0]&&(_+=8,t.xr[i+0]<0&&o++),0!=t.l3_enc[r+1]&&(_+=4,o*=2,t.xr[i+1]<0&&o++),0!=t.l3_enc[r+2]&&(_+=2,o*=2,t.xr[i+2]<0&&o++),0!=t.l3_enc[r+3]&&(_++,o*=2,t.xr[i+3]<0&&o++),r+=4,i+=4,c(e,o+n.table[_],n.hlen[_]),s+=n.hlen[_]}return s}function d(e,t,a,n,s){var r=x.ht[t],i=0;if(0==t)return i;for(var o=a;o<n;o+=2){var _=0,l=0,f=r.xlen,u=r.xlen,h=0,b=s.l3_enc[o],p=s.l3_enc[o+1];0!=b&&(s.xr[o]<0&&h++,_--),t>15&&(b>14&&(h|=b-15<<1,l=f,b=15),p>14&&(h<<=f,h|=p-15,l+=f,p=15),u=16),0!=p&&(h<<=1,s.xr[o+1]<0&&h++,_--),b=b*u+p,l-=_,_+=r.hlen[b],c(e,r.table[b],_),c(e,h,l),i+=_+l}return i}function g(e,t){var a=3*e.scalefac_band.s[3];a>t.big_values&&(a=t.big_values);var n=d(e,t.table_select[0],0,a,t);return n+=d(e,t.table_select[1],a,t.big_values,t)}function w(e,t){var a,n,s,r;a=t.big_values;var i=t.region0_count+1;return s=e.scalefac_band.l[i],i+=t.region1_count+1,s>a&&(s=a),(r=e.scalefac_band.l[i])>a&&(r=a),n=d(e,t.table_select[0],0,s,t),n+=d(e,t.table_select[1],s,r,t),n+=d(e,t.table_select[2],r,a,t)}function M(e){var t,a,n,s,r=0,i=e.internal_flags,o=i.l3_side;if(1==e.version)for(t=0;t<2;t++)for(a=0;a<i.channels_out;a++){var _=o.tt[t][a],l=S.slen1_tab[_.scalefac_compress],f=S.slen2_tab[_.scalefac_compress];for(s=0,n=0;n<_.sfbdivide;n++)-1!=_.scalefac[n]&&(c(i,_.scalefac[n],l),s+=l);for(;n<_.sfbmax;n++)-1!=_.scalefac[n]&&(c(i,_.scalefac[n],f),s+=f);_.block_type==Y.SHORT_TYPE?s+=g(i,_):s+=w(i,_),r+=s+=v(i,_)}else for(t=0,a=0;a<i.channels_out;a++){var u,h,b=0;if(s=0,n=0,h=0,(_=o.tt[t][a]).block_type==Y.SHORT_TYPE){for(;h<4;h++){var p=_.sfb_partition_table[h]/3,m=_.slen[h];for(u=0;u<p;u++,n++)c(i,Math.max(_.scalefac[3*n+0],0),m),c(i,Math.max(_.scalefac[3*n+1],0),m),c(i,Math.max(_.scalefac[3*n+2],0),m),b+=3*m}s+=g(i,_)}else{for(;h<4;h++)for(p=_.sfb_partition_table[h],m=_.slen[h],u=0;u<p;u++,n++)c(i,Math.max(_.scalefac[n],0),m),b+=m;s+=w(i,_)}r+=b+(s+=v(i,_))}return r}function y(){this.total=0}function A(t,n){var s,r,i,l=t.internal_flags;return l.w_ptr,-1==(i=l.h_ptr-1)&&(i=K.MAX_HEADER_BUF-1),s=l.header[i].write_timing-o,n.total=s,s>=0&&a(),s+=r=e.getframebits(t),n.total+=r,n.total%8!=0?n.total=1+n.total/8:n.total=n.total/8,n.total+=_+1,s}this.getframebits=function(e){var t,a=e.internal_flags;return t=0!=a.bitrate_index?x.bitrate_table[e.version][a.bitrate_index]:e.brate,8*(0|72e3*(e.version+1)*t/e.out_samplerate+a.padding)},this.flush_bitstream=function(e){var t,n,s=e.internal_flags,r=s.h_ptr-1;-1==r&&(r=K.MAX_HEADER_BUF-1),t=s.l3_side,(n=A(e,new y))<0||(b(e,n),s.ResvSize=0,t.main_data_begin=0,s.findReplayGain&&a(),s.findPeakSample&&a())},this.format_bitstream=function(e){var t,a=e.internal_flags;t=a.l3_side;var n=this.getframebits(e);b(e,t.resvDrain_pre),m(e,n);var s=8*a.sideinfo_len;if(s+=M(e),b(e,t.resvDrain_post),s+=t.resvDrain_post,t.main_data_begin+=(n-s)/8,A(e,new y),a.ResvSize,8*t.main_data_begin!=a.ResvSize&&(a.ResvSize=8*t.main_data_begin),o>1e9){var r;for(r=0;r<K.MAX_HEADER_BUF;++r)a.header[r].write_timing-=o;o=0}return 0},this.copy_buffer=function(e,t,n,o,f){var c=_+1;if(c<=0)return 0;if(0!=o&&c>o)return-1;if(h.arraycopy(i,0,t,n,c),_=-1,l=0,0!=f){var u=r(1);u[0]=e.nMusicCRC,s.updateMusicCRC(u,t,n,c),e.nMusicCRC=u[0],c>0&&(e.VBR_seek_table.nBytesWritten+=c),e.decode_on_the_fly&&a()}return c},this.init_bit_stream_w=function(e){i=n(Q.LAME_MAXMP3BUFFER),e.h_ptr=e.w_ptr=0,e.header[e.h_ptr].write_timing=0,_=-1,l=0,o=0}}function R(e,t,a,n){this.xlen=e,this.linmax=t,this.table=a,this.hlen=n}m.MAX_VALUE=34028235e31,v.vbr_off=new v(0),v.vbr_mt=new v(1),v.vbr_rh=new v(2),v.vbr_abr=new v(3),v.vbr_mtrh=new v(4),v.vbr_default=v.vbr_mtrh,d.STEREO=new d(0),d.JOINT_STEREO=new d(1),d.DUAL_CHANNEL=new d(2),d.MONO=new d(3),d.NOT_SET=new d(4),w.STEPS_per_dB=100,w.MAX_dB=120,w.GAIN_NOT_ENOUGH_SAMPLES=-24601,w.GAIN_ANALYSIS_ERROR=0,w.GAIN_ANALYSIS_OK=1,w.INIT_GAIN_ANALYSIS_ERROR=0,w.INIT_GAIN_ANALYSIS_OK=1,w.YULE_ORDER=10,w.MAX_ORDER=w.YULE_ORDER,w.MAX_SAMP_FREQ=48e3,w.RMS_WINDOW_TIME_NUMERATOR=1,w.RMS_WINDOW_TIME_DENOMINATOR=20,w.MAX_SAMPLES_PER_WINDOW=w.MAX_SAMP_FREQ*w.RMS_WINDOW_TIME_NUMERATOR/w.RMS_WINDOW_TIME_DENOMINATOR+1,A.NUMTOCENTRIES=100,A.MAXFRAMESIZE=2880,k.EQ=function(e,t){return Math.abs(e)>Math.abs(t)?Math.abs(e-t)<=1e-6*Math.abs(e):Math.abs(e-t)<=1e-6*Math.abs(t)},k.NEQ=function(e,t){return!k.EQ(e,t)};var x={};function B(e){this.bits=e}function T(){this.over_noise=0,this.tot_noise=0,this.max_noise=0,this.over_count=0,this.over_SSD=0,this.bits=0}function E(){this.setModules=function(e,t){}}function C(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=i(Y.SBMAX_l),this.s=i(Y.SBMAX_s),this.psfb21=i(Y.PSFB21),this.psfb12=i(Y.PSFB12),this.cb_l=i(Y.CBANDS),this.cb_s=i(Y.CBANDS),this.eql_w=i(Y.BLKSIZE/2)}function I(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=d.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null}function L(e){var t=e;this.quantize=t,this.iteration_loop=function(e,t,n,s){var o=e.internal_flags,_=i(V.SFBMAX),l=i(576),f=r(2),c=0,u=o.l3_side,h=new B(c);this.quantize.rv.ResvFrameBegin(e,h),c=h.bits;for(var b=0;b<o.mode_gr;b++){this.quantize.qupvt.on_pe(e,t,f,c,b,b),o.mode_ext==Y.MPG_MD_MS_LR&&a();for(var p=0;p<o.channels_out;p++){var m,v,d=u.tt[b][p];d.block_type!=Y.SHORT_TYPE?(m=0,v=o.PSY.mask_adjust-m):(m=0,v=o.PSY.mask_adjust_short-m),o.masking_lower=Math.pow(10,.1*v),this.quantize.init_outer_loop(o,d),this.quantize.init_xrpow(o,d,l)&&(this.quantize.qupvt.calc_xmin(e,s[b][p],d,_),this.quantize.outer_loop(e,d,_,l,p,f[p])),this.quantize.iteration_finish_one(o,b,p)}}this.quantize.rv.ResvFrameEnd(o,c)}}function P(){}function H(e,t,a,n){this.l=r(1+Y.SBMAX_l),this.s=r(1+Y.SBMAX_s),this.psfb21=r(1+Y.PSFB21),this.psfb12=r(1+Y.PSFB12);var s=this.l,i=this.s;4==arguments.length&&(this.arrL=arguments[0],this.arrS=arguments[1],this.arr21=arguments[2],this.arr12=arguments[3],h.arraycopy(this.arrL,0,s,0,Math.min(this.arrL.length,this.l.length)),h.arraycopy(this.arrS,0,i,0,Math.min(this.arrS.length,this.s.length)),h.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),h.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function O(){var t=null,n=null,s=null;function o(e){return p[e+O.Q_MAX2]}this.setModules=function(e,a,r){t=e,n=a,s=r},this.IPOW20=function(e){return d[e]};var _=2220446049250313e-31,l=O.IXMAX_VAL+2,f=O.Q_MAX,c=O.Q_MAX2,u=(O.LARGE_BITS,100);this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var h=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=h,this.sfBandIndex=[new H([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new H([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var p=i(f+c+1),d=i(f),g=i(l),S=i(l);function w(e,t){var a=s.ATHformula(t,e);return a-=u,a=Math.pow(10,a/10+e.ATHlower)}function M(t){for(var n=t.internal_flags.ATH.l,s=t.internal_flags.ATH.psfb21,r=t.internal_flags.ATH.s,i=t.internal_flags.ATH.psfb12,o=t.internal_flags,_=t.out_samplerate,l=0;l<Y.SBMAX_l;l++){var f=o.scalefac_band.l[l],c=o.scalefac_band.l[l+1];n[l]=m.MAX_VALUE;for(var u=f;u<c;u++){var h=w(t,u*_/1152);n[l]=Math.min(n[l],h)}}for(l=0;l<Y.PSFB21;l++)for(f=o.scalefac_band.psfb21[l],c=o.scalefac_band.psfb21[l+1],s[l]=m.MAX_VALUE,u=f;u<c;u++)h=w(t,u*_/1152),s[l]=Math.min(s[l],h);for(l=0;l<Y.SBMAX_s;l++){for(f=o.scalefac_band.s[l],c=o.scalefac_band.s[l+1],r[l]=m.MAX_VALUE,u=f;u<c;u++)h=w(t,u*_/384),r[l]=Math.min(r[l],h);r[l]*=o.scalefac_band.s[l+1]-o.scalefac_band.s[l]}for(l=0;l<Y.PSFB12;l++){for(f=o.scalefac_band.psfb12[l],c=o.scalefac_band.psfb12[l+1],i[l]=m.MAX_VALUE,u=f;u<c;u++)h=w(t,u*_/384),i[l]=Math.min(i[l],h);i[l]*=o.scalefac_band.s[13]-o.scalefac_band.s[12]}t.noATH&&a(),o.ATH.floor=10*e(w(t,-1))}function y(e){this.s=e}this.adj43=S,this.iteration_init=function(e){var a,n=e.internal_flags,s=n.l3_side;if(0==n.iteration_init_init){for(n.iteration_init_init=1,s.main_data_begin=0,M(e),g[0]=0,a=1;a<l;a++)g[a]=Math.pow(a,4/3);for(a=0;a<l-1;a++)S[a]=a+1-Math.pow(.5*(g[a]+g[a+1]),.75);for(S[a]=.5,a=0;a<f;a++)d[a]=Math.pow(2,-.1875*(a-210));for(a=0;a<=f+c;a++)p[a]=Math.pow(2,.25*(a-210-c));var r,i,o,_;for(t.huffman_init(n),(a=e.exp_nspsytune>>2&63)>=32&&(a-=64),r=Math.pow(10,a/4/10),(a=e.exp_nspsytune>>8&63)>=32&&(a-=64),i=Math.pow(10,a/4/10),(a=e.exp_nspsytune>>14&63)>=32&&(a-=64),o=Math.pow(10,a/4/10),(a=e.exp_nspsytune>>20&63)>=32&&(a-=64),_=o*Math.pow(10,a/4/10),a=0;a<Y.SBMAX_l;a++)u=a<=6?r:a<=13?i:a<=20?o:_,n.nsPsy.longfact[a]=u;for(a=0;a<Y.SBMAX_s;a++){var u;u=a<=5?r:a<=10?i:a<=11?o:_,n.nsPsy.shortfact[a]=u}}},this.on_pe=function(e,t,s,i,o,_){var l,f,c=e.internal_flags,u=0,h=r(2),b=new B(u),p=n.ResvMaxBits(e,i,b,_),m=(u=b.bits)+p;for(m>K.MAX_BITS_PER_GRANULE&&(m=K.MAX_BITS_PER_GRANULE),l=0,f=0;f<c.channels_out;++f)s[f]=Math.min(K.MAX_BITS_PER_CHANNEL,u/c.channels_out),h[f]=0|s[f]*t[o][f]/700-s[f],h[f]>3*i/4&&(h[f]=3*i/4),h[f]<0&&(h[f]=0),h[f]+s[f]>K.MAX_BITS_PER_CHANNEL&&(h[f]=Math.max(0,K.MAX_BITS_PER_CHANNEL-s[f])),l+=h[f];if(l>p)for(f=0;f<c.channels_out;++f)h[f]=p*h[f]/l;for(f=0;f<c.channels_out;++f)s[f]+=h[f],p-=h[f];for(l=0,f=0;f<c.channels_out;++f)l+=s[f];return l>K.MAX_BITS_PER_GRANULE&&a(),m},this.athAdjust=function(e,t,a){var n=90.30873362,s=94.82444863,r=b.FAST_LOG10_X(t,10),i=e*e,o=0;return r-=a,i>1e-20&&(o=1+b.FAST_LOG10_X(i,10/n)),o<0&&(o=0),r*=o,r+=a+n-s,Math.pow(10,.1*r)},this.calc_xmin=function(e,t,n,s){var r,i=0,o=e.internal_flags,l=0,f=0,c=o.ATH,u=n.xr,h=e.VBR==v.vbr_mtrh?1:0,b=o.masking_lower;for(e.VBR!=v.vbr_mtrh&&e.VBR!=v.vbr_mt||(b=1),r=0;r<n.psy_lmax;r++){y=(M=e.VBR==v.vbr_rh||e.VBR==v.vbr_mtrh?athAdjust(c.adjust,c.l[r],c.floor):c.adjust*c.l[r])/(g=n.width[r]),A=_,T=g>>1,B=0;do{B+=E=u[l]*u[l],A+=E<y?E:y,B+=C=u[++l]*u[l],A+=C<y?C:y,l++}while(--T>0);B>M&&f++,r==Y.SBPSY_l&&a(),0!=h&&(M=A),e.ATHonly||(R=t.en.l[r])>0&&(x=B*t.thm.l[r]*b/R,0!=h&&(x*=o.nsPsy.longfact[r]),M<x&&(M=x)),s[i++]=0!=h?M:M*o.nsPsy.longfact[r]}var p=575;if(n.block_type!=Y.SHORT_TYPE)for(var m=576;0!=m--&&k.EQ(u[m],0);)p=m;n.max_nonzero_coeff=p;for(var d=n.sfb_smin;r<n.psymax;d++,r+=3){var g,S,w;for(w=e.VBR==v.vbr_rh||e.VBR==v.vbr_mtrh?athAdjust(c.adjust,c.s[d],c.floor):c.adjust*c.s[d],g=n.width[r],S=0;S<3;S++){var M,y,A,R,x,B=0,T=g>>1;y=w/g,A=_;do{var E,C;B+=E=u[l]*u[l],A+=E<y?E:y,B+=C=u[++l]*u[l],A+=C<y?C:y,l++}while(--T>0);B>w&&f++,d==Y.SBPSY_s&&a(),M=0!=h?A:w,e.ATHonly||e.ATHshort||(R=t.en.s[d][S])>0&&(x=B*t.thm.s[d][S]*b/R,0!=h&&(x*=o.nsPsy.shortfact[d]),M<x&&(M=x)),s[i++]=0!=h?M:M*o.nsPsy.shortfact[d]}e.useTemporal&&(s[i-3]>s[i-3+1]&&(s[i-3+1]+=(s[i-3]-s[i-3+1])*o.decay),s[i-3+1]>s[i-3+2]&&(s[i-3+2]+=(s[i-3+1]-s[i-3+2])*o.decay))}return f},this.calc_noise_core=function(e,t,a,n){var s=0,r=t.s,o=e.l3_enc;if(r>e.count1)for(;0!=a--;)l=e.xr[r],r++,s+=l*l,l=e.xr[r],r++,s+=l*l;else if(r>e.big_values){var _=i(2);for(_[0]=0,_[1]=n;0!=a--;)l=Math.abs(e.xr[r])-_[o[r]],r++,s+=l*l,l=Math.abs(e.xr[r])-_[o[r]],r++,s+=l*l}else for(;0!=a--;){var l;l=Math.abs(e.xr[r])-g[o[r]]*n,r++,s+=l*l,l=Math.abs(e.xr[r])-g[o[r]]*n,r++,s+=l*l}return t.s=r,s},this.calc_noise=function(e,t,a,n,s){var r,i,_=0,l=0,f=0,c=0,u=0,p=-20,m=0,v=e.scalefac,d=0;for(n.over_SSD=0,r=0;r<e.psymax;r++){var g,S=e.global_gain-(v[d++]+(0!=e.preflag?h[r]:0)<<e.scalefac_scale+1)-8*e.subblock_gain[e.window[r]],w=0;if(null!=s&&s.step[r]==S)w=s.noise[r],m+=e.width[r],a[_++]=w/t[l++],w=s.noise_log[r];else{var M,A=o(S);i=e.width[r]>>1,m+e.width[r]>e.max_nonzero_coeff&&(i=(M=e.max_nonzero_coeff-m+1)>0?M>>1:0);var k=new y(m);w=this.calc_noise_core(e,k,i,A),m=k.s,null!=s&&(s.step[r]=S,s.noise[r]=w),w=a[_++]=w/t[l++],w=b.FAST_LOG10(Math.max(w,1e-20)),null!=s&&(s.noise_log[r]=w)}null!=s&&(s.global_gain=e.global_gain),u+=w,w>0&&(g=Math.max(0|10*w+.5,1),n.over_SSD+=g*g,f++,c+=w),p=Math.max(p,w)}return n.over_count=f,n.tot_noise=u,n.over_noise=c,n.max_noise=p,f}}function N(){this.global_gain=0,this.sfb_count1=0,this.step=r(39),this.noise=i(39),this.noise_log=i(39)}function D(){this.xr=i(576),this.l3_enc=r(576),this.scalefac=r(V.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=r(3),this.subblock_gain=r(4),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=r(V.SFBMAX),this.window=r(V.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=r(4),this.max_nonzero_coeff=0;var e=this;function t(e){return new Int32Array(e)}function a(e){return new Float32Array(e)}this.assign=function(n){e.xr=a(n.xr),e.l3_enc=t(n.l3_enc),e.scalefac=t(n.scalefac),e.xrpow_max=n.xrpow_max,e.part2_3_length=n.part2_3_length,e.big_values=n.big_values,e.count1=n.count1,e.global_gain=n.global_gain,e.scalefac_compress=n.scalefac_compress,e.block_type=n.block_type,e.mixed_block_flag=n.mixed_block_flag,e.table_select=t(n.table_select),e.subblock_gain=t(n.subblock_gain),e.region0_count=n.region0_count,e.region1_count=n.region1_count,e.preflag=n.preflag,e.scalefac_scale=n.scalefac_scale,e.count1table_select=n.count1table_select,e.part2_length=n.part2_length,e.sfb_lmax=n.sfb_lmax,e.sfb_smin=n.sfb_smin,e.psy_lmax=n.psy_lmax,e.sfbmax=n.sfbmax,e.psymax=n.psymax,e.sfbdivide=n.sfbdivide,e.width=t(n.width),e.window=t(n.window),e.count1bits=n.count1bits,e.sfb_partition_table=n.sfb_partition_table.slice(0),e.slen=t(n.slen),e.max_nonzero_coeff=n.max_nonzero_coeff}}x.t1HB=[1,1,1,0],x.t2HB=[1,2,1,3,1,1,3,2,0],x.t3HB=[3,2,1,1,1,1,3,2,0],x.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],x.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],x.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],x.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],x.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],x.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],x.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],x.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],x.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],x.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],x.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],x.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],x.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],x.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],x.t1l=[1,4,3,5],x.t2l=[1,4,7,4,5,7,6,7,8],x.t3l=[2,3,7,4,4,7,6,7,8],x.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],x.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],x.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],x.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],x.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],x.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],x.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],x.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],x.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],x.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],x.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],x.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],x.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],x.t32l=[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],x.t33l=[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8],x.ht=[new R(0,0,null,null),new R(2,0,x.t1HB,x.t1l),new R(3,0,x.t2HB,x.t2l),new R(3,0,x.t3HB,x.t3l),new R(0,0,null,null),new R(4,0,x.t5HB,x.t5l),new R(4,0,x.t6HB,x.t6l),new R(6,0,x.t7HB,x.t7l),new R(6,0,x.t8HB,x.t8l),new R(6,0,x.t9HB,x.t9l),new R(8,0,x.t10HB,x.t10l),new R(8,0,x.t11HB,x.t11l),new R(8,0,x.t12HB,x.t12l),new R(16,0,x.t13HB,x.t13l),new R(0,0,null,x.t16_5l),new R(16,0,x.t15HB,x.t15l),new R(1,1,x.t16HB,x.t16l),new R(2,3,x.t16HB,x.t16l),new R(3,7,x.t16HB,x.t16l),new R(4,15,x.t16HB,x.t16l),new R(6,63,x.t16HB,x.t16l),new R(8,255,x.t16HB,x.t16l),new R(10,1023,x.t16HB,x.t16l),new R(13,8191,x.t16HB,x.t16l),new R(4,15,x.t24HB,x.t24l),new R(5,31,x.t24HB,x.t24l),new R(6,63,x.t24HB,x.t24l),new R(7,127,x.t24HB,x.t24l),new R(8,255,x.t24HB,x.t24l),new R(9,511,x.t24HB,x.t24l),new R(11,2047,x.t24HB,x.t24l),new R(13,8191,x.t24HB,x.t24l),new R(0,0,x.t32HB,x.t32l),new R(0,0,x.t33HB,x.t33l)],x.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366],x.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296],x.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369],x.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]],x.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]],x.scfsi_band=[0,6,11,16,21],O.Q_MAX=257,O.Q_MAX2=116,O.LARGE_BITS=1e5,O.IXMAX_VAL=8206;var V={};function F(){var e,t;this.rv=null,this.qupvt=null;var n,s=new E;function r(e,t,a,n){n=0;for(var s=0;s<=a;++s){var r=Math.abs(e.xr[s]);n+=r,t[s]=Math.sqrt(r*Math.sqrt(r)),t[s]>e.xrpow_max&&(e.xrpow_max=t[s])}return n}function o(e,a){var n=e.ATH,s=a.xr;if(a.block_type!=Y.SHORT_TYPE)for(var r=!1,i=Y.PSFB21-1;i>=0&&!r;i--){var o=e.scalefac_band.psfb21[i],_=e.scalefac_band.psfb21[i+1],l=t.athAdjust(n.adjust,n.psfb21[i],n.floor);e.nsPsy.longfact[21]>1e-12&&(l*=e.nsPsy.longfact[21]);for(var f=_-1;f>=o;f--){if(!(Math.abs(s[f])<l)){r=!0;break}s[f]=0}}else for(var c=0;c<3;c++)for(r=!1,i=Y.PSFB12-1;i>=0&&!r;i--){_=(o=3*e.scalefac_band.s[12]+(e.scalefac_band.s[13]-e.scalefac_band.s[12])*c+(e.scalefac_band.psfb12[i]-e.scalefac_band.psfb12[0]))+(e.scalefac_band.psfb12[i+1]-e.scalefac_band.psfb12[i]);var u=t.athAdjust(n.adjust,n.psfb12[i],n.floor);for(e.nsPsy.shortfact[12]>1e-12&&(u*=e.nsPsy.shortfact[12]),f=_-1;f>=o;f--){if(!(Math.abs(s[f])<u)){r=!0;break}s[f]=0}}}function _(e){this.ordinal=e}function l(e,t,s,r,i){var o,l=e.CurrentStep[r],f=!1,c=e.OldValue[r],u=_.BINSEARCH_NONE;for(t.global_gain=c,s-=t.part2_length;;){var h;if(o=n.count_bits(e,i,t,null),1==l||o==s)break;o>s?(u==_.BINSEARCH_DOWN&&(f=!0),f&&(l/=2),u=_.BINSEARCH_UP,h=l):(u==_.BINSEARCH_UP&&(f=!0),f&&(l/=2),u=_.BINSEARCH_DOWN,h=-l),t.global_gain+=h,t.global_gain<0&&a(),t.global_gain>255&&a()}for(;o>s&&t.global_gain<255;)t.global_gain++,o=n.count_bits(e,i,t,null);return e.CurrentStep[r]=c-t.global_gain>=4?4:2,e.OldValue[r]=t.global_gain,t.part2_3_length=o,o}function f(e){for(var t=0;t<e.sfbmax;t++)if(e.scalefac[t]+e.subblock_gain[e.window[t]]==0)return!1;return!0}function c(e,t,n,s,r){var i;switch(e){default:case 9:t.over_count>0?(i=n.over_SSD<=t.over_SSD,n.over_SSD==t.over_SSD&&(i=n.bits<t.bits)):i=n.max_noise<0&&10*n.max_noise+n.bits<=10*t.max_noise+t.bits;break;case 0:i=n.over_count<t.over_count||n.over_count==t.over_count&&n.over_noise<t.over_noise||n.over_count==t.over_count&&k.EQ(n.over_noise,t.over_noise)&&n.tot_noise<t.tot_noise;break;case 8:a();case 1:i=n.max_noise<t.max_noise;break;case 2:i=n.tot_noise<t.tot_noise;break;case 3:i=n.tot_noise<t.tot_noise&&n.max_noise<t.max_noise;break;case 4:i=n.max_noise<=0&&t.max_noise>.2||n.max_noise<=0&&t.max_noise<0&&t.max_noise>n.max_noise-.2&&n.tot_noise<t.tot_noise||n.max_noise<=0&&t.max_noise>0&&t.max_noise>n.max_noise-.2&&n.tot_noise<t.tot_noise+t.over_noise||n.max_noise>0&&t.max_noise>-.05&&t.max_noise>n.max_noise-.1&&n.tot_noise+n.over_noise<t.tot_noise+t.over_noise||n.max_noise>0&&t.max_noise>-.1&&t.max_noise>n.max_noise-.15&&n.tot_noise+n.over_noise+n.over_noise<t.tot_noise+t.over_noise+t.over_noise;break;case 5:i=n.over_noise<t.over_noise||k.EQ(n.over_noise,t.over_noise)&&n.tot_noise<t.tot_noise;break;case 6:i=n.over_noise<t.over_noise||k.EQ(n.over_noise,t.over_noise)&&(n.max_noise<t.max_noise||k.EQ(n.max_noise,t.max_noise)&&n.tot_noise<=t.tot_noise);break;case 7:i=n.over_count<t.over_count||n.over_noise<t.over_noise}return 0==t.over_count&&(i=i&&n.bits<t.bits),i}function b(e,t,n,s,r){var i,o=e.internal_flags;i=0==t.scalefac_scale?1.2968395546510096:1.6817928305074292;for(var _=0,l=0;l<t.sfbmax;l++)_<n[l]&&(_=n[l]);var f=o.noise_shaping_amp;switch(3==f&&a(),f){case 2:break;case 1:_>1?_=Math.pow(_,.5):_*=.95;break;default:_>1?_=1:_*=.95}var c=0;for(l=0;l<t.sfbmax;l++){var u,h=t.width[l];if(c+=h,!(n[l]<_)){for(2&o.substep_shaping&&a(),t.scalefac[l]++,u=-h;u<0;u++)s[c+u]*=i,s[c+u]>t.xrpow_max&&(t.xrpow_max=s[c+u]);if(2==o.noise_shaping_amp)return}}}function p(e,a){for(var n=1.2968395546510096,s=0,r=0;r<e.sfbmax;r++){var i=e.width[r],o=e.scalefac[r];if(0!=e.preflag&&(o+=t.pretab[r]),s+=i,1&o){o++;for(var _=-i;_<0;_++)a[s+_]*=n,a[s+_]>e.xrpow_max&&(e.xrpow_max=a[s+_])}e.scalefac[r]=o>>1}e.preflag=0,e.scalefac_scale=1}function m(e,a,n){var s,r=a.scalefac;for(s=0;s<a.sfb_lmax;s++)if(r[s]>=16)return!0;for(var i=0;i<3;i++){var o=0,_=0;for(s=a.sfb_lmax+i;s<a.sfbdivide;s+=3)o<r[s]&&(o=r[s]);for(;s<a.sfbmax;s+=3)_<r[s]&&(_=r[s]);if(!(o<16&&_<8)){if(a.subblock_gain[i]>=7)return!0;a.subblock_gain[i]++;var l=e.scalefac_band.l[a.sfb_lmax];for(s=a.sfb_lmax+i;s<a.sfbmax;s+=3){var f=a.width[s],c=r[s];if((c-=4>>a.scalefac_scale)>=0)r[s]=c,l+=3*f;else{r[s]=0;var u=210+(c<<a.scalefac_scale+1);b=t.IPOW20(u),l+=f*(i+1);for(var h=-f;h<0;h++)n[l+h]*=b,n[l+h]>a.xrpow_max&&(a.xrpow_max=n[l+h]);l+=f*(3-i-1)}}var b=t.IPOW20(202);for(l+=a.width[s]*(i+1),h=-a.width[s];h<0;h++)n[l+h]*=b,n[l+h]>a.xrpow_max&&(a.xrpow_max=n[l+h])}}return!1}function d(e,t,a,s,r){var i=e.internal_flags;b(e,t,a,s,r);var o=f(t);return!(o||(o=2==i.mode_gr?n.scale_bitcount(t):n.scale_bitcount_lsf(i,t))&&(i.noise_shaping>1&&(u.fill(i.pseudohalf,0),0==t.scalefac_scale?(p(t,s),o=!1):t.block_type==Y.SHORT_TYPE&&i.subblock_gain>0&&(o=m(i,t,s)||f(t))),o||(o=2==i.mode_gr?n.scale_bitcount(t):n.scale_bitcount_lsf(i,t)),o))}this.setModules=function(a,r,i,o){e=r,this.rv=r,t=i,this.qupvt=i,n=o,s.setModules(t,n)},this.init_xrpow=function(e,t,a){var n=0,s=0|t.max_nonzero_coeff;if(t.xrpow_max=0,u.fill(a,s,576,0),(n=r(t,a,s,n))>1e-20){var i=0;2&e.substep_shaping&&(i=1);for(var o=0;o<t.psymax;o++)e.pseudohalf[o]=i;return!0}return u.fill(t.l3_enc,0,576,0),!1},this.init_outer_loop=function(e,n){n.part2_3_length=0,n.big_values=0,n.count1=0,n.global_gain=210,n.scalefac_compress=0,n.table_select[0]=0,n.table_select[1]=0,n.table_select[2]=0,n.subblock_gain[0]=0,n.subblock_gain[1]=0,n.subblock_gain[2]=0,n.subblock_gain[3]=0,n.region0_count=0,n.region1_count=0,n.preflag=0,n.scalefac_scale=0,n.count1table_select=0,n.part2_length=0,n.sfb_lmax=Y.SBPSY_l,n.sfb_smin=Y.SBPSY_s,n.psy_lmax=e.sfb21_extra?Y.SBMAX_l:Y.SBPSY_l,n.psymax=n.psy_lmax,n.sfbmax=n.sfb_lmax,n.sfbdivide=11;for(var s=0;s<Y.SBMAX_l;s++)n.width[s]=e.scalefac_band.l[s+1]-e.scalefac_band.l[s],n.window[s]=3;if(n.block_type==Y.SHORT_TYPE){var r=i(576);n.sfb_smin=0,n.sfb_lmax=0,0!=n.mixed_block_flag&&a(),n.psymax=n.sfb_lmax+3*((e.sfb21_extra?Y.SBMAX_s:Y.SBPSY_s)-n.sfb_smin),n.sfbmax=n.sfb_lmax+3*(Y.SBPSY_s-n.sfb_smin),n.sfbdivide=n.sfbmax-18,n.psy_lmax=n.sfb_lmax;var _=e.scalefac_band.l[n.sfb_lmax];for(h.arraycopy(n.xr,0,r,0,576),s=n.sfb_smin;s<Y.SBMAX_s;s++)for(var l=e.scalefac_band.s[s],f=e.scalefac_band.s[s+1],c=0;c<3;c++)for(var b=l;b<f;b++)n.xr[_++]=r[3*b+c];var p=n.sfb_lmax;for(s=n.sfb_smin;s<Y.SBMAX_s;s++)n.width[p]=n.width[p+1]=n.width[p+2]=e.scalefac_band.s[s+1]-e.scalefac_band.s[s],n.window[p]=0,n.window[p+1]=1,n.window[p+2]=2,p+=3}n.count1bits=0,n.sfb_partition_table=t.nr_of_sfb_block[0][0],n.slen[0]=0,n.slen[1]=0,n.slen[2]=0,n.slen[3]=0,n.max_nonzero_coeff=575,u.fill(n.scalefac,0),o(e,n)},_.BINSEARCH_NONE=new _(0),_.BINSEARCH_UP=new _(1),_.BINSEARCH_DOWN=new _(2),this.outer_loop=function(e,s,r,o,_,f){var u=e.internal_flags,b=new D,p=i(576),m=i(V.SFBMAX),g=new T,S=new N,w=9999999,M=!1,y=!1,A=0;if(l(u,s,f,_,o),0==u.noise_shaping)return 100;t.calc_noise(s,r,m,g,S),g.bits=s.part2_3_length,b.assign(s);var k=0;for(h.arraycopy(o,0,p,0,576);!M;){do{var R,x=new T,B=255;if(R=2&u.substep_shaping?20:3,u.sfb21_extra&&a(),!d(e,b,m,o,y))break;0!=b.scalefac_scale&&(B=254);var E=f-b.part2_length;if(E<=0)break;for(;(b.part2_3_length=n.count_bits(u,o,b,S))>E&&b.global_gain<=B;)b.global_gain++;if(b.global_gain>B)break;if(0==g.over_count){for(;(b.part2_3_length=n.count_bits(u,o,b,S))>w&&b.global_gain<=B;)b.global_gain++;if(b.global_gain>B)break}if(t.calc_noise(b,r,m,x,S),x.bits=b.part2_3_length,0!=(c(s.block_type!=Y.SHORT_TYPE?e.quant_comp:e.quant_comp_short,g,x,b,m)?1:0))w=s.part2_3_length,g=x,s.assign(b),k=0,h.arraycopy(o,0,p,0,576);else if(0==u.full_outer_loop){if(++k>R&&0==g.over_count)break;if(3==u.noise_shaping_amp&&y&&k>30)break;if(3==u.noise_shaping_amp&&y&&b.global_gain-A>15)break}}while(b.global_gain+b.scalefac_scale<255);3==u.noise_shaping_amp?a():M=!0}return e.VBR==v.vbr_rh||e.VBR==v.vbr_mtrh?h.arraycopy(p,0,o,0,576):1&u.substep_shaping&&a(),g.over_count},this.iteration_finish_one=function(t,a,s){var r=t.l3_side,i=r.tt[a][s];n.best_scalefac_store(t,a,s,r),1==t.use_best_huffman&&n.best_huffman_divide(t,i),e.ResvAdjust(t,i)}}function j(){var e=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*b.SQRT2*.5/2384e-9,.017876148*b.SQRT2*.5/2384e-9,.003134727*b.SQRT2*.5/2384e-9,.002457142*b.SQRT2*.5/2384e-9,971317e-9*b.SQRT2*.5/2384e-9,218868e-9*b.SQRT2*.5/2384e-9,101566e-9*b.SQRT2*.5/2384e-9,13828e-9*b.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,-29.20218120805369],t=12,n=36,s=[[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,940084909404969e-27,6423305872147839e-28,2382191739347918e-28,5456116108943412e-27,4878985199565852e-27,4240448995017367e-27,3559909094758252e-27,2858043359288075e-27,2156177623817898e-27,1475637723558783e-27,8371015190102974e-28,2599706096327376e-28,-5456116108943412e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758252e-27,-2858043359288076e-27,-2156177623817898e-27,-1475637723558783e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347923e-28,-6423305872147843e-28,-9400849094049696e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049694e-28,-642330587214784e-27,-2382191739347918e-28],[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,9400849094049688e-28,6423305872147841e-28,2382191739347918e-28,5456116108943413e-27,4878985199565852e-27,4240448995017367e-27,3559909094758253e-27,2858043359288075e-27,2156177623817898e-27,1475637723558782e-27,8371015190102975e-28,2599706096327376e-28,-5461314069809755e-27,-4921085770524055e-27,-4343405037091838e-27,-3732668368707687e-27,-3093523840190885e-27,-2430835727329465e-27,-1734679010007751e-27,-974825365660928e-27,-2797435120168326e-28,0,0,0,0,0,0,-2283748241799531e-28,-4037858874020686e-28,-2146547464825323e-28],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2283748241799531e-28,4037858874020686e-28,2146547464825323e-28,5461314069809755e-27,4921085770524055e-27,4343405037091838e-27,3732668368707687e-27,3093523840190885e-27,2430835727329466e-27,1734679010007751e-27,974825365660928e-27,2797435120168326e-28,-5456116108943413e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758253e-27,-2858043359288075e-27,-2156177623817898e-27,-1475637723558782e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347913e-28,-6423305872147834e-28,-9400849094049688e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049688e-28,-6423305872147841e-28,-2382191739347918e-28]],r=s[Y.SHORT_TYPE],o=s[Y.SHORT_TYPE],_=s[Y.SHORT_TYPE],l=s[Y.SHORT_TYPE],f=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function c(t,a,n){for(var s,r,i,o=10,_=a+238-14-286,l=-15;l<0;l++){var f,c,u;f=e[o+-10],c=t[_+-224]*f,u=t[a+224]*f,f=e[o+-9],c+=t[_+-160]*f,u+=t[a+160]*f,f=e[o+-8],c+=t[_+-96]*f,u+=t[a+96]*f,f=e[o+-7],c+=t[_+-32]*f,u+=t[a+32]*f,f=e[o+-6],c+=t[_+32]*f,u+=t[a+-32]*f,f=e[o+-5],c+=t[_+96]*f,u+=t[a+-96]*f,f=e[o+-4],c+=t[_+160]*f,u+=t[a+-160]*f,f=e[o+-3],c+=t[_+224]*f,u+=t[a+-224]*f,f=e[o+-2],c+=t[a+-256]*f,u-=t[_+256]*f,f=e[o+-1],c+=t[a+-192]*f,u-=t[_+192]*f,f=e[o+0],c+=t[a+-128]*f,u-=t[_+128]*f,f=e[o+1],c+=t[a+-64]*f,u-=t[_+64]*f,f=e[o+2],c+=t[a+0]*f,u-=t[_+0]*f,f=e[o+3],c+=t[a+64]*f,u-=t[_+-64]*f,f=e[o+4],c+=t[a+128]*f,u-=t[_+-128]*f,f=e[o+5],c+=t[a+192]*f,f=(u-=t[_+-192]*f)-(c*=e[o+6]),n[30+2*l]=u+c,n[31+2*l]=e[o+7]*f,o+=18,a--,_++}u=t[a+-16]*e[o+-10],c=t[a+-32]*e[o+-2],u+=(t[a+-48]-t[a+16])*e[o+-9],c+=t[a+-96]*e[o+-1],u+=(t[a+-80]+t[a+48])*e[o+-8],c+=t[a+-160]*e[o+0],u+=(t[a+-112]-t[a+80])*e[o+-7],c+=t[a+-224]*e[o+1],u+=(t[a+-144]+t[a+112])*e[o+-6],c-=t[a+32]*e[o+2],u+=(t[a+-176]-t[a+144])*e[o+-5],c-=t[a+96]*e[o+3],u+=(t[a+-208]+t[a+176])*e[o+-4],c-=t[a+160]*e[o+4],u+=(t[a+-240]-t[a+208])*e[o+-3],s=(c-=t[a+224])-u,r=c+u,u=n[14],c=n[15]-u,n[31]=r+u,n[30]=s+c,n[15]=s-c,n[14]=r-u,i=n[28]-n[0],n[0]+=n[28],n[28]=i*e[o+-36+7],i=n[29]-n[1],n[1]+=n[29],n[29]=i*e[o+-36+7],i=n[26]-n[2],n[2]+=n[26],n[26]=i*e[o+-72+7],i=n[27]-n[3],n[3]+=n[27],n[27]=i*e[o+-72+7],i=n[24]-n[4],n[4]+=n[24],n[24]=i*e[o+-108+7],i=n[25]-n[5],n[5]+=n[25],n[25]=i*e[o+-108+7],i=n[22]-n[6],n[6]+=n[22],n[22]=i*b.SQRT2,i=n[23]-n[7],n[7]+=n[23],n[23]=i*b.SQRT2-n[7],n[7]-=n[6],n[22]-=n[7],n[23]-=n[22],i=n[6],n[6]=n[31]-i,n[31]=n[31]+i,i=n[7],n[7]=n[30]-i,n[30]=n[30]+i,i=n[22],n[22]=n[15]-i,n[15]=n[15]+i,i=n[23],n[23]=n[14]-i,n[14]=n[14]+i,i=n[20]-n[8],n[8]+=n[20],n[20]=i*e[o+-180+7],i=n[21]-n[9],n[9]+=n[21],n[21]=i*e[o+-180+7],i=n[18]-n[10],n[10]+=n[18],n[18]=i*e[o+-216+7],i=n[19]-n[11],n[11]+=n[19],n[19]=i*e[o+-216+7],i=n[16]-n[12],n[12]+=n[16],n[16]=i*e[o+-252+7],i=n[17]-n[13],n[13]+=n[17],n[17]=i*e[o+-252+7],i=-n[20]+n[24],n[20]+=n[24],n[24]=i*e[o+-216+7],i=-n[21]+n[25],n[21]+=n[25],n[25]=i*e[o+-216+7],i=n[4]-n[8],n[4]+=n[8],n[8]=i*e[o+-216+7],i=n[5]-n[9],n[5]+=n[9],n[9]=i*e[o+-216+7],i=n[0]-n[12],n[0]+=n[12],n[12]=i*e[o+-72+7],i=n[1]-n[13],n[1]+=n[13],n[13]=i*e[o+-72+7],i=n[16]-n[28],n[16]+=n[28],n[28]=i*e[o+-72+7],i=-n[17]+n[29],n[17]+=n[29],n[29]=i*e[o+-72+7],i=b.SQRT2*(n[2]-n[10]),n[2]+=n[10],n[10]=i,i=b.SQRT2*(n[3]-n[11]),n[3]+=n[11],n[11]=i,i=b.SQRT2*(-n[18]+n[26]),n[18]+=n[26],n[26]=i-n[18],i=b.SQRT2*(-n[19]+n[27]),n[19]+=n[27],n[27]=i-n[19],i=n[2],n[19]-=n[3],n[3]-=i,n[2]=n[31]-i,n[31]+=i,i=n[3],n[11]-=n[19],n[18]-=i,n[3]=n[30]-i,n[30]+=i,i=n[18],n[27]-=n[11],n[19]-=i,n[18]=n[15]-i,n[15]+=i,i=n[19],n[10]-=i,n[19]=n[14]-i,n[14]+=i,i=n[10],n[11]-=i,n[10]=n[23]-i,n[23]+=i,i=n[11],n[26]-=i,n[11]=n[22]-i,n[22]+=i,i=n[26],n[27]-=i,n[26]=n[7]-i,n[7]+=i,i=n[27],n[27]=n[6]-i,n[6]+=i,i=b.SQRT2*(n[0]-n[4]),n[0]+=n[4],n[4]=i,i=b.SQRT2*(n[1]-n[5]),n[1]+=n[5],n[5]=i,i=b.SQRT2*(n[16]-n[20]),n[16]+=n[20],n[20]=i,i=b.SQRT2*(n[17]-n[21]),n[17]+=n[21],n[21]=i,i=-b.SQRT2*(n[8]-n[12]),n[8]+=n[12],n[12]=i-n[8],i=-b.SQRT2*(n[9]-n[13]),n[9]+=n[13],n[13]=i-n[9],i=-b.SQRT2*(n[25]-n[29]),n[25]+=n[29],n[29]=i-n[25],i=-b.SQRT2*(n[24]+n[28]),n[24]-=n[28],n[28]=i-n[24],i=n[24]-n[16],n[24]=i,i=n[20]-i,n[20]=i,i=n[28]-i,n[28]=i,i=n[25]-n[17],n[25]=i,i=n[21]-i,n[21]=i,i=n[29]-i,n[29]=i,i=n[17]-n[1],n[17]=i,i=n[9]-i,n[9]=i,i=n[25]-i,n[25]=i,i=n[5]-i,n[5]=i,i=n[21]-i,n[21]=i,i=n[13]-i,n[13]=i,i=n[29]-i,n[29]=i,i=n[1]-n[0],n[1]=i,i=n[16]-i,n[16]=i,i=n[17]-i,n[17]=i,i=n[8]-i,n[8]=i,i=n[9]-i,n[9]=i,i=n[24]-i,n[24]=i,i=n[25]-i,n[25]=i,i=n[4]-i,n[4]=i,i=n[5]-i,n[5]=i,i=n[20]-i,n[20]=i,i=n[21]-i,n[21]=i,i=n[12]-i,n[12]=i,i=n[13]-i,n[13]=i,i=n[28]-i,n[28]=i,i=n[29]-i,n[29]=i,i=n[0],n[0]+=n[31],n[31]-=i,i=n[1],n[1]+=n[30],n[30]-=i,i=n[16],n[16]+=n[15],n[15]-=i,i=n[17],n[17]+=n[14],n[14]-=i,i=n[8],n[8]+=n[23],n[23]-=i,i=n[9],n[9]+=n[22],n[22]-=i,i=n[24],n[24]+=n[7],n[7]-=i,i=n[25],n[25]+=n[6],n[6]-=i,i=n[4],n[4]+=n[27],n[27]-=i,i=n[5],n[5]+=n[26],n[26]-=i,i=n[20],n[20]+=n[11],n[11]-=i,i=n[21],n[21]+=n[10],n[10]-=i,i=n[12],n[12]+=n[19],n[19]-=i,i=n[13],n[13]+=n[18],n[18]-=i,i=n[28],n[28]+=n[3],n[3]-=i,i=n[29],n[29]+=n[2],n[2]-=i}function p(e,t){for(var a=0;a<3;a++){var n,r,i,o,_,l;r=(o=e[t+6]*s[Y.SHORT_TYPE][0]-e[t+15])+(n=e[t+0]*s[Y.SHORT_TYPE][2]-e[t+9]),i=o-n,_=(o=e[t+15]*s[Y.SHORT_TYPE][0]+e[t+6])+(n=e[t+9]*s[Y.SHORT_TYPE][2]+e[t+0]),l=-o+n,n=2069978111953089e-26*(e[t+3]*s[Y.SHORT_TYPE][1]-e[t+12]),o=2069978111953089e-26*(e[t+12]*s[Y.SHORT_TYPE][1]+e[t+3]),e[t+0]=190752519173728e-25*r+n,e[t+15]=190752519173728e-25*-_+o,i=.8660254037844387*i*1907525191737281e-26,_=.5*_*1907525191737281e-26+o,e[t+3]=i-_,e[t+6]=i+_,r=.5*r*1907525191737281e-26-n,l=.8660254037844387*l*1907525191737281e-26,e[t+9]=r+l,e[t+12]=r-l,t++}}function m(e,t,a){var n,s,r,i,_,l,f,c,u,h,b,p,m,v,d,g,S,w;r=a[17]-a[9],_=a[15]-a[11],l=a[14]-a[12],f=a[0]+a[8],c=a[1]+a[7],u=a[2]+a[6],h=a[3]+a[5],e[t+17]=f+u-h-(c-a[4]),s=(f+u-h)*o[19]+(c-a[4]),n=(r-_-l)*o[18],e[t+5]=n+s,e[t+6]=n-s,i=(a[16]-a[10])*o[18],c=c*o[19]+a[4],n=r*o[12]+i+_*o[13]+l*o[14],s=-f*o[16]+c-u*o[17]+h*o[15],e[t+1]=n+s,e[t+2]=n-s,n=r*o[13]-i-_*o[14]+l*o[12],s=-f*o[17]+c-u*o[15]+h*o[16],e[t+9]=n+s,e[t+10]=n-s,n=r*o[14]-i+_*o[12]-l*o[13],s=f*o[15]-c+u*o[16]-h*o[17],e[t+13]=n+s,e[t+14]=n-s,b=a[8]-a[0],m=a[6]-a[2],v=a[5]-a[3],d=a[17]+a[9],g=a[16]+a[10],S=a[15]+a[11],w=a[14]+a[12],e[t+0]=d+S+w+(g+a[13]),n=(d+S+w)*o[19]-(g+a[13]),s=(b-m+v)*o[18],e[t+11]=n+s,e[t+12]=n-s,p=(a[7]-a[1])*o[18],g=a[13]-g*o[19],n=d*o[15]-g+S*o[16]+w*o[17],s=b*o[14]+p+m*o[12]+v*o[13],e[t+3]=n+s,e[t+4]=n-s,n=-d*o[17]+g-S*o[15]-w*o[16],s=b*o[13]+p-m*o[14]-v*o[12],e[t+7]=n+s,e[t+8]=n-s,n=-d*o[16]+g-S*o[17]-w*o[15],s=b*o[12]-p+m*o[13]-v*o[14],e[t+15]=n+s,e[t+16]=n-s}this.mdct_sub48=function(e,o,b){for(var v=o,d=286,g=0;g<e.channels_out;g++){for(var S=0;S<e.mode_gr;S++){for(var w,M=e.l3_side.tt[S][g],y=M.xr,A=0,k=e.sb_sample[g][1-S],R=0,x=0;x<9;x++)for(c(v,d,k[R]),c(v,d+32,k[R+1]),R+=2,d+=64,w=1;w<32;w+=2)k[R-1][w]*=-1;for(w=0;w<32;w++,A+=18){var B=M.block_type,T=e.sb_sample[g][S],E=e.sb_sample[g][1-S];if(0!=M.mixed_block_flag&&w<2&&(B=0),e.amp_filter[w]<1e-12)u.fill(y,A+0,A+18,0);else if(e.amp_filter[w]<1&&a(),B==Y.SHORT_TYPE){for(x=-t/4;x<0;x++){var C=s[Y.SHORT_TYPE][x+3];y[A+3*x+9]=T[9+x][f[w]]*C-T[8-x][f[w]],y[A+3*x+18]=T[14-x][f[w]]*C+T[15+x][f[w]],y[A+3*x+10]=T[15+x][f[w]]*C-T[14-x][f[w]],y[A+3*x+19]=E[2-x][f[w]]*C+E[3+x][f[w]],y[A+3*x+11]=E[3+x][f[w]]*C-E[2-x][f[w]],y[A+3*x+20]=E[8-x][f[w]]*C+E[9+x][f[w]]}p(y,A)}else{var I=i(18);for(x=-n/4;x<0;x++){var L,P;L=s[B][x+27]*E[x+9][f[w]]+s[B][x+36]*E[8-x][f[w]],P=s[B][x+9]*T[x+9][f[w]]-s[B][x+18]*T[8-x][f[w]],I[x+9]=L-P*r[3+x+9],I[x+18]=L*r[3+x+9]+P}m(y,A,I)}if(B!=Y.SHORT_TYPE&&0!=w)for(x=7;x>=0;--x){var H,O;H=y[A+x]*_[20+x]+y[A+-1-x]*l[28+x],O=y[A+x]*l[28+x]-y[A+-1-x]*_[20+x],y[A+-1-x]=H,y[A+x]=O}}}if(v=b,d=286,1==e.mode_gr)for(var N=0;N<18;N++)h.arraycopy(e.sb_sample[g][1][N],0,e.sb_sample[g][0][N],0,32)}}}function X(){this.thm=new G,this.en=new G}function Y(){Y.FFTOFFSET;var e=Y.MPG_MD_MS_LR,t=null;this.psy=null;var n=null,s=null;this.setModules=function(e,a,r,i){t=e,this.psy=a,n=a,s=i};var o=new j;function l(e){var t,n;if(0!=e.ATH.useAdjust)if(n=e.loudness_sq[0][0],t=e.loudness_sq[1][0],2==e.channels_out?a():(n+=n,t+=t),2==e.mode_gr&&(n=Math.max(n,t)),n*=.5,(n*=e.ATH.aaSensitivityP)>.03125)e.ATH.adjust>=1?e.ATH.adjust=1:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=1;else{var s=31.98*n+625e-6;e.ATH.adjust>=s?(e.ATH.adjust*=.075*s+.925,e.ATH.adjust<s&&(e.ATH.adjust=s)):e.ATH.adjustLimit>=s?e.ATH.adjust=s:e.ATH.adjust<e.ATH.adjustLimit&&(e.ATH.adjust=e.ATH.adjustLimit),e.ATH.adjustLimit=s}else e.ATH.adjust=1}function f(e){var t,n;for(e.bitrate_stereoMode_Hist[e.bitrate_index][4]++,e.bitrate_stereoMode_Hist[15][4]++,2==e.channels_out&&a(),t=0;t<e.mode_gr;++t)for(n=0;n<e.channels_out;++n){var s=0|e.l3_side.tt[t][n].block_type;0!=e.l3_side.tt[t][n].mixed_block_flag&&(s=4),e.bitrate_blockType_Hist[e.bitrate_index][s]++,e.bitrate_blockType_Hist[e.bitrate_index][5]++,e.bitrate_blockType_Hist[15][s]++,e.bitrate_blockType_Hist[15][5]++}}function u(e,t){var a,n,s=e.internal_flags;if(0==s.lame_encode_frame_init){var r,_,l=i(2014),f=i(2014);for(s.lame_encode_frame_init=1,r=0,_=0;r<286+576*(1+s.mode_gr);++r)r<576*s.mode_gr?(l[r]=0,2==s.channels_out&&(f[r]=0)):(l[r]=t[0][_],2==s.channels_out&&(f[r]=t[1][_]),++_);for(n=0;n<s.mode_gr;n++)for(a=0;a<s.channels_out;a++)s.l3_side.tt[n][a].block_type=Y.SHORT_TYPE;o.mdct_sub48(s,l,f)}}this.lame_encode_mp3_frame=function(i,h,b,p,m,g){var S,w=c([2,2]);w[0][0]=new X,w[0][1]=new X,w[1][0]=new X,w[1][1]=new X;var M,y=c([2,2]);y[0][0]=new X,y[0][1]=new X,y[1][0]=new X,y[1][1]=new X;var A,k,R,x=[null,null],B=i.internal_flags,T=_([2,4]),E=[.5,.5],C=[[0,0],[0,0]],I=[[0,0],[0,0]];if(x[0]=h,x[1]=b,0==B.lame_encode_frame_init&&u(i,x),B.padding=0,(B.slot_lag-=B.frac_SpF)<0&&(B.slot_lag+=i.out_samplerate,B.padding=1),0!=B.psymodel){var L,P=[null,null],H=0,O=r(2);for(R=0;R<B.mode_gr;R++){for(k=0;k<B.channels_out;k++)P[k]=x[k],H=576+576*R-Y.FFTOFFSET;if(i.VBR==v.vbr_mtrh||i.VBR==v.vbr_mt?a():L=n.L3psycho_anal_ns(i,P,H,R,w,y,C[R],I[R],T[R],O),0!=L)return-4;for(i.mode==d.JOINT_STEREO&&a(),k=0;k<B.channels_out;k++){var N=B.l3_side.tt[R][k];N.block_type=O[k],N.mixed_block_flag=0}}}else a();if(l(B),o.mdct_sub48(B,x[0],x[1]),B.mode_ext=Y.MPG_MD_LR_LR,i.force_ms?B.mode_ext=Y.MPG_MD_MS_LR:i.mode==d.JOINT_STEREO&&a(),B.mode_ext==e?(M=y,A=I):(M=w,A=C),i.analysis&&null!=B.pinfo&&a(),i.VBR==v.vbr_off||i.VBR==v.vbr_abr){var D,V;for(D=0;D<18;D++)B.nsPsy.pefirbuf[D]=B.nsPsy.pefirbuf[D+1];for(V=0,R=0;R<B.mode_gr;R++)for(k=0;k<B.channels_out;k++)V+=A[R][k];for(B.nsPsy.pefirbuf[18]=V,V=B.nsPsy.pefirbuf[9],D=0;D<9;D++)V+=(B.nsPsy.pefirbuf[D]+B.nsPsy.pefirbuf[18-D])*Y.fircoef[D];for(V=3350*B.mode_gr*B.channels_out/V,R=0;R<B.mode_gr;R++)for(k=0;k<B.channels_out;k++)A[R][k]*=V}return B.iteration_loop.iteration_loop(i,A,E,M),t.format_bitstream(i),S=t.copy_buffer(B,p,m,g,1),i.bWriteVbrTag&&s.addVbrFrame(i),i.analysis&&null!=B.pinfo&&a(),f(B),S}}function z(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0}function q(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[r(4),r(4)];for(var e=0;e<2;e++)for(var t=0;t<2;t++)this.tt[e][t]=new D}function G(){this.l=i(Y.SBMAX_l),this.s=_([Y.SBMAX_s,3]);var e=this;this.assign=function(t){h.arraycopy(t.l,0,e.l,0,Y.SBMAX_l);for(var a=0;a<Y.SBMAX_s;a++)for(var n=0;n<3;n++)e.s[a][n]=t.s[a][n]}}function U(){this.last_en_subshort=_([4,9]),this.lastAttacks=r(4),this.pefirbuf=i(19),this.longfact=i(Y.SBMAX_l),this.shortfact=i(Y.SBMAX_s),this.attackthre=0,this.attackthre_s=0}function K(){var e=40;function t(){this.write_timing=0,this.ptr=0,this.buf=n(e)}this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=_([2,K.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new q,this.ms_ratio=i(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=r(2),this.CurrentStep=r(2),this.masking_lower=0,this.bv_scf=r(576),this.pseudohalf=r(V.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*K.BPC+1),this.itime=o(2),this.sideinfo_len=0,this.sb_sample=_([2,2,18,Y.SBLIMIT]),this.amp_filter=i(32),this.header=new Array(K.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new H,this.minval_l=i(Y.CBANDS),this.minval_s=i(Y.CBANDS),this.nb_1=_([4,Y.CBANDS]),this.nb_2=_([4,Y.CBANDS]),this.nb_s1=_([4,Y.CBANDS]),this.nb_s2=_([4,Y.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=i(4),this.loudness_sq=_([2,2]),this.loudness_sq_save=i(2),this.mld_l=i(Y.SBMAX_l),this.mld_s=i(Y.SBMAX_s),this.bm_l=r(Y.SBMAX_l),this.bo_l=r(Y.SBMAX_l),this.bm_s=r(Y.SBMAX_s),this.bo_s=r(Y.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=l([Y.CBANDS,2]),this.s3ind_s=l([Y.CBANDS,2]),this.numlines_s=r(Y.CBANDS),this.numlines_l=r(Y.CBANDS),this.rnumlines_l=i(Y.CBANDS),this.mld_cb_l=i(Y.CBANDS),this.mld_cb_s=i(Y.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=i(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=r(2),this.nsPsy=new U,this.VBR_seek_table=new z,this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=l([16,5]),this.bitrate_blockType_Hist=l([16,6]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var a=0;a<this.en.length;a++)this.en[a]=new G;for(a=0;a<this.thm.length;a++)this.thm[a]=new G;for(a=0;a<this.header.length;a++)this.header[a]=new t}function Z(){var e=i(Y.BLKSIZE),t=i(Y.BLKSIZE_s/2),a=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function n(e,t,n){var s,r,i,o=0,_=t+(n<<=1);s=4;do{var l,f,c,u,h,p,m;m=s>>1,p=(h=s<<1)+(u=s),s=h<<1,i=(r=t)+m;do{y=e[r+0]-e[r+u],M=e[r+0]+e[r+u],x=e[r+h]-e[r+p],k=e[r+h]+e[r+p],e[r+h]=M-k,e[r+0]=M+k,e[r+p]=y-x,e[r+u]=y+x,y=e[i+0]-e[i+u],M=e[i+0]+e[i+u],x=b.SQRT2*e[i+p],k=b.SQRT2*e[i+h],e[i+h]=M-k,e[i+0]=M+k,e[i+p]=y-x,e[i+u]=y+x,i+=s,r+=s}while(r<_);for(f=a[o+0],l=a[o+1],c=1;c<m;c++){var v,d;v=1-2*l*l,d=2*l*f,r=t+c,i=t+u-c;do{var g,S,w,M,y,A,k,R,x,B;S=d*e[r+u]-v*e[i+u],g=v*e[r+u]+d*e[i+u],y=e[r+0]-g,M=e[r+0]+g,A=e[i+0]-S,w=e[i+0]+S,S=d*e[r+p]-v*e[i+p],g=v*e[r+p]+d*e[i+p],x=e[r+h]-g,k=e[r+h]+g,B=e[i+h]-S,R=e[i+h]+S,S=l*k-f*B,g=f*k+l*B,e[r+h]=M-g,e[r+0]=M+g,e[i+p]=A-S,e[i+u]=A+S,S=f*R-l*x,g=l*R+f*x,e[i+h]=w-g,e[i+0]=w+g,e[r+p]=y-S,e[r+u]=y+S,i+=s,r+=s}while(r<_);f=(v=f)*a[o+0]-l*a[o+1],l=v*a[o+1]+l*a[o+0]}o+=2}while(s<n)}var s=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(e,a,r,i,o){for(var _=0;_<3;_++){var l=Y.BLKSIZE_s/2,f=65535&192*(_+1),c=Y.BLKSIZE_s/8-1;do{var u,h,b,p,m,v=255&s[c<<2];h=(u=t[v]*i[r][o+v+f])-(m=t[127-v]*i[r][o+v+f+128]),u+=m,p=(b=t[v+64]*i[r][o+v+f+64])-(m=t[63-v]*i[r][o+v+f+192]),b+=m,l-=4,a[_][l+0]=u+b,a[_][l+2]=u-b,a[_][l+1]=h+p,a[_][l+3]=h-p,h=(u=t[v+1]*i[r][o+v+f+1])-(m=t[126-v]*i[r][o+v+f+129]),u+=m,p=(b=t[v+65]*i[r][o+v+f+65])-(m=t[62-v]*i[r][o+v+f+193]),b+=m,a[_][l+Y.BLKSIZE_s/2+0]=u+b,a[_][l+Y.BLKSIZE_s/2+2]=u-b,a[_][l+Y.BLKSIZE_s/2+1]=h+p,a[_][l+Y.BLKSIZE_s/2+3]=h-p}while(--c>=0);n(a[_],l,Y.BLKSIZE_s/2)}},this.fft_long=function(t,a,r,i,o){var _=Y.BLKSIZE/8-1,l=Y.BLKSIZE/2;do{var f,c,u,h,b,p=255&s[_];c=(f=e[p]*i[r][o+p])-(b=e[p+512]*i[r][o+p+512]),f+=b,h=(u=e[p+256]*i[r][o+p+256])-(b=e[p+768]*i[r][o+p+768]),u+=b,a[0+(l-=4)]=f+u,a[l+2]=f-u,a[l+1]=c+h,a[l+3]=c-h,c=(f=e[p+1]*i[r][o+p+1])-(b=e[p+513]*i[r][o+p+513]),f+=b,h=(u=e[p+257]*i[r][o+p+257])-(b=e[p+769]*i[r][o+p+769]),u+=b,a[l+Y.BLKSIZE/2+0]=f+u,a[l+Y.BLKSIZE/2+2]=f-u,a[l+Y.BLKSIZE/2+1]=c+h,a[l+Y.BLKSIZE/2+3]=c-h}while(--_>=0);n(a,l,Y.BLKSIZE/2)},this.init_fft=function(a){for(var n=0;n<Y.BLKSIZE;n++)e[n]=.42-.5*Math.cos(2*Math.PI*(n+.5)/Y.BLKSIZE)+.08*Math.cos(4*Math.PI*(n+.5)/Y.BLKSIZE);for(n=0;n<Y.BLKSIZE_s/2;n++)t[n]=.5*(1-Math.cos(2*Math.PI*(n+.5)/Y.BLKSIZE_s))}}function W(){var e=new Z,t=2.302585092994046,n=2,s=16,o=2,l=16,f=.34,c=1/217621504/(Y.BLKSIZE/2),h=.01,g=.8,S=.6,w=.3,M=3.5,y=21,A=.2302585093;function k(e,t){for(var a=0,n=0;n<Y.BLKSIZE/2;++n)a+=e[n]*t.ATH.eql_w[n];return a*=c}function R(t,n,s,r,i,o,_,l,f,c,u){var h=t.internal_flags;f<2?(e.fft_long(h,r[i],f,c,u),e.fft_short(h,o[_],f,c,u)):2==f&&a(),n[0]=r[i+0][0],n[0]*=n[0];for(var b=Y.BLKSIZE/2-1;b>=0;--b){var p=r[i+0][Y.BLKSIZE/2-b],m=r[i+0][Y.BLKSIZE/2+b];n[Y.BLKSIZE/2-b]=.5*(p*p+m*m)}for(var v=2;v>=0;--v)for(s[v][0]=o[_+0][v][0],s[v][0]*=s[v][0],b=Y.BLKSIZE_s/2-1;b>=0;--b)p=o[_+0][v][Y.BLKSIZE_s/2-b],m=o[_+0][v][Y.BLKSIZE_s/2+b],s[v][Y.BLKSIZE_s/2-b]=.5*(p*p+m*m);var d=0;for(b=11;b<Y.HBLKSIZE;b++)d+=n[b];h.tot_ener[f]=d,t.analysis&&a(),2==t.athaa_loudapprox&&f<2&&(h.loudness_sq[l][f]=h.loudness_sq_save[f],h.loudness_sq_save[f]=k(n,h))}var x,B,T,E=8,C=23,I=15,L=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749];function P(){x=Math.pow(10,(E+1)/16),B=Math.pow(10,(C+1)/16),T=Math.pow(10,I/10)}var H=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],O=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],N=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276];function D(e,t,a,n,s,r){var i,o,_;if(t>e){if(!(t<e*B))return e+t;i=t/e}else{if(e>=t*B)return e+t;i=e/t}if(e+=t,n+3<=6){if(i>=x)return e;var l=0|b.FAST_LOG10_X(i,16);return e*O[l]}return l=0|b.FAST_LOG10_X(i,16),t=0!=r?s.ATH.cb_s[a]*s.ATH.adjust:s.ATH.cb_l[a]*s.ATH.adjust,e<T*t?e>t?(o=1,l<=13&&(o=N[l]),_=b.FAST_LOG10_X(e/t,10/15),e*((H[l]-o)*_+o)):l>13?e:e*N[l]:e*H[l]}function V(e,t,a,n,s){var r,i,o=0,_=0;for(r=i=0;r<Y.SBMAX_s;++i,++r){for(var l=e.bo_s[r],f=e.npart_s,c=l<f?l:f;i<c;)o+=t[i],_+=a[i],i++;if(e.en[n].s[r][s]=o,e.thm[n].s[r][s]=_,i>=f){++r;break}var u=e.PSY.bo_s_weight[r],h=1-u;o=u*t[i],_=u*a[i],e.en[n].s[r][s]+=o,e.thm[n].s[r][s]+=_,o=h*t[i],_=h*a[i]}for(;r<Y.SBMAX_s;++r)e.en[n].s[r][s]=0,e.thm[n].s[r][s]=0}function F(e,t,a,n){var s,r,i=0,o=0;for(s=r=0;s<Y.SBMAX_l;++r,++s){for(var _=e.bo_l[s],l=e.npart_l,f=_<l?_:l;r<f;)i+=t[r],o+=a[r],r++;if(e.en[n].l[s]=i,e.thm[n].l[s]=o,r>=l){++s;break}var c=e.PSY.bo_l_weight[s],u=1-c;i=c*t[r],o=c*a[r],e.en[n].l[s]+=i,e.thm[n].l[s]+=o,i=u*t[r],o=u*a[r]}for(;s<Y.SBMAX_l;++s)e.en[n].l[s]=0,e.thm[n].l[s]=0}function j(e,t,a,n,s,r){var i,_,f=e.internal_flags;for(_=i=0;_<f.npart_s;++_){for(var c=0,u=0,h=f.numlines_s[_],b=0;b<h;++b,++i){var p=t[r][i];c+=p,u<p&&(u=p)}a[_]=c}for(i=_=0;_<f.npart_s;_++){var m=f.s3ind_s[_][0],v=f.s3_ss[i++]*a[m];for(++m;m<=f.s3ind_s[_][1];)v+=f.s3_ss[i]*a[m],++i,++m;var d=o*f.nb_s1[s][_];if(n[_]=Math.min(v,d),f.blocktype_old[1&s]==Y.SHORT_TYPE){d=l*f.nb_s2[s][_];var g=n[_];n[_]=Math.min(d,g)}f.nb_s2[s][_]=f.nb_s1[s][_],f.nb_s1[s][_]=v}for(;_<=Y.CBANDS;++_)a[_]=0,n[_]=0}function X(e,t,a,n){var s=e.internal_flags;e.short_blocks!=p.short_block_coupled||0!=t[0]&&0!=t[1]||(t[0]=t[1]=0);for(var r=0;r<s.channels_out;r++)n[r]=Y.NORM_TYPE,e.short_blocks==p.short_block_dispensed&&(t[r]=1),e.short_blocks==p.short_block_forced&&(t[r]=0),0!=t[r]?s.blocktype_old[r]==Y.SHORT_TYPE&&(n[r]=Y.STOP_TYPE):(n[r]=Y.SHORT_TYPE,s.blocktype_old[r]==Y.NORM_TYPE&&(s.blocktype_old[r]=Y.START_TYPE),s.blocktype_old[r]==Y.STOP_TYPE&&(s.blocktype_old[r]=Y.SHORT_TYPE)),a[r]=s.blocktype_old[r],s.blocktype_old[r]=n[r]}function z(e,t,a){return a>=1?e:a<=0?t:t>0?Math.pow(e/t,a)*t:0}var q=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function G(e,a){for(var n=309.07,s=0;s<Y.SBMAX_s-1;s++)for(var r=0;r<3;r++){var i=e.thm.s[s][r];if(i>0){var o=i*a,_=e.en.s[s][r];_>o&&(n+=_>1e10*o?q[s]*(10*t):q[s]*b.FAST_LOG10(_/o))}}return n}var U=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function K(e,a){for(var n=281.0575,s=0;s<Y.SBMAX_l-1;s++){var r=e.thm.l[s];if(r>0){var i=r*a,o=e.en.l[s];o>i&&(n+=o>1e10*i?U[s]*(10*t):U[s]*b.FAST_LOG10(o/i))}}return n}function W(e,t,a,n,s){var r,i;for(r=i=0;r<e.npart_l;++r){var o,_=0,l=0;for(o=0;o<e.numlines_l[r];++o,++i){var f=t[i];_+=f,l<f&&(l=f)}a[r]=_,n[r]=l,s[r]=_*e.rnumlines_l[r]}}function Q(e,t,a,n){var s=L.length-1,r=0,i=a[r]+a[r+1];for(i>0?((o=t[r])<t[r+1]&&(o=t[r+1]),(_=0|(i=20*(2*o-i)/(i*(e.numlines_l[r]+e.numlines_l[r+1]-1))))>s&&(_=s),n[r]=_):n[r]=0,r=1;r<e.npart_l-1;r++){var o,_;(i=a[r-1]+a[r]+a[r+1])>0?((o=t[r-1])<t[r]&&(o=t[r]),o<t[r+1]&&(o=t[r+1]),(_=0|(i=20*(3*o-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]+e.numlines_l[r+1]-1))))>s&&(_=s),n[r]=_):n[r]=0}(i=a[r-1]+a[r])>0?((o=t[r-1])<t[r]&&(o=t[r]),(_=0|(i=20*(2*o-i)/(i*(e.numlines_l[r-1]+e.numlines_l[r]-1))))>s&&(_=s),n[r]=_):n[r]=0}var $=[-1730326e-23,-.01703172,-1349528e-23,.0418072,-673278e-22,-.0876324,-30835e-21,.1863476,-1104424e-22,-.627638];function J(e){var t,a,n,s;return t=e,a=(t*=t>=0?3:1.5)>=.5&&t<=2.5?8*((s=t-.5)*s-2*s):0,(n=15.811389+7.5*(t+=.474)-17.5*Math.sqrt(1+t*t))<=-60?0:(t=Math.exp((a+n)*A),t/=.6609193)}function ee(e){return e<0&&(e=0),e*=.001,13*Math.atan(.76*e)+3.5*Math.atan(e*e/56.25)}function te(e,t,a,n,s,o,_,l,c,u,h,b){var p,m=i(Y.CBANDS+1),v=l/(b>15?1152:384),d=r(Y.HBLKSIZE);l/=c;var g=0,S=0;for(p=0;p<Y.CBANDS;p++){var w;for(E=ee(l*g),m[p]=l*g,w=g;ee(l*w)-E<f&&w<=c/2;w++);for(e[p]=w-g,S=p+1;g<w;)d[g++]=p;if(g>c/2){g=c/2,++p;break}}m[p]=l*g;for(var M=0;M<b;M++){var y,A,k,R,x;k=u[M],R=u[M+1],(y=0|Math.floor(.5+h*(k-.5)))<0&&(y=0),(A=0|Math.floor(.5+h*(R-.5)))>c/2&&(A=c/2),a[M]=(d[y]+d[A])/2,t[M]=d[A];var B=v*R;_[M]=(B-m[t[M]])/(m[t[M]+1]-m[t[M]]),_[M]<0?_[M]=0:_[M]>1&&(_[M]=1),x=ee(l*u[M]*h),x=Math.min(x,15.5)/15.5,o[M]=Math.pow(10,1.25*(1-Math.cos(Math.PI*x))-2.5)}g=0;for(var T=0;T<S;T++){var E,C,I=e[T];E=ee(l*g),C=ee(l*(g+I-1)),n[T]=.5*(E+C),E=ee(l*(g-.5)),C=ee(l*(g+I-.5)),s[T]=C-E,g+=I}return S}function ae(e,t,n,s,r,o){var l,f=_([Y.CBANDS,Y.CBANDS]),c=0;if(o)for(var u=0;u<t;u++)for(l=0;l<t;l++){var h=J(n[u]-n[l])*s[l];f[u][l]=h*r[u]}else a();for(u=0;u<t;u++){for(l=0;l<t&&!(f[u][l]>0);l++);for(e[u][0]=l,l=t-1;l>0&&!(f[u][l]>0);l--);e[u][1]=l,c+=e[u][1]-e[u][0]+1}var b=i(c),p=0;for(u=0;u<t;u++)for(l=e[u][0];l<=e[u][1];l++)b[p++]=f[u][l];return b}function ne(e){var t=ee(e);return t=Math.min(t,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*t))-2.5)}function se(e,t){return e<-.3&&(e=3410),e/=1e3,e=Math.max(.1,e),3.64*Math.pow(e,-.8)-6.8*Math.exp(-.6*Math.pow(e-3.4,2))+6*Math.exp(-.15*Math.pow(e-8.7,2))+.001*(.6+.04*t)*Math.pow(e,4)}this.L3psycho_anal_ns=function(e,t,o,l,f,c,h,b,p,m){var M,A,k,x,B,T,E,C,I,P=e.internal_flags,H=_([2,Y.BLKSIZE]),O=_([2,3,Y.BLKSIZE_s]),N=i(Y.CBANDS+1),q=i(Y.CBANDS+1),U=i(Y.CBANDS+2),Z=r(2),J=r(2),ee=_([2,576]),te=r(Y.CBANDS+2),ae=r(Y.CBANDS+2);for(u.fill(ae,0),M=P.channels_out,e.mode==d.JOINT_STEREO&&(M=4),I=e.VBR==v.vbr_off?0==P.ResvMax?0:P.ResvSize/P.ResvMax*.5:e.VBR==v.vbr_rh||e.VBR==v.vbr_mtrh||e.VBR==v.vbr_mt?.6:1,A=0;A<P.channels_out;A++){var ne=t[A],se=o+576-350-y+192;for(x=0;x<576;x++){var re,ie;for(re=ne[se+x+10],ie=0,B=0;B<(y-1)/2-1;B+=2)re+=$[B]*(ne[se+x+B]+ne[se+x+y-B]),ie+=$[B+1]*(ne[se+x+B+1]+ne[se+x+y-B-1]);ee[A][x]=re+ie}f[l][A].en.assign(P.en[A]),f[l][A].thm.assign(P.thm[A]),M>2&&a()}for(A=0;A<M;A++){var oe,_e=i(12),le=[0,0,0,0],fe=i(12),ce=1,ue=i(Y.CBANDS),he=i(Y.CBANDS),be=[0,0,0,0],pe=i(Y.HBLKSIZE),me=_([3,Y.HBLKSIZE_s]);for(x=0;x<3;x++)_e[x]=P.nsPsy.last_en_subshort[A][x+6],fe[x]=_e[x]/P.nsPsy.last_en_subshort[A][x+4],le[0]+=_e[x];2==A&&a();var ve=ee[1&A],de=0;for(x=0;x<9;x++){for(var ge=de+64,Se=1;de<ge;de++)Se<Math.abs(ve[de])&&(Se=Math.abs(ve[de]));P.nsPsy.last_en_subshort[A][x]=_e[x+3]=Se,le[1+x/3]+=Se,Se>_e[x+3-2]?Se/=_e[x+3-2]:Se=_e[x+3-2]>10*Se?_e[x+3-2]/(10*Se):0,fe[x+3]=Se}for(e.analysis&&a(),oe=3==A?P.nsPsy.attackthre_s:P.nsPsy.attackthre,x=0;x<12;x++)0==be[x/3]&&fe[x]>oe&&(be[x/3]=x%3+1);for(x=1;x<4;x++)(le[x-1]>le[x]?le[x-1]/le[x]:le[x]/le[x-1])<1.7&&(be[x]=0,1==x&&(be[0]=0));for(0!=be[0]&&0!=P.nsPsy.lastAttacks[A]&&(be[0]=0),3!=P.nsPsy.lastAttacks[A]&&be[0]+be[1]+be[2]+be[3]==0||(ce=0,0!=be[1]&&0!=be[0]&&(be[1]=0),0!=be[2]&&0!=be[1]&&(be[2]=0),0!=be[3]&&0!=be[2]&&(be[3]=0)),A<2?J[A]=ce:a(),p[A]=P.tot_ener[A],R(e,pe,me,H,1&A,O,1&A,l,A,t,o),W(P,pe,N,ue,he),Q(P,ue,he,te),C=0;C<3;C++){var we,Me;for(j(e,me,q,U,A,C),V(P,q,U,A,C),E=0;E<Y.SBMAX_s;E++){if(Me=P.thm[A].s[E][C],Me*=g,be[C]>=2||1==be[C+1]){var ye=0!=C?C-1:2;Se=z(P.thm[A].s[E][ye],Me,S*I),Me=Math.min(Me,Se)}1==be[C]?(ye=0!=C?C-1:2,Se=z(P.thm[A].s[E][ye],Me,w*I),Me=Math.min(Me,Se)):(0!=C&&3==be[C-1]||0==C&&3==P.nsPsy.lastAttacks[A])&&(ye=2!=C?C+1:0,Se=z(P.thm[A].s[E][ye],Me,w*I),Me=Math.min(Me,Se)),we=_e[3*C+3]+_e[3*C+4]+_e[3*C+5],6*_e[3*C+5]<we&&(Me*=.5,6*_e[3*C+4]<we&&(Me*=.5)),P.thm[A].s[E][C]=Me}}for(P.nsPsy.lastAttacks[A]=be[2],T=0,k=0;k<P.npart_l;k++){for(var Ae=P.s3ind[k][0],ke=N[Ae]*L[te[Ae]],Re=P.s3_ll[T++]*ke;++Ae<=P.s3ind[k][1];)ke=N[Ae]*L[te[Ae]],Re=D(Re,P.s3_ll[T++]*ke,Ae,Ae-k,P,0);Re*=.158489319246111,P.blocktype_old[1&A]==Y.SHORT_TYPE?U[k]=Re:U[k]=z(Math.min(Re,Math.min(n*P.nb_1[A][k],s*P.nb_2[A][k])),Re,I),P.nb_2[A][k]=P.nb_1[A][k],P.nb_1[A][k]=Re}for(;k<=Y.CBANDS;++k)N[k]=0,U[k]=0;F(P,N,U,A)}for(e.mode!=d.STEREO&&e.mode!=d.JOINT_STEREO||a(),e.mode==d.JOINT_STEREO&&a(),X(e,J,m,Z),A=0;A<M;A++){var xe,Be,Te,Ee=0;A>1?a():(xe=h,Ee=0,Be=m[A],Te=f[l][A]),Be==Y.SHORT_TYPE?xe[Ee+A]=G(Te,P.masking_lower):xe[Ee+A]=K(Te,P.masking_lower),e.analysis&&(P.pinfo.pe[l][A]=xe[Ee+A])}return 0},this.psymodel_init=function(a){var n,s,r=a.internal_flags,o=!0,_=13,l=24,f=0,c=0,u=-8.25,b=-4.5,p=i(Y.CBANDS),d=i(Y.CBANDS),g=i(Y.CBANDS),S=a.out_samplerate;switch(a.experimentalZ){default:case 0:o=!0;break;case 1:o=a.VBR!=v.vbr_mtrh&&a.VBR!=v.vbr_mt;break;case 2:o=!1;break;case 3:_=8,f=-1.75,c=-.0125,u=-8.25,b=-2.25}for(r.ms_ener_ratio_old=.25,r.blocktype_old[0]=r.blocktype_old[1]=Y.NORM_TYPE,n=0;n<4;++n){for(var w=0;w<Y.CBANDS;++w)r.nb_1[n][w]=1e20,r.nb_2[n][w]=1e20,r.nb_s1[n][w]=r.nb_s2[n][w]=1;for(var y=0;y<Y.SBMAX_l;y++)r.en[n].l[y]=1e20,r.thm[n].l[y]=1e20;for(w=0;w<3;++w){for(y=0;y<Y.SBMAX_s;y++)r.en[n].s[y][w]=1e20,r.thm[n].s[y][w]=1e20;r.nsPsy.lastAttacks[n]=0}for(w=0;w<9;w++)r.nsPsy.last_en_subshort[n][w]=10}for(r.loudness_sq_save[0]=r.loudness_sq_save[1]=0,r.npart_l=te(r.numlines_l,r.bo_l,r.bm_l,p,d,r.mld_l,r.PSY.bo_l_weight,S,Y.BLKSIZE,r.scalefac_band.l,Y.BLKSIZE/1152,Y.SBMAX_l),n=0;n<r.npart_l;n++){var A=f;p[n]>=_&&(A=c*(p[n]-_)/(l-_)+f*(l-p[n])/(l-_)),g[n]=Math.pow(10,A/10),r.numlines_l[n]>0?r.rnumlines_l[n]=1/r.numlines_l[n]:r.rnumlines_l[n]=0}for(r.s3_ll=ae(r.s3ind,r.npart_l,p,d,g,o),w=0,n=0;n<r.npart_l;n++){x=m.MAX_VALUE;for(var k=0;k<r.numlines_l[n];k++,w++){var R=S*w/(1e3*Y.BLKSIZE);B=this.ATHformula(1e3*R,a)-20,B=Math.pow(10,.1*B),x>(B*=r.numlines_l[n])&&(x=B)}r.ATH.cb_l[n]=x,(x=20*p[n]/10-20)>6&&(x=100),x<-15&&(x=-15),x-=8,r.minval_l[n]=Math.pow(10,x/10)*r.numlines_l[n]}for(r.npart_s=te(r.numlines_s,r.bo_s,r.bm_s,p,d,r.mld_s,r.PSY.bo_s_weight,S,Y.BLKSIZE_s,r.scalefac_band.s,Y.BLKSIZE_s/384,Y.SBMAX_s),w=0,n=0;n<r.npart_s;n++){var x;for(A=u,p[n]>=_&&(A=b*(p[n]-_)/(l-_)+u*(l-p[n])/(l-_)),g[n]=Math.pow(10,A/10),x=m.MAX_VALUE,k=0;k<r.numlines_s[n];k++,w++){var B;R=S*w/(1e3*Y.BLKSIZE_s),B=this.ATHformula(1e3*R,a)-20,B=Math.pow(10,.1*B),x>(B*=r.numlines_s[n])&&(x=B)}r.ATH.cb_s[n]=x,x=7*p[n]/12-7,p[n]>12&&(x*=1+3.1*Math.log(1+x)),p[n]<12&&(x*=1+2.3*Math.log(1-x)),x<-15&&(x=-15),x-=8,r.minval_s[n]=Math.pow(10,x/10)*r.numlines_s[n]}r.s3_ss=ae(r.s3ind_s,r.npart_s,p,d,g,o),P(),e.init_fft(r),r.decay=Math.exp(-1*t/(h*S/192)),s=M,2&a.exp_nspsytune&&(s=1),Math.abs(a.msfix)>0&&(s=a.msfix),a.msfix=s;for(var T=0;T<r.npart_l;T++)r.s3ind[T][1]>r.npart_l-1&&(r.s3ind[T][1]=r.npart_l-1);var E=576*r.mode_gr/S;if(r.ATH.decay=Math.pow(10,-1.2*E),r.ATH.adjust=.01,r.ATH.adjustLimit=1,-1!=a.ATHtype){var C=a.out_samplerate/Y.BLKSIZE,I=0;for(R=0,n=0;n<Y.BLKSIZE/2;++n)R+=C,r.ATH.eql_w[n]=1/Math.pow(10,this.ATHformula(R,a)/10),I+=r.ATH.eql_w[n];for(I=1/I,n=Y.BLKSIZE/2;--n>=0;)r.ATH.eql_w[n]*=I}for(T=w=0;T<r.npart_s;++T)for(n=0;n<r.numlines_s[T];++n)++w;for(T=w=0;T<r.npart_l;++T)for(n=0;n<r.numlines_l[T];++n)++w;for(w=0,n=0;n<r.npart_l;n++)R=S*(w+r.numlines_l[n]/2)/(1*Y.BLKSIZE),r.mld_cb_l[n]=ne(R),w+=r.numlines_l[n];for(;n<Y.CBANDS;++n)r.mld_cb_l[n]=1;for(w=0,n=0;n<r.npart_s;n++)R=S*(w+r.numlines_s[n]/2)/(1*Y.BLKSIZE_s),r.mld_cb_s[n]=ne(R),w+=r.numlines_s[n];for(;n<Y.CBANDS;++n)r.mld_cb_s[n]=1;return 0},this.ATHformula=function(e,t){var a;switch(t.ATHtype){case 0:a=se(e,9);break;case 1:a=se(e,-1);break;case 2:default:a=se(e,0);break;case 3:a=se(e,1)+6;break;case 4:a=se(e,t.ATHcurve)}return a}}function Q(){var e=this,t=131072;Q.V9=410,Q.V8=420,Q.V7=430,Q.V6=440,Q.V5=450,Q.V4=460,Q.V3=470,Q.V2=480,Q.V1=490,Q.V0=500,Q.R3MIX=1e3,Q.STANDARD=1001,Q.EXTREME=1002,Q.INSANE=1003,Q.STANDARD_FAST=1004,Q.EXTREME_FAST=1005,Q.MEDIUM=1006,Q.MEDIUM_FAST=1007;var n,s,r,o,_,c=16384+t;Q.LAME_MAXMP3BUFFER=c;var u,h,b=new W;function m(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=i(Y.SBMAX_l),this.bo_s_weight=i(Y.SBMAX_s)}function g(){this.lowerlimit=0}function S(e,t){this.lowpass=t}this.enc=new Y,this.setModules=function(e,t,a,i,l,f,c,p,m){n=e,s=t,r=a,o=i,_=l,u=f,h=p,this.enc.setModules(s,b,o,u)};var M=4294479419;function y(e){var t;return e.class_id=M,t=e.internal_flags=new K,e.mode=d.NOT_SET,e.original=1,e.in_samplerate=44100,e.num_channels=2,e.num_samples=-1,e.bWriteVbrTag=!0,e.quality=-1,e.short_blocks=null,t.subblock_gain=-1,e.lowpassfreq=0,e.highpassfreq=0,e.lowpasswidth=-1,e.highpasswidth=-1,e.VBR=v.vbr_off,e.VBR_q=4,e.ATHcurve=-1,e.VBR_mean_bitrate_kbps=128,e.VBR_min_bitrate_kbps=0,e.VBR_max_bitrate_kbps=0,e.VBR_hard_min=0,t.VBR_min_bitrate=1,t.VBR_max_bitrate=13,e.quant_comp=-1,e.quant_comp_short=-1,e.msfix=-1,t.resample_ratio=1,t.OldValue[0]=180,t.OldValue[1]=180,t.CurrentStep[0]=4,t.CurrentStep[1]=4,t.masking_lower=1,t.nsPsy.attackthre=-1,t.nsPsy.attackthre_s=-1,e.scale=-1,e.athaa_type=-1,e.ATHtype=-1,e.athaa_loudapprox=-1,e.athaa_sensitivity=0,e.useTemporal=null,e.interChRatio=-1,t.mf_samples_to_encode=Y.ENCDELAY+Y.POSTDELAY,e.encoder_padding=0,t.mf_size=Y.ENCDELAY-Y.MDCTDELAY,e.findReplayGain=!1,e.decode_on_the_fly=!1,t.decode_on_the_fly=!1,t.findReplayGain=!1,t.findPeakSample=!1,t.RadioGain=0,t.AudiophileGain=0,t.noclipGainChange=0,t.noclipScale=-1,e.preset=0,e.write_id3tag_automatic=!0,0}function A(e){return e>1?0:e<=0?1:Math.cos(Math.PI/2*e)}function R(e,t){switch(e){case 44100:return t.version=1,0;case 48e3:return t.version=1,1;case 32e3:return t.version=1,2;case 22050:case 11025:return t.version=0,0;case 24e3:case 12e3:return t.version=0,1;case 16e3:case 8e3:return t.version=0,2;default:return t.version=0,-1}}function B(e,t,a){a<16e3&&(t=2);for(var n=x.bitrate_table[t][1],s=2;s<=14;s++)x.bitrate_table[t][s]>0&&Math.abs(x.bitrate_table[t][s]-e)<Math.abs(n-e)&&(n=x.bitrate_table[t][s]);return n}function T(e,t,a){a<16e3&&(t=2);for(var n=0;n<=14;n++)if(x.bitrate_table[t][n]>0&&x.bitrate_table[t][n]==e)return n;return-1}function E(t,a){var n=[new S(8,2e3),new S(16,3700),new S(24,3900),new S(32,5500),new S(40,7e3),new S(48,7500),new S(56,1e4),new S(64,11e3),new S(80,13500),new S(96,15100),new S(112,15600),new S(128,17e3),new S(160,17500),new S(192,18600),new S(224,19400),new S(256,19700),new S(320,20500)],s=e.nearestBitrateFullIndex(a);t.lowerlimit=n[s].lowpass}function H(e){var t=e.internal_flags,n=32;if(t.lowpass1>0){for(var s=999,r=0;r<=31;r++)(_=r/31)>=t.lowpass2&&(n=Math.min(n,r)),t.lowpass1<_&&_<t.lowpass2&&(s=Math.min(s,r));t.lowpass1=999==s?(n-.75)/31:(s-.75)/31,t.lowpass2=n/31}for(t.highpass2>0&&a(),t.highpass2>0&&a(),r=0;r<32;r++){var i,o,_=r/31;t.highpass2>t.highpass1?a():i=1,o=t.lowpass2>t.lowpass1?A((_-t.lowpass1)/(t.lowpass2-t.lowpass1+1e-20)):1,t.amp_filter[r]=i*o}}function O(e){var t=e.internal_flags;switch(e.quality){default:case 9:t.psymodel=0,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 8:e.quality=7;case 7:t.psymodel=1,t.noise_shaping=0,t.noise_shaping_amp=0,t.noise_shaping_stop=0,t.use_best_huffman=0,t.full_outer_loop=0;break;case 6:case 5:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=0,t.full_outer_loop=0;break;case 4:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=0,t.noise_shaping_stop=0,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 3:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 2:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=1,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0;break;case 1:case 0:t.psymodel=1,0==t.noise_shaping&&(t.noise_shaping=1),0==t.substep_shaping&&(t.substep_shaping=2),t.noise_shaping_amp=2,t.noise_shaping_stop=1,-1==t.subblock_gain&&(t.subblock_gain=1),t.use_best_huffman=1,t.full_outer_loop=0}}function N(e){var t=e.internal_flags;e.frameNum=0,e.write_id3tag_automatic&&h.id3tag_write_v2(e),t.bitrate_stereoMode_Hist=l([16,5]),t.bitrate_blockType_Hist=l([16,6]),t.PeakSample=0,e.bWriteVbrTag&&u.InitVbrTag(e)}function D(e,t){(null==e.in_buffer_0||e.in_buffer_nsamples<t)&&(e.in_buffer_0=i(t),e.in_buffer_1=i(t),e.in_buffer_nsamples=t)}function V(e){var t=Y.BLKSIZE+e.framesize-Y.FFTOFFSET;return t=Math.max(t,512+e.framesize-32)}function F(e,t,r,i,o,_,l){var f,c,u,h,b,p=e.internal_flags,m=0,v=[null,null],d=[null,null];if(p.Class_ID!=M)return-3;if(0==i)return 0;if((b=s.copy_buffer(p,o,_,l,0))<0)return b;if(_+=b,m+=b,d[0]=t,d[1]=r,k.NEQ(e.scale,0)&&k.NEQ(e.scale,1))for(c=0;c<i;++c)d[0][c]*=e.scale,2==p.channels_out&&(d[1][c]*=e.scale);if(k.NEQ(e.scale_left,0)&&k.NEQ(e.scale_left,1))for(c=0;c<i;++c)d[0][c]*=e.scale_left;if(k.NEQ(e.scale_right,0)&&k.NEQ(e.scale_right,1))for(c=0;c<i;++c)d[1][c]*=e.scale_right;2==e.num_channels&&1==p.channels_out&&a(),h=V(e),v[0]=p.mfbuf[0],v[1]=p.mfbuf[1];for(var g=0;i>0;){var S=[null,null],y=0,A=0;S[0]=d[0],S[1]=d[1];var R=new X;if(z(e,v,S,g,i,R),y=R.n_in,A=R.n_out,p.findReplayGain&&!p.decode_on_the_fly&&n.AnalyzeSamples(p.rgdata,v[0],p.mf_size,v[1],p.mf_size,A,p.channels_out)==w.GAIN_ANALYSIS_ERROR)return-6;if(i-=y,g+=y,p.channels_out,p.mf_size+=A,p.mf_samples_to_encode<1&&a(),p.mf_samples_to_encode+=A,p.mf_size>=h){var x=l-m;if(0==l&&(x=0),(f=j(e,v[0],v[1],o,_,x))<0)return f;for(_+=f,m+=f,p.mf_size-=e.framesize,p.mf_samples_to_encode-=e.framesize,u=0;u<p.channels_out;u++)for(c=0;c<p.mf_size;c++)v[u][c]=v[u][c+e.framesize]}}return m}function j(t,a,n,s,r,i){var o=e.enc.lame_encode_mp3_frame(t,a,n,s,r,i);return t.frameNum++,o}function X(){this.n_in=0,this.n_out=0}function z(e,t,n,s,r,i){var o=e.internal_flags;if(o.resample_ratio<.9999||o.resample_ratio>1.0001)a();else{i.n_out=Math.min(e.framesize,r),i.n_in=i.n_out;for(var _=0;_<i.n_out;++_)t[0][o.mf_size+_]=n[0][s+_],2==o.channels_out&&(t[1][o.mf_size+_]=n[1][s+_])}}this.lame_init=function(){var e=new I;return 0!=y(e)?null:(e.lame_allocated_gfp=1,e)},this.nearestBitrateFullIndex=function(e){var t=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],a=0,n=0,s=0,r=0;r=t[16],s=16,n=t[16],a=16;for(var i=0;i<16;i++)if(Math.max(e,t[i+1])!=e){r=t[i+1],s=i+1,n=t[i],a=i;break}return r-e>e-n?a:s},this.lame_init_params=function(e){var t=e.internal_flags;if(t.Class_ID=0,null==t.ATH&&(t.ATH=new C),null==t.PSY&&(t.PSY=new m),null==t.rgdata&&(t.rgdata=new P),t.channels_in=e.num_channels,1==t.channels_in&&(e.mode=d.MONO),t.channels_out=e.mode==d.MONO?1:2,t.mode_ext=Y.MPG_MD_MS_LR,e.mode==d.MONO&&(e.force_ms=!1),e.VBR==v.vbr_off&&128!=e.VBR_mean_bitrate_kbps&&0==e.brate&&(e.brate=e.VBR_mean_bitrate_kbps),e.VBR==v.vbr_off||e.VBR==v.vbr_mtrh||e.VBR==v.vbr_mt||(e.free_format=!1),e.VBR==v.vbr_off&&0==e.brate&&a(),e.VBR==v.vbr_off&&e.compression_ratio>0&&a(),0!=e.out_samplerate&&(e.out_samplerate<16e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,64)):e.out_samplerate<32e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,160)):(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,32),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320))),0==e.lowpassfreq){var n=16e3;switch(e.VBR){case v.vbr_off:E(i=new g,e.brate),n=i.lowerlimit;break;case v.vbr_abr:var i;E(i=new g,e.VBR_mean_bitrate_kbps),n=i.lowerlimit;break;case v.vbr_rh:a();default:a()}e.mode!=d.MONO||e.VBR!=v.vbr_off&&e.VBR!=v.vbr_abr||(n*=1.5),e.lowpassfreq=0|n}switch(0==e.out_samplerate&&a(),e.lowpassfreq=Math.min(20500,e.lowpassfreq),e.lowpassfreq=Math.min(e.out_samplerate/2,e.lowpassfreq),e.VBR==v.vbr_off&&(e.compression_ratio=16*e.out_samplerate*t.channels_out/(1e3*e.brate)),e.VBR==v.vbr_abr&&a(),e.bWriteVbrTag||(e.findReplayGain=!1,e.decode_on_the_fly=!1,t.findPeakSample=!1),t.findReplayGain=e.findReplayGain,t.decode_on_the_fly=e.decode_on_the_fly,t.decode_on_the_fly&&(t.findPeakSample=!0),t.findReplayGain&&a(),t.decode_on_the_fly&&!e.decode_only&&a(),t.mode_gr=e.out_samplerate<=24e3?1:2,e.framesize=576*t.mode_gr,e.encoder_delay=Y.ENCDELAY,t.resample_ratio=e.in_samplerate/e.out_samplerate,e.VBR){case v.vbr_mt:case v.vbr_rh:case v.vbr_mtrh:var l=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5];e.compression_ratio=l[e.VBR_q];break;case v.vbr_abr:e.compression_ratio=16*e.out_samplerate*t.channels_out/(1e3*e.VBR_mean_bitrate_kbps);break;default:e.compression_ratio=16*e.out_samplerate*t.channels_out/(1e3*e.brate)}e.mode==d.NOT_SET&&(e.mode=d.JOINT_STEREO),e.highpassfreq>0?a():(t.highpass1=0,t.highpass2=0),e.lowpassfreq>0?(t.lowpass2=2*e.lowpassfreq,e.lowpasswidth>=0?a():t.lowpass1=2*e.lowpassfreq,t.lowpass1/=e.out_samplerate,t.lowpass2/=e.out_samplerate):a(),H(e),t.samplerate_index=R(e.out_samplerate,e),t.samplerate_index<0&&a(),e.VBR==v.vbr_off?e.free_format?t.bitrate_index=0:(e.brate=B(e.brate,e.version,e.out_samplerate),t.bitrate_index=T(e.brate,e.version,e.out_samplerate),t.bitrate_index<=0&&a()):t.bitrate_index=1,e.analysis&&(e.bWriteVbrTag=!1),null!=t.pinfo&&(e.bWriteVbrTag=!1),s.init_bit_stream_w(t);for(var f,c=t.samplerate_index+3*e.version+6*(e.out_samplerate<16e3?1:0),u=0;u<Y.SBMAX_l+1;u++)t.scalefac_band.l[u]=o.sfBandIndex[c].l[u];for(u=0;u<Y.PSFB21+1;u++){var h=(t.scalefac_band.l[22]-t.scalefac_band.l[21])/Y.PSFB21,S=t.scalefac_band.l[21]+u*h;t.scalefac_band.psfb21[u]=S}for(t.scalefac_band.psfb21[Y.PSFB21]=576,u=0;u<Y.SBMAX_s+1;u++)t.scalefac_band.s[u]=o.sfBandIndex[c].s[u];for(u=0;u<Y.PSFB12+1;u++)h=(t.scalefac_band.s[13]-t.scalefac_band.s[12])/Y.PSFB12,S=t.scalefac_band.s[12]+u*h,t.scalefac_band.psfb12[u]=S;for(t.scalefac_band.psfb12[Y.PSFB12]=192,1==e.version?t.sideinfo_len=1==t.channels_out?21:36:t.sideinfo_len=1==t.channels_out?13:21,e.error_protection&&(t.sideinfo_len+=2),N(e),t.Class_ID=M,f=0;f<19;f++)t.nsPsy.pefirbuf[f]=700*t.mode_gr*t.channels_out;switch(-1==e.ATHtype&&(e.ATHtype=4),e.VBR){case v.vbr_mt:e.VBR=v.vbr_mtrh;case v.vbr_mtrh:null==e.useTemporal&&(e.useTemporal=!1),r.apply_preset(e,500-10*e.VBR_q,0),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),e.quality<5&&(e.quality=0),e.quality>5&&(e.quality=5),t.PSY.mask_adjust=e.maskingadjust,t.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?t.sfb21_extra=!1:t.sfb21_extra=e.out_samplerate>44e3,t.iteration_loop=new VBRNewIterationLoop(_);break;case v.vbr_rh:r.apply_preset(e,500-10*e.VBR_q,0),t.PSY.mask_adjust=e.maskingadjust,t.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?t.sfb21_extra=!1:t.sfb21_extra=e.out_samplerate>44e3,e.quality>6&&(e.quality=6),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),t.iteration_loop=new VBROldIterationLoop(_);break;default:var w;t.sfb21_extra=!1,e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),(w=e.VBR)==v.vbr_off&&(e.VBR_mean_bitrate_kbps=e.brate),r.apply_preset(e,e.VBR_mean_bitrate_kbps,0),e.VBR=w,t.PSY.mask_adjust=e.maskingadjust,t.PSY.mask_adjust_short=e.maskingadjust_short,w==v.vbr_off?t.iteration_loop=new L(_):a()}return e.VBR!=v.vbr_off&&a(),e.tune&&a(),O(e),e.athaa_type<0?t.ATH.useAdjust=3:t.ATH.useAdjust=e.athaa_type,t.ATH.aaSensitivityP=Math.pow(10,e.athaa_sensitivity/-10),null==e.short_blocks&&(e.short_blocks=p.short_block_allowed),e.short_blocks!=p.short_block_allowed||e.mode!=d.JOINT_STEREO&&e.mode!=d.STEREO||(e.short_blocks=p.short_block_coupled),e.quant_comp<0&&(e.quant_comp=1),e.quant_comp_short<0&&(e.quant_comp_short=0),e.msfix<0&&(e.msfix=0),e.exp_nspsytune=1|e.exp_nspsytune,e.internal_flags.nsPsy.attackthre<0&&(e.internal_flags.nsPsy.attackthre=W.NSATTACKTHRE),e.internal_flags.nsPsy.attackthre_s<0&&(e.internal_flags.nsPsy.attackthre_s=W.NSATTACKTHRE_S),e.scale<0&&(e.scale=1),e.ATHtype<0&&(e.ATHtype=4),e.ATHcurve<0&&(e.ATHcurve=4),e.athaa_loudapprox<0&&(e.athaa_loudapprox=2),e.interChRatio<0&&(e.interChRatio=0),null==e.useTemporal&&(e.useTemporal=!0),t.slot_lag=t.frac_SpF=0,e.VBR==v.vbr_off&&(t.slot_lag=t.frac_SpF=72e3*(e.version+1)*e.brate%e.out_samplerate|0),o.iteration_init(e),b.psymodel_init(e),0},this.lame_encode_flush=function(e,t,n,r){var i,o,_,l,c=e.internal_flags,u=f([2,1152]),h=0,b=c.mf_samples_to_encode-Y.POSTDELAY,p=V(e);if(c.mf_samples_to_encode<1)return 0;for(i=0,e.in_samplerate!=e.out_samplerate&&a(),(_=e.framesize-b%e.framesize)<576&&(_+=e.framesize),e.encoder_padding=_,l=(b+_)/e.framesize;l>0&&h>=0;){var m=p-c.mf_size,v=e.frameNum;m*=e.in_samplerate,(m/=e.out_samplerate)>1152&&(m=1152),m<1&&(m=1),o=r-i,0==r&&(o=0),n+=h=this.lame_encode_buffer(e,u[0],u[1],m,t,n,o),i+=h,l-=v!=e.frameNum?1:0}return c.mf_samples_to_encode=0,h<0?h:(o=r-i,0==r&&(o=0),s.flush_bitstream(e),(h=s.copy_buffer(c,t,n,o,1))<0?h:(n+=h,o=r-(i+=h),0==r&&(o=0),e.write_id3tag_automatic&&a(),i))},this.lame_encode_buffer=function(e,t,a,n,s,r,i){var o=e.internal_flags,_=[null,null];if(o.Class_ID!=M)return-3;if(0==n)return 0;D(o,n),_[0]=o.in_buffer_0,_[1]=o.in_buffer_1;for(var l=0;l<n;l++)_[0][l]=t[l],o.channels_in>1&&(_[1][l]=a[l]);return F(e,_[0],_[1],n,s,r,i)}}function $(){this.setModules=function(e,t){}}function J(){this.setModules=function(e,t,a){}}function ee(){}function te(){this.setModules=function(e,t){}}function ae(e,t,s){1!=e&&a("fix cc: only supports mono");var r=new Q,i=new $,o=new w,_=new k,l=new M,f=new O,c=new F,u=new A,h=new g,b=new te,p=new y,m=new S,v=new J,R=new ee;r.setModules(o,_,l,f,c,u,h,b,R),_.setModules(o,R,h,u),b.setModules(_,h),l.setModules(r),c.setModules(_,p,f,m),f.setModules(m,p,r.enc.psy),p.setModules(_),m.setModules(f),u.setModules(r,_,h),i.setModules(v,R),v.setModules(h,b,l);var x=r.lame_init();x.num_channels=e,x.in_samplerate=t,x.out_samplerate=t,x.brate=s,x.mode=d.STEREO,x.quality=3,x.bWriteVbrTag=!1,x.disable_reservoir=!0,x.write_id3tag_automatic=!1,r.lame_init_params(x);var B=1152,T=0|1.25*B+7200,E=n(T);this.encodeBuffer=function(t,a){1==e&&(a=t),t.length>B&&(B=t.length,E=n(T=0|1.25*B+7200));var s=r.lame_encode_buffer(x,t,a,t.length,E,0,T);return new Int8Array(E.subarray(0,s))},this.flush=function(){var e=r.lame_encode_flush(x,E,0,T);return new Int8Array(E.subarray(0,e))}}V.SFBMAX=3*Y.SBMAX_s,Y.ENCDELAY=576,Y.POSTDELAY=1152,Y.MDCTDELAY=48,Y.FFTOFFSET=224+Y.MDCTDELAY,Y.DECDELAY=528,Y.SBLIMIT=32,Y.CBANDS=64,Y.SBPSY_l=21,Y.SBPSY_s=12,Y.SBMAX_l=22,Y.SBMAX_s=13,Y.PSFB21=6,Y.PSFB12=6,Y.BLKSIZE=1024,Y.HBLKSIZE=Y.BLKSIZE/2+1,Y.BLKSIZE_s=256,Y.HBLKSIZE_s=Y.BLKSIZE_s/2+1,Y.NORM_TYPE=0,Y.START_TYPE=1,Y.SHORT_TYPE=2,Y.STOP_TYPE=3,Y.MPG_MD_LR_LR=0,Y.MPG_MD_LR_I=1,Y.MPG_MD_MS_LR=2,Y.MPG_MD_MS_I=3,Y.fircoef=[-.1039435,-.1892065,5*-.0432472,-.155915,3898045e-23,.0467745*5,.50455,.756825,.187098*5],K.MFSIZE=3456+Y.ENCDELAY-Y.MDCTDELAY,K.MAX_HEADER_BUF=256,K.MAX_BITS_PER_CHANNEL=4095,K.MAX_BITS_PER_GRANULE=7680,K.BPC=320,V.SFBMAX=3*Y.SBMAX_s,t.Mp3Encoder=ae}t(),e.lamejs=t}(("object"==typeof window&&window.document?window:Object).Recorder);