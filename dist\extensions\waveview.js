/*
錄音
https://github.com/xiangyuecn/Recorder
src: extensions/waveview.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,a=t.i18n;!function(e,t,a,r){"use strict";var i=function(e){return new o(e)},n="WaveView",o=function(e){var t=this,i={scale:2,speed:9,phase:21.8,fps:20,keep:!0,lineWidth:3,linear1:[0,"rgba(150,96,238,1)",.2,"rgba(170,79,249,1)",1,"rgba(53,199,253,1)"],linear2:[0,"rgba(209,130,255,0.6)",1,"rgba(53,199,255,0.6)"],linearBg:[0,"rgba(255,255,255,0.2)",1,"rgba(54,197,252,0.2)"]};for(var o in e)i[o]=e[o];t.set=e=i;var h="compatibleCanvas";if(e[h])var l=t.canvas=e[h];else{if(!r)throw new Error(a.G("NonBrowser-1",[n]));var s=e.elem;s&&("string"==typeof s?s=document.querySelector(s):s.length&&(s=s[0])),s&&(e.width=s.offsetWidth,e.height=s.offsetHeight);var c=t.elem=document.createElement("div");c.style.fontSize=0,c.innerHTML='<canvas style="width:100%;height:100%;"/>';l=t.canvas=c.querySelector("canvas");s&&(s.innerHTML="",s.appendChild(c))}var d=e.scale,f=e.width*d,g=e.height*d;if(!f||!g)throw new Error(a.G("IllegalArgs-1",[n+" width=0 height=0"]));l.width=f,l.height=g;var p=t.ctx=l.getContext("2d");t.linear1=t.genLinear(p,f,e.linear1),t.linear2=t.genLinear(p,f,e.linear2),t.linearBg=t.genLinear(p,g,e.linearBg,!0),t._phase=0};o.prototype=i.prototype={genLinear:function(e,t,a,r){for(var i=e.createLinearGradient(0,0,r?0:t,r?t:0),n=0;n<a.length;)i.addColorStop(a[n++],a[n++]);return i},genPath:function(e,t,a){for(var r=[],i=this.set,n=i.scale,o=i.width*n,h=i.height*n/2,l=0;l<=o;l+=n){var s=(1+Math.cos(Math.PI+l/o*2*Math.PI))/2*h*t*Math.sin(2*Math.PI*(l/o)*e+a)+h;r.push(s)}return r},input:function(e,t,a){var r=this;r.sampleRate=a,r.pcmData=e,r.pcmPos=0,r.inputTime=Date.now(),r.schedule()},schedule:function(){var t=this,a=t.set,r=Math.floor(1e3/a.fps);t.timer||(t.timer=setInterval(function(){t.schedule()},r));var i=Date.now();if(!(i-(t.drawTime||0)<r)){t.drawTime=i;for(var n=t.sampleRate/a.fps,o=t.pcmData,h=t.pcmPos,l=Math.max(0,Math.min(n,o.length-h)),s=0,c=0;c<l;c++,h++)s+=Math.abs(o[h]);t.pcmPos=h,!l&&a.keep||t.draw(e.PowerLevel(s,l)),!l&&i-t.inputTime>1300&&(clearInterval(t.timer),t.timer=0)}},draw:function(e){var t=this,a=t.set,r=t.ctx,i=a.scale,n=a.width*i,o=a.height*i,h=a.speed/a.fps,l=t._phase-=h,s=l+h*a.phase,c=e/100,d=t.genPath(2,c,l),f=t.genPath(1.8,c,s);r.clearRect(0,0,n,o),r.beginPath();for(var g=0,p=0;p<=n;g++,p+=i)0==p?r.moveTo(p,d[g]):r.lineTo(p,d[g]);g--;for(p=n-1;p>=0;g--,p-=i)r.lineTo(p,f[g]);r.closePath(),r.fillStyle=t.linearBg,r.fill(),t.drawPath(f,t.linear2),t.drawPath(d,t.linear1)},drawPath:function(e,t){var a=this,r=a.set,i=a.ctx,n=r.scale,o=r.width*n;i.beginPath();for(var h=0,l=0;l<=o;h++,l+=n)0==l?i.moveTo(l,e[h]):i.lineTo(l,e[h]);i.lineWidth=r.lineWidth*n,i.strokeStyle=t,i.stroke()}},e[n]=i}(t,0,a.$T,e)}();