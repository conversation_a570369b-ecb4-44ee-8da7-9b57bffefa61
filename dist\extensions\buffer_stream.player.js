/*
錄音
https://github.com/xiangyuecn/Recorder
src: extensions/buffer_stream.player.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,r=t.i18n;!function(e,t,r,n){"use strict";var a=function(e){return new i(e)},u="BufferStreamPlayer",i=function(e){var t=this,r={play:!0,realtime:!0};for(var n in e)r[n]=e[n];t.set=e=r,e.onInputError||(e.onInputError=function(e,t){c(e,1)})};i.prototype=a.prototype={getAudioSrc:function(){return c(r("0XYC::getAudioSrc方法已过时：请直接使用getMediaStream然后赋值给audio.srcObject，仅允许在不支持srcObject的浏览器中调用本方法赋值给audio.src以做兼容"),3),this._src||(this._src=(window.URL||webkitURL).createObjectURL(this.getMediaStream())),this._src},getMediaStream:function(){if(!this._dest)throw new Error(f());return this._dest.stream},start:function(t,i){var f=function(e,t){var r=!o();t||s._clear(),c(e,1),r&&i&&i(e)},o=function(){if(s.isStop)return c(r("6DDt::start被stop终止"),3),!0},s=this,d=s.set,l=s.__abTest;if(null==s._Tc)if(n){s._Tc=0,s._Td=0,s.currentTime=0,s.duration=0,s.isStop=0,s.isPause=0,s.isPlayEnd=0,s.inputN=0,s.inputQueueIdx=0,s.inputQueue=[],s.bufferSampleRate=0,s.audioBuffer=0,s.pcmBuffer=[[],[]];var p=function(e){f(r("P6Gs::浏览器不支持打开{1}",0,u)+(e?": "+e:""))},_=d.runningContext||e.GetContext(!0);s._ctx=_;var h=_.state,m=e.CtxSpEnd(h);!l&&c("start... ctx.state="+h+(m?r("JwDm::（注意：ctx不是running状态，start需要在用户操作(触摸、点击等)时进行调用，否则会尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）"):""));var v=1;if(_&&_.createMediaStreamDestination){var B=_.createBufferSource();B.start&&void 0!==B.onended||(v=0)}else v=0;if(v){var b=function(){if(!o()){var e=_.createMediaStreamDestination();e.channelCount=1,s._dest=e,!l&&c("start ok"),t&&t(),s._inputProcess(),s._updateTime(),w?(c(r("qx6X::此浏览器的AudioBuffer实现不支持动态特性，采用兼容模式"),3),s._writeInt=setInterval(function(){s._writeBad()},10)):s._writeInt=setInterval(function(){s._writeBuffer()},100)}},g=function(){var t,n=a({play:!1,sampleRate:8e3,runningContext:_});n.__abTest=1,n.start(function(){t=e({type:"unknown",sourceStream:n.getMediaStream(),runningContext:_,onProcess:function(e){for(var r=e[e.length-1],i=1,f=0;f<r.length;f++)if(0!=r[f]){i=0;break}i&&e.length<5||(t.close(),n.stop(),u&&(clearTimeout(u),u=0,w=i,a.BadAudioBuffer=w,b()))}}),t.open(function(){t.start()},function(e){n.stop(),p(e)})},p);for(var u=setTimeout(function(){u=0,n.stop(),t&&t.close(),p(r("cdOx::环境检测超时"))},1500),i=new Int16Array(8e3),f=0;f<8e3;f++)i[f]=~~(32767*Math.random()*2-32767);n.input(i)},w=a.BadAudioBuffer,x=function(){l||null!=w?setTimeout(b):g()},S="AudioContext resume: ";e.ResumeCtx(_,function(e){return e&&c(S+"wait..."),!s.isStop},function(e){e&&c(S+_.state),x()},function(e){c(S+_.state+" "+r("S2Bu::可能无法播放：{1}",0,e),1),x()})}else p("")}else f(r.G("NonBrowser-1",[u]));else f(r("I4h4::{1}多次start",0,u),1)},_clear:function(){var t=this;t.isStop=1,clearInterval(t._writeInt),t.inputQueue=0,t._src&&((window.URL||webkitURL).revokeObjectURL(t._src),t._src=0),t._dest&&(e.StopS_(t._dest.stream),t._dest=0),!t.set.runningContext&&t._ctx&&e.CloseNewCtx(t._ctx),t._ctx=0;var r=t.bufferSource;r&&(r.disconnect(),r.stop()),t.bufferSource=0,t.audioBuffer=0},stop:function(){var e=this;e._clear(),!e.__abTest&&c("stop"),e._playEnd(1)},pause:function(){c("pause"),this.isPause=1,this._updateTime(1)},resume:function(){var t=this,r="resume",n=r+"(wait ctx)";c(r),t.isPause=0,t._updateTime(1);var a=t._ctx;a&&e.ResumeCtx(a,function(e){return e&&c(n+"..."),!t.isStop&&!t.isPause},function(e){e&&c(n+a.state)},function(e){c(n+a.state+"[err]"+e,1)})},_playEnd:function(e){var t=this,r=t._PNs,n=t.set.onPlayEnd;!e&&t.isPause||!e&&t.isPlayEnd||(e||r&&Date.now()-r>500?(t._PNs=0,t.isPlayEnd=1,n&&n(),t._updateTime(1)):r||(t._PNs=Date.now()))},_playLive:function(){var e=this;e.isPlayEnd=0,e._PNs=0},_updateTime:function(e){var t=this,r=t.bufferSampleRate||9e9,n=t.set.onUpdateTime;t.currentTime=Math.round(t._Tc/r*1e3),t.duration=Math.round(t._Td/r*1e3);var a=""+t.currentTime+t.duration;(e||t._UTs!=a)&&(t._UTs=a,n&&n())},input:function(e){var t=this,r=t.set,n=++t.inputN;if(!t.inputQueue)throw new Error(f());var a=r.decode;a?s(e,function(e){t.inputQueue&&((null==a.fadeInOut||a.fadeInOut)&&o(e.data,e.sampleRate),t._input2(n,e.data,e.sampleRate))},function(e){t._inputErr(e,n)}):t._input2(n,e,r.sampleRate)},_input2:function(e,t,r){var n=this,a=n.set;a.transform?a.transform(t,r,function(t,a){n.inputQueue&&(r=a||r,n._input3(e,t,r))},function(t){n._inputErr(t,e)}):n._input3(e,t,r)},_input3:function(e,t,n){var a=this;t&&t.subarray?n?a.bufferSampleRate&&a.bufferSampleRate!=n?a._inputErr(r("IHZd::input调用失败：data的sampleRate={1}和之前的={2}不同",0,n,a.bufferSampleRate),e):(a.bufferSampleRate||(a.bufferSampleRate=n),e>a.inputQueueIdx&&(a.inputQueue[e]=t),a._dest&&a._inputProcess()):a._inputErr(r("N4ke::input调用失败：未提供sampleRate"),e):a._inputErr(r("ZfGG::input调用失败：非pcm[Int16,...]输入时，必须解码或者使用transform转换"),e)},_inputErr:function(e,t){this.inputQueue&&(this.inputQueue[t]=1,this.set.onInputError(e,t))},_inputProcess:function(){var e=this;if(e.bufferSampleRate){for(var t=e.inputQueue,r=e.inputQueueIdx+1;r<t.length;r++){var n=t[r];if(1!=n){if(!n)return;e.inputQueueIdx=r,t[r]=null;var u=e.pcmBuffer,i=u[0],f=u[1];if(i.length){if(f.length){var o=new Int16Array(i.length+f.length);o.set(i),o.set(f,i.length),u[0]=o}}else u[0]=f;u[1]=n,e._Td+=n.length,e._updateTime(),e._playLive()}else e.inputQueueIdx=r}a.BadAudioBuffer?e._writeBad():e.audioBuffer?e._writeBuffer():e._createBuffer(!0)}},clearInput:function(e){var t=this,r=t.bufferSampleRate,n=0;if(t.inputQueue){t.inputQueueIdx=t.inputN;var a=t.pcmBuffer;n=a[0].length+a[1].length,t._subClear(),e||(t._Td-=n),t._updateTime(1)}var u=n?Math.round(n/r*1e3):0;return c("clearInput "+u+"ms "+n),u},_createBuffer:function(e){var t=this,r=t.set;if(e||t.audioBuffer){var n=t._ctx,a=t.bufferSampleRate,u=a*(r.bufferSecond||60),i=n.createBuffer(1,u,a),f=n.createBufferSource();f.channelCount=1,f.buffer=i,f.connect(t._dest),r.play&&f.connect(n.destination),f.onended=function(){f.disconnect(),f.stop(),t._createBuffer()},f.start(),t.bufferSource=f,t.audioBuffer=i,t.audioBufferIdx=0,t._createBufferTime=Date.now(),t._writeBuffer()}},_writeBuffer:function(){var e=this,t=e.set,r=e.audioBuffer,n=e.bufferSampleRate,a=e.audioBufferIdx;if(r){var u=Math.floor((Date.now()-e._createBufferTime)/1e3*n);e.audioBufferIdx+.005*n<u&&(e.audioBufferIdx=u);var i=Math.max(0,e.audioBufferIdx-u),f=r.length-e.audioBufferIdx;if(!((f=Math.min(f,~~(.8*n)-i))<1||e._subPause())){var o=e.pcmBuffer,s=o[0],c=o[1].length;if(s.length+c!=0){e._playLive();for(var d=0,l=1,p=t.realtime;p;){var _=.15,h=i+s.length,m=(p.maxDelay||300)/1e3*n;if(h<m){var v=Math.floor(_*n-h-c);0==a&&v>0&&(e.audioBufferIdx=Math.max(e.audioBufferIdx,v)),p=!1;break}if(p.discardAll){h>1.333*m&&(s=e._cutPcm0(Math.round(.666*m-i-c))),p=!1;break}s=e._cutPcm0(3*n-i-c),l=1.6,d=Math.min(f,Math.floor((s.length+c)/l));break}p||(d=Math.min(f,s.length+c)),d&&(e.audioBufferIdx=e._subWrite(r,d,e.audioBufferIdx,l))}else e._playEnd()}}},_writeBad:function(){var e=this,t=e.set,r=e.audioBuffer,n=e.bufferSampleRate,a=e._ctx;if(r){var u=r.length/n*1e3;if(Date.now()-e._createBufferTime<u-5)return}var i=~~(.8*n),f=t.PlayBufferDisable?0:n/1e3*300;if(!e._subPause()){var s=e.pcmBuffer,c=s[0],d=s[1].length,l=c.length+d;if(0==l||l<f)e._playEnd();else{e._playLive();for(var p=0,_=1,h=t.realtime;h;){var m=c.length,v=(h.maxDelay||300)/1e3*n;if(m<v){h=!1;break}if(h.discardAll){m>1.333*v&&(c=e._cutPcm0(Math.round(.666*v-d))),h=!1;break}c=e._cutPcm0(3*n-d),_=1.6,p=Math.min(i,Math.floor((c.length+d)/_));break}if(h||(p=Math.min(i,c.length+d)),p){r=a.createBuffer(1,p,n),e._subWrite(r,p,0,_),o(r.getChannelData(0),n);var B=a.createBufferSource();B.channelCount=1,B.buffer=r,B.connect(e._dest),t.play&&B.connect(a.destination),B.start(),e.bufferSource=B,e.audioBuffer=r,e._createBufferTime=Date.now()}}}},_cutPcm0:function(e){var t=this.pcmBuffer,n=t[0];if(e<0&&(e=0),n.length>e){var a=n.length-e,u=Math.round(a/this.bufferSampleRate*1e3);n=n.subarray(a),t[0]=n,c(r("L8sC::延迟过大，已丢弃{1}ms {2}",0,u,a),3)}return n},_subPause:function(){var e=this;return e.isPause?(e.set.realtime&&e._subClear(),1):0},_subClear:function(){this.pcmBuffer=[[],[]]},_subWrite:function(e,t,r,n){for(var a=this,u=a.pcmBuffer,i=u[0],f=u[1],o=new Int16Array(t),s=0,c=0,d=0;c<t&&d<i.length;)o[c++]=i[s],d+=n,s=Math.round(d);if(s>=i.length){for(i=new Int16Array(0),d=0,s=0;c<t&&d<f.length;)o[c++]=f[s],d+=n,s=Math.round(d);f=s>=f.length?new Int16Array(0):f.subarray(s),u[1]=f}else i=i.subarray(s);u[0]=i;var l=e.getChannelData(0);for(s=0;s<t;s++,r++)l[r]=o[s]/32767;return a._Tc+=t,a._updateTime(),r}};var f=function(){return r("TZPq::{1}未调用start方法",0,u)},o=a.FadeInOut=function(e,t){for(var r=t/1e3*1,n=0;n<r;n++)e[n]*=n/r;var a=e.length;for(n=~~(a-r);n<a;n++)e[n]*=(a-n)/r},s=a.DecodeAudio=function(t,n,a){var u=e.GetContext();u?t&&t instanceof ArrayBuffer?u.decodeAudioData(t,function(e){for(var t=e.getChannelData(0),r=e.sampleRate,a=new Int16Array(t.length),u=0;u<t.length;u++){var i=Math.max(-1,Math.min(1,t[u]));i=i<0?32768*i:32767*i,a[u]=i}n&&n({sampleRate:r,duration:Math.round(t.length/r*1e3),data:a})},function(e){a&&a(r("mOaT::音频解码失败：{1}",0,e&&e.message||"-"))}):a&&a(r("wE2k::音频解码数据必须是ArrayBuffer")):a&&a(r("iCFC::浏览器不支持音频解码"))},c=function(){var t=arguments;t[0]="["+u+"]"+t[0],e.CLog.apply(null,t)};e[u]=a}(t,0,r.$T,e)}();