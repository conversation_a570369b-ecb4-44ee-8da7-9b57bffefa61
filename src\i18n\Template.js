/*
Recorder i18n/Template.js
https://github.com/xiangyuecn/Recorder

Usage: Recorder.i18n.lang="Your-Language-Name" or "your-language"

Desc: This file is a language translation template file. After copying and renaming, translate the text into the corresponding language. 此文件为语言翻译模板文件，复制并改名后，将文本翻译成对应语言即可。

注意：请勿修改//@@打头的文本行；以下代码结构由/src/package-i18n.js自动生成，只允许在字符串中填写翻译后的文本，请勿改变代码结构；翻译的文本如果需要明确的空值，请填写"=Empty"；文本中的变量用{n}表示（n代表第几个变量），所有变量必须都出现至少一次，如果不要某变量用{n!}表示

Note: Do not modify the text lines starting with //@@; The following code structure is automatically generated by /src/package-i18n.js, only the translated text is allowed to be filled in the string, please do not change the code structure; If the translated text requires an explicit empty value, please fill in "=Empty"; Variables in the text are represented by {n} (n represents the number of variables), all variables must appear at least once, if a variable is not required, it is represented by {n!}
*/
(function(factory){
	var browser=typeof window=="object" && !!window.document;
	var win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面
	factory(win.Recorder,browser);
}(function(Recorder,isBrowser){
"use strict";
var i18n=Recorder.i18n;

//@@User Code-1 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-1 End @@

//@@Exec i18n.lang="Your-Language-Name";
Recorder.CLog('Import Recorder i18n lang="Your-Language-Name"');

i18n.alias["Your-Language-Name"]="your-language";

var putSet={lang:"your-language"};

i18n.data["rtl$your-language"]=false;
i18n.data["desc$your-language"]="This file is a language translation template file. After copying and renaming, translate the text into the corresponding language. 此文件为语言翻译模板文件，复制并改名后，将文本翻译成对应语言即可。";
//@@Exec i18n.GenerateDisplayEnglish=true;



//*************** Begin srcFile=recorder-core.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="重复导入{1}"
//@@en="Duplicate import {1}"
//@@Put0
 "K8zP:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="剩{1}个GetContext未close"
//@@en="There are {1} GetContext unclosed"
,"mSxV:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）"
//@@en=" (Note: ctx is not in the running state. At least one of rec.open and start must be called during user operations (touch, click, etc.), otherwise ctx.resume will be attempted during rec.start, which may cause compatibility issues (iOS only), please refer to the runningContext configuration in the documentation) "
,"nMIy:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，"
//@@en="The sampleRate of the Stream {1} is not equal to {2}, so the sampleRate conversion will be performed (note: the sound quality will not improve and may even deteriorate). This phenomenon mainly occurs when echoCancellation is not disabled on the mobile terminal. When the browser has echoCancellation, it may only return audio data with a sampleRate of 16k. "
,"eS8i:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。"
//@@en=". Due to 375 callbacks in 1 second in {1}, there may be performance problems on the mobile side, which may cause the callback to be lost and the recording to be shortened, but it will not affect the PC side. It is not recommended to enable {1} for now."
,"ZGlf:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="Connect采用老的{1}，"
//@@en="Connect uses the old {1}, "
,"7TU0:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="但已设置{1}尝试启用{2}"
//@@en="But {1} is set trying to enable {2}"
,"JwCL:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="可设置{1}尝试启用{2}"
//@@en="Can set {1} try to enable {2}"
,"VGjB:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="{1}未返回任何音频，恢复使用{2}"
//@@en="{1} did not return any audio, reverting to {2}"
,"MxX1:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="{1}多余回调"
//@@en="{1} redundant callback"
,"XUap:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="Connect采用{1}，设置{2}可恢复老式{3}"
//@@en="Connect uses {1}, set {2} to restore old-fashioned {3}"
,"yOta:"+ //args: {1}-{3}
       "" /** TODO: translate to your-language **/

//@@zh="（此浏览器不支持{1}）"
//@@en=" (This browser does not support {1}) "
,"VwPd:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="{1}未返回任何音频，降级使用{2}"
//@@en="{1} did not return any audio, downgrade to {2}"
,"vHnb:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="{1}多余回调"
//@@en="{1} redundant callback"
,"O9P7:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="Connect采用{1}，设置{2}可恢复使用{3}或老式{4}"
//@@en="Connect uses {1}, set {2} to restore to using {3} or old-fashioned {4}"
,"LMEm:"+ //args: {1}-{4}
       "" /** TODO: translate to your-language **/

//@@zh="{1}的filter采样率变了，重设滤波"
//@@en="The filter sampleRate of {1} has changed, reset the filter"
,"d48C:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="{1}似乎传入了未重置chunk {2}"
//@@en="{1} seems to have passed in an unreset chunk {2}"
,"tlbC:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="{1}和{2}必须是数值"
//@@en="{1} and {2} must be number"
,"VtS4:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="录音open失败："
//@@en="Recording open failed: "
,"5tWi:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="open被取消"
//@@en="open cancelled"
,"dFm8:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="open被中断"
//@@en="open interrupted"
,"VtJO:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="，可尝试使用RecordApp解决方案"
//@@en=", you can try to use the RecordApp solution "
,"EMJq:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="不能录音："
//@@en="Cannot record: "
,"A5bm:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="不支持此浏览器从流中获取录音"
//@@en="This browser does not support obtaining recordings from stream"
,"1iU7:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="从流中打开录音失败："
//@@en="Failed to open recording from stream: "
,"BTW2:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})"
//@@en="No permission to record (cross domain, please try adding microphone access policy to iframe, such as: {1})"
,"Nclz:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="，无可用麦克风"
//@@en=", no microphone available"
,"jBa9:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="用户拒绝了录音权限"
//@@en="User denied recording permission"
,"gyO5:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="浏览器禁止不安全页面录音，可开启https解决"
//@@en="Browser prohibits recording of unsafe pages, which can be resolved by enabling HTTPS"
,"oWNo:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="此浏览器不支持录音"
//@@en="This browser does not support recording"
,"COxc:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="发现同时多次调用open"
//@@en="It was found that open was called multiple times at the same time"
,"upb8:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="录音功能无效：无音频流"
//@@en="Invalid recording: no audio stream"
,"Q1GA:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="，将尝试禁用回声消除后重试"
//@@en=", will try to disable echoCancellation and try again"
,"KxE2:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="请求录音权限错误"
//@@en="Error requesting recording permission"
,"xEQR:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="无法录音："
//@@en="Unable to record: "
,"bDOG:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象"
//@@en="Note: The {1} parameter has been configured, which may cause the browser to not correctly select the microphone, or the mobile terminal to not enable echoCancellation, etc. "
,"IjL3:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置"
//@@en=", when {1} is not configured, the browser may automatically enable echoCancellation. When echoCancellation is not disabled on the mobile terminal, the system playback volume may be reduced (can be restored after closing the recording) and only 16k sampleRate audio stream is provided (when echoCancellation is not required, it can be explicitly configured to disable to obtain 48k high-quality stream). Please refer to the {2} configuration in the document"
,"RiWe:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="close被忽略（因为同时open了多个rec，只有最后一个会真正close）"
//@@en="close is ignored (because multiple recs are open at the same time, only the last one will actually close)"
,"hWVz:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="忽略"
//@@en="ignore"
,"UHvm:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="不支持{1}架构"
//@@en="{1} architecture not supported"
,"Essp:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="{1}类型不支持设置takeoffEncodeChunk"
//@@en="{1} type does not support setting takeoffEncodeChunk"
,"2XBl:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="(未加载编码器)"
//@@en="(Encoder not loaded)"
,"LG7e:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="{1}环境不支持实时处理"
//@@en="{1} environment does not support real-time processing"
,"7uMV:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="补偿{1}ms"
//@@en="Compensation {1}ms"
,"4Kfd:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="未补偿{1}ms"
//@@en="Uncompensated {1}ms"
,"bM5i:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="回调出错是不允许的，需保证不会抛异常"
//@@en="Callback error is not allowed, you need to ensure that no exception will be thrown"
,"gFUF:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="低性能，耗时{1}ms"
//@@en="Low performance, took {1}ms"
,"2ghS:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="未进入异步前不能清除buffers"
//@@en="Buffers cannot be cleared before entering async"
,"ufqH:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="start失败：未open"
//@@en="start failed: not open"
,"6WmN:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="start 开始录音，"
//@@en="start recording, "
,"kLDN:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="start被中断"
//@@en="start was interrupted"
,"Bp2y:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="，可能无法录音："
//@@en=", may fail to record: "
,"upkE:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="stop 和start时差:"
//@@en="Stop and start time difference: "
,"Xq4s:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="补偿:"
//@@en="compensate: "
,"3CQP:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="结束录音失败："
//@@en="Failed to stop recording: "
,"u8JG:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="，请设置{1}"
//@@en=", please set {1}"
,"1skY:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b"
//@@en="Stop recording, encoding takes {1}ms, audio duration {2}ms, file size {3}b"
,"Wv7l:"+ //args: {1}-{3}
       "" /** TODO: translate to your-language **/

//@@zh="{1}编码器返回的不是{2}"
//@@en="{1} encoder returned not {2}"
,"Vkbd:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"
//@@en="After enabling takeoffEncodeChunk, the length of the blob returned by stop is 0 and no audio data is provided"
,"QWnr:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="生成的{1}无效"
//@@en="Invalid generated {1}"
,"Sz2H:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音"
//@@en="Recording not started"
,"wf9t:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="，开始录音前无用户交互导致AudioContext未运行"
//@@en=", No user interaction before starting recording, resulting in AudioContext not running"
,"Dl2c:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未采集到录音"
//@@en="Recording not captured"
,"Ltz3:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载"
//@@en="The {1} encoder is not loaded. Please try to find the {1} encoder in the src/engine directory of the {2} and load it"
,"xGuI:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="录音错误："
//@@en="Recording error: "
,"AxOH:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="音频buffers被释放"
//@@en="Audio buffers are released"
,"xkKd:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="采样:{1} 花:{2}ms"
//@@en="Sampled: {1}, took: {2}ms"
,"CxeT:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="非浏览器环境，不支持{1}"
//@@en="Non-browser environment, does not support {1}"
,"NonBrowser-1:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="参数错误：{1}"
//@@en="Illegal argument: {1}"
,"IllegalArgs-1:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="调用{1}需要先导入{2}"
//@@en="Calling {1} needs to import {2} first"
,"NeedImport-2:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="不支持：{1}"
//@@en="Not support: {1}"
,"NotSupport-1:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="覆盖导入{1}"
//@@en="Override import {1}"
,"8HO5:"+ //args: {1}
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=recorder-core.js ***************



//*************** Begin srcFile=engine/beta-amr.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="AMR-NB(NarrowBand)，采样率设置无效（只提供8000hz），比特率范围：{1}（默认12.2kbps），一帧20ms、{2}字节；浏览器一般不支持播放amr格式，可用Recorder.amr2wav()转码成wav播放"
//@@en="AMR-NB (NarrowBand), sampleRate setting is invalid (only 8000hz is provided), bitRate range: {1} (default 12.2kbps), one frame 20ms, {2} bytes; browsers generally do not support playing amr format, available Recorder.amr2wav() transcoding into wav playback"
//@@Put0
 "b2mN:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="AMR Info: 和设置的不匹配{1}，已更新成{2}"
//@@en="AMR Info: does not match the set {1}, has been updated to {2}"
,"tQBv:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="数据采样率低于{1}"
//@@en="Data sampleRate lower than {1}"
,"q12D:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="当前浏览器版本太低，无法实时处理"
//@@en="The current browser version is too low to process in real time"
,"TxjV:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="takeoffEncodeChunk接管AMR编码器输出的二进制数据，只有首次回调数据（首帧）包含AMR头；在合并成AMR文件时，如果没有把首帧数据包含进去，则必须在文件开头添加上AMR头：Recorder.AMR.AMR_HEADER（转成二进制），否则无法播放"
//@@en="takeoffEncodeChunk takes over the binary data output by the AMR encoder, and only the first callback data (the first frame) contains the AMR header; when merging into an AMR file, if the first frame data is not included, the AMR header must be added at the beginning of the file: Recorder.AMR.AMR_HEADER (converted to binary), otherwise it cannot be played"
,"Q7p7:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前环境不支持Web Worker，amr实时编码器运行在主线程中"
//@@en="The current environment does not support Web Worker, and the amr real-time encoder runs in the main thread"
,"6o9Z:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="amr worker剩{1}个未stop"
//@@en="amr worker left {1} unstopped"
,"yYWs:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="amr编码器未start"
//@@en="amr encoder not started"
,"jOi8:"+ //no args
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=engine/beta-amr.js ***************



//*************** Begin srcFile=engine/beta-ogg.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="Ogg Vorbis，比特率取值16-100kbps，采样率取值无限制"
//@@en="Ogg Vorbis, bitRate 16-100kbps, sampleRate unlimited"
//@@Put0
 "O8Gn:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前浏览器版本太低，无法实时处理"
//@@en="The current browser version is too low to process in real time"
,"5si6:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="takeoffEncodeChunk接管OggVorbis编码器输出的二进制数据，Ogg由数据页组成，一页包含多帧音频数据（含几秒的音频，一页数据无法单独解码和播放），此编码器每次输出都是完整的一页数据，因此实时性会比较低；在合并成完整ogg文件时，必须将输出的所有数据合并到一起，否则可能无法播放，不支持截取中间一部分单独解码和播放"
//@@en="takeoffEncodeChunk takes over the binary data output by the OggVorbis encoder. Ogg is composed of data pages. One page contains multiple frames of audio data (including a few seconds of audio, and one page of data cannot be decoded and played alone). This encoder outputs a complete page of data each time, so the real-time performance will be relatively low; when merging into a complete ogg file, all the output data must be merged together, otherwise it may not be able to play, and it does not support intercepting the middle part to decode and play separately"
,"R8yz:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前环境不支持Web Worker，OggVorbis实时编码器运行在主线程中"
//@@en="The current environment does not support Web Worker, and the OggVorbis real-time encoder runs in the main thread"
,"hB9D:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="ogg worker剩{1}个未stop"
//@@en="There are {1} unstopped ogg workers"
,"oTiy:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="ogg编码器未start"
//@@en="ogg encoder not started"
,"dIpw:"+ //no args
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=engine/beta-ogg.js ***************



//*************** Begin srcFile=engine/beta-webm.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="此浏览器不支持进行webm编码，未实现MediaRecorder"
//@@en="This browser does not support webm encoding, MediaRecorder is not implemented"
//@@Put0
 "L49q:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="只有比较新的浏览器支持，压缩率和mp3差不多。由于未找到对已有pcm数据进行快速编码的方法，只能按照类似边播放边收听形式把数据导入到MediaRecorder，有几秒就要等几秒。输出音频虽然可以通过比特率来控制文件大小，但音频文件中的比特率并非设定比特率，采样率由于是我们自己采样的，到这个编码器随他怎么搞"
//@@en="Only newer browsers support it, and the compression rate is similar to mp3. Since there is no way to quickly encode the existing pcm data, the data can only be imported into MediaRecorder in a similar manner while playing and listening, and it takes a few seconds to wait for a few seconds. Although the output audio can control the file size through the bitRate, the bitRate in the audio file is not the set bitRate. Since the sampleRate is sampled by ourselves, we can do whatever we want with this encoder."
,"tsTW:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="此浏览器不支持把录音转成webm格式"
//@@en="This browser does not support converting recordings to webm format"
,"aG4z:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="转码webm出错：{1}"
//@@en="Error encoding webm: {1}"
,"PIX0:"+ //args: {1}
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=engine/beta-webm.js ***************



//*************** Begin srcFile=engine/g711x.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="{1}；{2}音频文件无法直接播放，可用Recorder.{2}2wav()转码成wav播放；采样率比特率设置无效，固定为8000hz采样率、16位，每个采样压缩成8位存储，音频文件大小为8000字节/秒；如需任意采样率支持，请使用Recorder.{2}_encode()方法"
//@@en="{1}; {2} audio files cannot be played directly, and can be transcoded into wav by Recorder.{2}2wav(); the sampleRate bitRate setting is invalid, fixed at 8000hz sampleRate, 16 bits, each sample is compressed into 8 bits for storage, and the audio file size is 8000 bytes/second; if you need any sampleRate support, please use Recorder.{2}_encode() Method"
//@@Put0
 "d8YX:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="数据采样率低于{1}"
//@@en="Data sampleRate lower than {1}"
,"29UK:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="{1}编码器未start"
//@@en="{1} encoder not started"
,"quVJ:"+ //args: {1}
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=engine/g711x.js ***************



//*************** Begin srcFile=engine/mp3.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）"
//@@en="sampleRate range: {1}; bitRate range: {2} (the sampleRate range supported by different bitRate is different, when the bitRate is less than 32kbps, the sampleRate must be less than 32000)"
//@@Put0
 "Zm7L:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="{1}不在mp3支持的取值范围：{2}"
//@@en="{1} is not in the value range supported by mp3: {2}"
,"eGB9:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}"
//@@en="sampleRate has been updated to {1}, because {2} is not in the value range supported by mp3: {3}"
,"zLTa:"+ //args: {1}-{3}
       "" /** TODO: translate to your-language **/

//@@zh="当前浏览器版本太低，无法实时处理"
//@@en="The current browser version is too low to process in real time"
,"yhUs:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前环境不支持Web Worker，mp3实时编码器运行在主线程中"
//@@en="The current environment does not support Web Worker, and the mp3 real-time encoder runs in the main thread"
,"k9PT:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="mp3 worker剩{1}个未stop"
//@@en="There are {1} unstopped mp3 workers left"
,"fT6M:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="mp3编码器未start"
//@@en="mp3 encoder not started"
,"mPxH:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="和设置的不匹配{1}，已更新成{2}"
//@@en="Does not match the set {1}, has been updated to {2}"
,"uY9i:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="Fix移除{1}帧"
//@@en="Fix remove {1} frame"
,"iMSm:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="移除帧数过多"
//@@en="Remove too many frames"
,"b9zm:"+ //no args
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=engine/mp3.js ***************



//*************** Begin srcFile=engine/pcm.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="pcm为未封装的原始音频数据，pcm音频文件无法直接播放，可用Recorder.pcm2wav()转码成wav播放；支持位数8位、16位（填在比特率里面），采样率取值无限制"
//@@en="pcm is unencapsulated original audio data, pcm audio files cannot be played directly, and can be transcoded into wav by Recorder.pcm2wav(); it supports 8-bit and 16-bit bits (fill in the bitRate), and the sampleRate is unlimited"
//@@Put0
 "fWsN:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="PCM Info: 不支持{1}位，已更新成{2}位"
//@@en="PCM Info: {1} bit is not supported, has been updated to {2} bit"
,"uMUJ:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="pcm2wav必须提供sampleRate和bitRate"
//@@en="pcm2wav must provide sampleRate and bitRate"
,"KmRz:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="pcm编码器未start"
//@@en="pcm encoder not started"
,"sDkA:"+ //no args
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=engine/pcm.js ***************



//*************** Begin srcFile=engine/wav.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）"
//@@en="Supports 8-bit and 16-bit bits (fill in the bitRate), and the sampleRate is unlimited; this encoder only adds a 44-byte wav header before the pcm data, and the encoded 16-bit wav file removes the beginning 44 bytes to get pcm (note: other wav encoders may not be 44 bytes)"
//@@Put0
 "gPSE:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="WAV Info: 不支持{1}位，已更新成{2}位"
//@@en="WAV Info: {1} bit is not supported, has been updated to {2} bit"
,"wyw9:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=engine/wav.js ***************



//*************** Begin srcFile=extensions/buffer_stream.player.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="getAudioSrc方法已过时：请直接使用getMediaStream然后赋值给audio.srcObject，仅允许在不支持srcObject的浏览器中调用本方法赋值给audio.src以做兼容"
//@@en="The getAudioSrc method is obsolete: please use getMediaStream directly and then assign it to audio.srcObject, it is only allowed to call this method in browsers that do not support srcObject and assign it to audio.src for compatibility"
//@@Put0
 "0XYC:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="start被stop终止"
//@@en="start is terminated by stop"
,"6DDt:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="{1}多次start"
//@@en="{1} repeat start"
,"I4h4:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="浏览器不支持打开{1}"
//@@en="The browser does not support opening {1}"
,"P6Gs:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="（注意：ctx不是running状态，start需要在用户操作(触摸、点击等)时进行调用，否则会尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）"
//@@en=" (Note: ctx is not in the running state, start needs to be called when the user operates (touch, click, etc.), otherwise it will try to perform ctx.resume, which may cause compatibility issues (only iOS), please refer to the runningContext configuration in the document) "
,"JwDm:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="此浏览器的AudioBuffer实现不支持动态特性，采用兼容模式"
//@@en="The AudioBuffer implementation of this browser does not support dynamic features, use compatibility mode"
,"qx6X:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="环境检测超时"
//@@en="Environment detection timeout"
,"cdOx:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="可能无法播放：{1}"
//@@en="Could not play: {1}"
,"S2Bu:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="input调用失败：非pcm[Int16,...]输入时，必须解码或者使用transform转换"
//@@en="input call failed: non-pcm[Int16,...] input must be decoded or converted using transform"
,"ZfGG:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="input调用失败：未提供sampleRate"
//@@en="input call failed: sampleRate not provided"
,"N4ke:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="input调用失败：data的sampleRate={1}和之前的={2}不同"
//@@en="input call failed: sampleRate={1} of data is different from previous={2}"
,"IHZd:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="延迟过大，已丢弃{1}ms {2}"
//@@en="The delay is too large, {1}ms has been discarded, {2}"
,"L8sC:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="{1}未调用start方法"
//@@en="{1} did not call the start method"
,"TZPq:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="浏览器不支持音频解码"
//@@en="Browser does not support audio decoding"
,"iCFC:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="音频解码数据必须是ArrayBuffer"
//@@en="Audio decoding data must be ArrayBuffer"
,"wE2k:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="音频解码失败：{1}"
//@@en="Audio decoding failed: {1}"
,"mOaT:"+ //args: {1}
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=extensions/buffer_stream.player.js ***************



//*************** Begin srcFile=extensions/create-audio.nmn2pcm.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="符号[{1}]无效：{2}"
//@@en="Invalid symbol [{1}]: {2}"
//@@Put0
 "3RBa:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="音符[{1}]无效：{2}"
//@@en="Invalid note [{1}]: {2}"
,"U212:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="多个音时必须对齐，相差{1}ms"
//@@en="Multiple tones must be aligned, with a difference of {1}ms"
,"7qAD:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="祝你生日快乐"
//@@en="Happy Birthday to You"
,"QGsW:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="致爱丽丝"
//@@en="For Elise"
,"emJR:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="卡农-右手简谱"
//@@en="Canon - Right Hand Notation"
,"GsYy:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="卡农"
//@@en="Canon"
,"bSFZ:"+ //no args
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=extensions/create-audio.nmn2pcm.js ***************



//*************** Begin srcFile=extensions/sonic.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="当前环境不支持Web Worker，不支持调用Sonic.Async"
//@@en="The current environment does not support Web Worker and does not support calling Sonic.Async"
//@@Put0
 "Ikdz:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="sonic worker剩{1}个未flush"
//@@en="There are {1} unflushed sonic workers left"
,"IC5Y:"+ //args: {1}
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=extensions/sonic.js ***************



//*************** Begin srcFile=app-support/app-native-support.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="{1}中的{2}方法未实现，请在{3}文件中或配置文件中实现此方法"
//@@en="The {2} method in {1} is not implemented, please implement this method in the {3} file or configuration file"
//@@Put0
 "WWoj:"+ //args: {1}-{3}
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音，但收到Native PCM数据"
//@@en="Recording does not start, but Native PCM data is received"
,"rCAM:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="检测到跨域iframe，NativeRecordReceivePCM无法注入到顶层，已监听postMessage转发兼容传输数据，请自行实现将top层接收到数据转发到本iframe（不限层），不然无法接收到录音数据"
//@@en="A cross-domain iframe is detected. NativeRecordReceivePCM cannot be injected into the top layer. It has listened to postMessage to be compatible with data transmission. Please implement it by yourself to forward the data received by the top layer to this iframe (no limit on layer), otherwise the recording data cannot be received."
,"t2OF:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音"
//@@en="Recording not started"
,"Z2y2:"+ //no args
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=app-support/app-native-support.js ***************



//*************** Begin srcFile=app-support/app.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="重复导入{1}"
//@@en="Duplicate import {1}"
//@@Put0
 "uXtA:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="注意：因为并发调用了其他录音相关方法，当前 {1} 的调用结果已被丢弃且不会有回调"
//@@en="Note: Because other recording-related methods are called concurrently, the current call result of {1} has been discarded and there will be no callback"
,"kIBu:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="重复注册{1}"
//@@en="Duplicate registration {1}"
,"ha2K:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="仅清理资源"
//@@en="Clean resources only"
,"wpTL:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音"
//@@en="Recording not started"
,"bpvP:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前环境不支持实时回调，无法进行{1}"
//@@en="The current environment does not support real-time callback and cannot be performed {1}"
,"fLJD:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="录音权限请求失败："
//@@en="Recording permission request failed: "
,"YnzX:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="需先调用{1}"
//@@en="Need to call {1} first"
,"nwKR:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="当前不是浏览器环境，需引入针对此平台的支持文件（{1}），或调用{2}自行实现接入"
//@@en="This is not a browser environment. You need to import support files for this platform ({1}), or call {2} to implement the access yourself."
,"citA:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="开始录音失败："
//@@en="Failed to start recording: "
,"ecp9:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="不能录音："
//@@en="Cannot record: "
,"EKmS:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="已开始录音"
//@@en="Recording started"
,"k7Qo:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="结束录音失败："
//@@en="Failed to stop recording: "
,"Douz:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="和Start时差：{1}ms"
//@@en="Time difference from Start: {1}ms"
,"wqSH:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="结束录音 耗时{1}ms 音频时长{2}ms 文件大小{3}b {4}"
//@@en="Stop recording, takes {1}ms, audio duration {2}ms, file size {3}b, {4}"
,"g3VX:"+ //args: {1}-{4}
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=app-support/app.js ***************

//@@User Code-2 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-2 End @@

}));