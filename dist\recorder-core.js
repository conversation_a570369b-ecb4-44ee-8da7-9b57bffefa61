/*
錄音
https://github.com/xiangyuecn/Recorder
src: recorder-core.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=e?window:Object;!function(e,t){"use strict";var r=function(){},n=function(e){return"number"==typeof e},a=function(e){return JSON.stringify(e)},o=function(e){return new O(e)},i=o.LM="2025-01-11 09:28",s="https://github.com/xiangyuecn/Recorder",c="Recorder",f="getUserMedia",u="srcSampleRate",l="sampleRate",v="bitRate",p="catch",h=e[c];if(h&&h.LM==i)return void h.CLog(h.i18n.$T("K8zP::重复导入{1}",0,c),3);o.IsOpen=function(){var e=o.Stream;if(e){var t=I(e)[0];if(t){var r=t.readyState;return"live"==r||r==t.LIVE}}return!1},o.BufferSize=4096,o.Destroy=function(){for(var e in T(c+" Destroy"),k(),g)g[e]()};var g={};o.BindDestroy=function(e,t){g[e]=t},o.Support=function(){if(!t)return!1;var e=navigator.mediaDevices||{};return e[f]||(e=navigator)[f]||(e[f]=e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia),!!e[f]&&(o.Scope=e,!!o.GetContext())},o.GetContext=function(e){if(!t)return null;var r=window.AudioContext;if(r||(r=window.webkitAudioContext),!r)return null;var n=o.Ctx,a=0;return n||(n=o.Ctx=new r,a=1,o.NewCtxs=o.NewCtxs||[],o.BindDestroy("Ctx",function(){var e=o.Ctx;e&&e.close&&(m(e),o.Ctx=0);var t=o.NewCtxs;o.NewCtxs=[];for(var r=0;r<t.length;r++)m(t[r])})),e&&n.close&&(a||(n._useC||m(n),n=new r),n._useC=1,o.NewCtxs.push(n)),n},o.CloseNewCtx=function(e){if(e&&e.close){m(e);for(var t=o.NewCtxs||[],r=t.length,n=0;n<t.length;n++)if(t[n]==e){t.splice(n,1);break}T(P("mSxV::剩{1}个GetContext未close",0,r+"-1="+t.length),t.length?3:0)}};var m=function(e){if(e&&e.close&&!e._isC&&(e._isC=1,"closed"!=e.state))try{e.close()}catch(e){T("ctx close err",1,e)}},d=o.ResumeCtx=function(e,t,r,n){var a=0,o=0,i=0,s=0,c="EventListener",f="ResumeCtx ",u=function(t,f){o&&l(),a||(a=1,t&&n(t,s),f&&r(s)),f&&(!e._LsSC&&e["add"+c]&&e["add"+c]("statechange",v),e._LsSC=1,i=1)},l=function(e){if(!e||!o){o=e?1:0;for(var t=["focus","mousedown","mouseup","touchstart","touchend"],r=0;r<t.length;r++)window[(e?"add":"remove")+c](t[r],v,!0)}},v=function(){var r=e.state,n=C(r);if(!a&&!t(n?++s:s))return u();n?(i&&T(f+"sc "+r,3),l(1),e.resume().then(function(){i&&T(f+"sc "+e.state),u(0,1)})[p](function(t){T(f+"error",1,t),C(e.state)||u(t.message||"error")})):"closed"==r?(i&&!e._isC&&T(f+"sc "+r,1),u("ctx closed")):u(0,1)};v()},C=o.CtxSpEnd=function(e){return"suspended"==e||"interrupted"==e},_=function(e){var t=e.state,r="ctx.state="+t;return C(t)&&(r+=P("nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）")),r},y="ConnectEnableWebM";o[y]=!0;var b="ConnectEnableWorklet";o[b]=!1;var S=function(e){var t=e.BufferSize||o.BufferSize,r=e.Stream,n=r._c,i=n[l],s={},f=I(r)[0],u=null,v="";if(f&&f.getSettings){var h=(u=f.getSettings())[l];h&&h!=i&&(v=P("eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，",0,h,i))}r._ts=u,T(v+"Stream TrackSet: "+a(u),v?3:0);var g,m,C,_=function(e){var t=r._m=n.createMediaStreamSource(r),a=n.destination,o="createMediaStreamDestination";n[o]&&(a=r._d=n[o]()),t.connect(e),e.connect(a)},S="",x=r._call,k=function(e,t){for(var r in x){if(t!=i){s.index=0;var n=(s=o.SampleData([e],t,i,s,{_sum:1})).data,a=s._sum}else{s={};for(var c=e.length,f=(n=new Int16Array(c),a=0,0);f<c;f++){var u=Math.max(-1,Math.min(1,e[f]));u=u<0?32768*u:32767*u,n[f]=u,a+=Math.abs(u)}}for(var l in x)x[l](n,a);return}},L="ScriptProcessor",A="audioWorklet",R=c+" "+A,O="RecProc",D="MediaRecorder",E=D+".WebM.PCM",N=n.createScriptProcessor||n.createJavaScriptNode,G=P("ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。",0,A),U=function(){m=r.isWorklet=!1,w(r),T(P("7TU0::Connect采用老的{1}，",0,L)+F.get(o[b]?P("JwCL::但已设置{1}尝试启用{2}",2):P("VGjB::可设置{1}尝试启用{2}",2),[c+"."+b+"=true",A])+S+G,3);var e=r._p=N.call(n,t,1,1);_(e),e.onaudioprocess=function(e){var t=e.inputBuffer.getChannelData(0);k(t,i)}},W=function(){g=r.isWebM=!1,M(r),m=r.isWorklet=!N||o[b];var e=window.AudioWorkletNode;if(m&&n[A]&&e){var a=function(){var e=function(e){return e.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,R)},t="class "+O+" extends AudioWorkletProcessor{";return t+="constructor "+e(function(e){DEL_super(e);var t=this,r=e.processorOptions.bufferSize;t.bufferSize=r,t.buffer=new Float32Array(2*r),t.pos=0,t.port.onmessage=function(e){e.data.kill&&(t.kill=!0,$C.log("$RA kill call"))},$C.log("$RA .ctor call",e)}),t+="process "+e(function(e,t,r){var n=this,a=n.bufferSize,o=n.buffer,i=n.pos;if((e=(e[0]||[])[0]||[]).length){o.set(e,i);var s=~~((i+=e.length)/a)*a;if(s){this.port.postMessage({val:o.slice(0,s)});var c=o.subarray(s,i);(o=new Float32Array(2*a)).set(c),i=c.length,n.buffer=o}n.pos=i}return!n.kill}),t=(t+='}try{registerProcessor("'+O+'", '+O+')}catch(e){$C.error("'+R+' Reg Error",e)}').replace(/\$C\./g,"console."),"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(t)))},s=function(){return m&&r._na},f=r._na=function(){""!==C&&(clearTimeout(C),C=setTimeout(function(){C=0,s()&&(T(P("MxX1::{1}未返回任何音频，恢复使用{2}",0,A,L),3),N&&U())},500))},u=function(){if(s()){var a=r._n=new e(n,O,{processorOptions:{bufferSize:t}});_(a),a.port.onmessage=function(e){C&&(clearTimeout(C),C=""),s()?k(e.data.val,i):m||T(P("XUap::{1}多余回调",0,A),3)},T(P("yOta::Connect采用{1}，设置{2}可恢复老式{3}",0,A,c+"."+b+"=false",L)+S+G,3)}},l=function(){if(s())if(n[O])u();else{var e=a();n[A].addModule(e).then(function(e){s()&&(n[O]=1,u(),C&&f())})[p](function(e){T(A+".addModule Error",1,e),s()&&U()})}};d(n,function(){return s()},l,l)}else U()};(function(){var e=window[D],n="ondataavailable",a="audio/webm; codecs=pcm";g=r.isWebM=o[y];var i=e&&n in e.prototype&&e.isTypeSupported(a);if(S=i?"":P("VwPd::（此浏览器不支持{1}）",0,E),g&&i){var s=function(){return g&&r._ra};r._ra=function(){""!==C&&(clearTimeout(C),C=setTimeout(function(){s()&&(T(P("vHnb::{1}未返回任何音频，降级使用{2}",0,D,A),3),W())},500))};var f=Object.assign({mimeType:a},o.ConnectWebMOptions),u=r._r=new e(r,f),l=r._rd={};u[n]=function(e){var t=new FileReader;t.onloadend=function(){if(s()){var e=z(new Uint8Array(t.result),l);if(!e)return;if(-1==e)return void W();C&&(clearTimeout(C),C=""),k(e,l.webmSR)}else g||T(P("O9P7::{1}多余回调",0,D),3)},t.readAsArrayBuffer(e.data)};try{u.start(~~(t/48)),T(P("LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}",0,E,c+"."+y+"=false",A,L))}catch(e){T("mr start err",1,e),W()}}else W()})()},x=function(e){e._na&&e._na(),e._ra&&e._ra()},w=function(e){e._na=null,e._n&&(e._n.port.postMessage({kill:!0}),e._n.disconnect(),e._n=null)},M=function(e){if(e._ra=null,e._r){try{e._r.stop()}catch(e){T("mr stop err",1,e)}e._r=null}},k=function(e){var t=(e=e||o)==o,r=e.Stream;r&&(r._m&&(r._m.disconnect(),r._m=null),!r._RC&&r._c&&o.CloseNewCtx(r._c),r._RC=null,r._c=null,r._d&&(L(r._d.stream),r._d=null),r._p&&(r._p.disconnect(),r._p.onaudioprocess=r._p=null),w(r),M(r),t&&L(r)),e.Stream=0},L=o.StopS_=function(e){for(var t=I(e),r=0;r<t.length;r++){var n=t[r];n.stop&&n.stop()}e.stop&&e.stop()},I=function(e){var t=0,r=0,n=[];e.getAudioTracks&&(t=e.getAudioTracks(),r=e.getVideoTracks()),t||(t=e.audioTracks,r=e.videoTracks);for(var a=0,o=t?t.length:0;a<o;a++)n.push(t[a]);for(a=0,o=r?r.length:0;a<o;a++)n.push(r[a]);return n};o.SampleData=function(e,t,r,n,a){var i="SampleData";n||(n={});var s,c=n.index||0,f=n.offset||0,u=n.raisePrev||0,l=n.filter;(l&&l.fn&&(l.sr&&l.sr!=t||l.srn&&l.srn!=r)&&(l=null,T(P("d48C::{1}的filter采样率变了，重设滤波",0,i),3)),l)||(l=r<=t?{fn:(s=r>3*t/4?0:r/2*3/4)?o.IIRFilter(!0,t,s):0}:{fn:(s=t>3*r/4?0:t/2*3/4)?o.IIRFilter(!0,r,s):0});l.sr=t,l.srn=r;var v=l.fn,p=n.frameNext||[];a||(a={});var h=a.frameSize||1;a.frameType&&(h="mp3"==a.frameType?1152:1);var g=a._sum,m=0,d=e.length;c>d+1&&T(P("tlbC::{1}似乎传入了未重置chunk {2}",0,i,c+">"+d),3);for(var C=0,_=c;_<d;_++)C+=e[_].length;var y=t/r;if(y>1)C=Math.max(0,C-Math.floor(f)),C=Math.floor(C/y);else if(y<1){var b=1/y;C=Math.floor(C*b)}C+=p.length;var S=new Int16Array(C),x=0;for(_=0;_<p.length;_++)S[x]=p[_],x++;for(;c<d;c++){var w=e[c],M=w instanceof Float32Array,k=(_=f,w.length),L=v&&v.Embed,I=0,A=0,R=0,O=0;if(y<1){for(var z=x+_,D=u,E=0;E<k;E++){var N=w[E];M&&(N=(N=Math.max(-1,Math.min(1,N)))<0?32768*N:32767*N);var G=Math.floor(z);z+=b;for(var F=Math.floor(z),U=(N-D)/(F-G),W=1;G<F;G++,W++){var B=Math.floor(D+W*U);L?(R=B,O=L.b0*R+L.b1*L.x1+L.b0*L.x2-L.a1*L.y1-L.a2*L.y2,L.x2=L.x1,L.x1=R,L.y2=L.y1,L.y1=O,B=O):B=v?v(B):B,B>32767?B=32767:B<-32768&&(B=-32768),g&&(m+=Math.abs(B)),S[G]=B,x++}D=u=N,_+=b}f=_%1}else{E=0;for(var $=0;E<k;E++,$++){if($<k){N=w[$];M&&(N=(N=Math.max(-1,Math.min(1,N)))<0?32768*N:32767*N),L?(R=N,O=L.b0*R+L.b1*L.x1+L.b0*L.x2-L.a1*L.y1-L.a2*L.y2,L.x2=L.x1,L.x1=R,L.y2=L.y1,L.y1=O):O=v?v(N):N}if(I=A,A=O,0!=$){var j=Math.floor(_);if(E==j){var V=I+((Math.ceil(_)<k?A:I)-I)*(_-j);V>32767?V=32767:V<-32768&&(V=-32768),g&&(m+=Math.abs(V)),S[x]=V,x++,_+=y}}else E--}f=Math.max(0,_-k)}}y<1&&x+1==C&&(C--,S=new Int16Array(S.buffer.slice(0,2*C))),x-1!=C&&x!=C&&T(i+" idx:"+x+" != size:"+C,3),p=null;var H=C%h;if(H>0){var J=2*(C-H);p=new Int16Array(S.buffer.slice(J)),S=new Int16Array(S.buffer.slice(0,J))}var K={index:c,offset:f,raisePrev:u,filter:l,frameNext:p,sampleRate:r,data:S};return g&&(K._sum=m),K},o.IIRFilter=function(e,t,r){var n=2*Math.PI*r/t,a=Math.sin(n),o=Math.cos(n),i=a/2,s=1+i,c=-2*o/s,f=(1-i)/s;if(e)var u=(1-o)/2/s,l=(1-o)/s;else u=(1+o)/2/s,l=-(1+o)/s;var v=0,p=0,h=0,g=0,m=0,d=function(e){return h=u*e+l*v+u*p-c*g-f*m,p=v,v=e,m=g,g=h,h};return d.Embed={x1:0,x2:0,y1:0,y2:0,b0:u,b1:l,a1:c,a2:f},d},o.PowerLevel=function(e,t){var r=e/t||0;return r<1251?Math.round(r/1250*10):Math.round(Math.min(100,Math.max(0,100*(1+Math.log(r/1e4)/Math.log(10)))))},o.PowerDBFS=function(e){var t=Math.max(.1,e||0),r=32767;return t=Math.min(t,r),t=20*Math.log(t/r)/Math.log(10),Math.max(-100,Math.round(t))},o.CLog=function(e,t){if("object"==typeof console){var r=new Date,a=("0"+r.getMinutes()).substr(-2)+":"+("0"+r.getSeconds()).substr(-2)+"."+("00"+r.getMilliseconds()).substr(-3),i=this&&this.envIn&&this.envCheck&&this.id,s=["["+a+" "+c+(i?":"+i:"")+"]"+e],f=arguments,u=o.CLog,l=2,v=u.log||console.log;for(n(t)?v=1==t?u.error||console.error:3==t?u.warn||console.warn:v:l=1;l<f.length;l++)s.push(f[l]);A?v&&v("[IsLoser]"+s[0],s.length>1?s:""):v.apply(console,s)}};var T=function(){o.CLog.apply(this,arguments)},A=!0;try{A=!console.log.apply}catch(e){}var R=0;function O(e){var t=this;t.id=++R;var a={type:"mp3",onProcess:r};for(var o in e)a[o]=e[o];t.set=a;var i=a[v],s=a[l];(i&&!n(i)||s&&!n(s))&&t.CLog(P.G("IllegalArgs-1",[P("VtS4::{1}和{2}必须是数值",0,l,v)]),1,e),a[v]=+i||16,a[l]=+s||16e3,t.state=0,t._S=9,t.Sync={O:9,C:9}}o.Sync={O:9,C:9},o.prototype=O.prototype={CLog:T,_streamStore:function(){return this.set.sourceStream?this:o},_streamGet:function(){return this._streamStore().Stream},_streamCtx:function(){var e=this._streamGet();return e&&e._c},open:function(e,n){var c=this,u=c.set,v=c._streamStore(),h=0;e=e||r;var g=function(e,t){t=!!t,c.CLog(P("5tWi::录音open失败：")+e+",isUserNotAllow:"+t,1),h&&o.CloseNewCtx(h),n&&n(e,t)};c._streamTag=f;var m=function(){c.CLog("open ok, id:"+c.id+" stream:"+c._streamTag),e(),c._SO=0},d=v.Sync,C=++d.O,y=d.C;c._O=c._O_=C,c._SO=c._S;var b=function(){if(y!=d.C||!c._O){var e=P("dFm8::open被取消");return C==d.O?c.close():e=P("VtJO::open被中断"),g(e),!0}};if(t){var x=c.envCheck({envName:"H5",canProcess:!0});if(x)g(P("A5bm::不能录音：")+x);else{var w,M=function(){(w=u.runningContext)||(w=h=o.GetContext(!0))};if(u.sourceStream){if(c._streamTag="set.sourceStream",!o.GetContext())return void g(P("1iU7::不支持此浏览器从流中获取录音"));M(),k(v);var L=c.Stream=u.sourceStream;L._c=w,L._RC=u.runningContext,L._call={};try{S(v)}catch(e){return k(v),void g(P("BTW2::从流中打开录音失败：")+e.message)}m()}else{var I=function(e,t){try{window.top.a}catch(e){return void g(P("Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})",0,'allow="camera;microphone"'))}T(1,e)&&(/Found/i.test(e)?g(t+P("jBa9::，无可用麦克风")):g(t))},T=function(e,t){if(/Permission|Allow/i.test(t))e&&g(P("gyO5::用户拒绝了录音权限"),!0);else{if(!1!==window.isSecureContext)return 1;e&&g(P("oWNo::浏览器禁止不安全页面录音，可开启https解决"))}};if(o.IsOpen())m();else if(o.Support()){M();var A,R,O=function(e){setTimeout(function(){e._call={};var t=o.Stream;t&&(k(),e._call=t._call),o.Stream=e,e._c=w,e._RC=u.runningContext,b()||(o.IsOpen()?(t&&c.CLog(P("upb8::发现同时多次调用open"),1),S(v),m()):g(P("Q1GA::录音功能无效：无音频流")))},100)},z=function(e){var t=e.name||e.message||e.code+":"+e,r="";1==D&&T(0,t)&&(r=P("KxE2::，将尝试禁用回声消除后重试"));var n=P("xEQR::请求录音权限错误"),a=P("bDOG::无法录音：");c.CLog(n+r+"|"+e,r||R?3:1,e),r?(A=t,R=e,E(1)):R?(c.CLog(n+"|"+R,1,R),I(A,a+R)):I(t,a+e)},D=0,E=function(e){D++;var t="audioTrackSet",r="autoGainControl",n="echoCancellation",v="noiseSuppression",h=t+":{"+n+","+v+","+r+"}",g=JSON.parse(a(u[t]||!0));c.CLog("open... "+D+" "+t+":"+a(g)),e&&("object"!=typeof g&&(g={}),g[r]=!1,g[n]=!1,g[v]=!1),g[l]&&c.CLog(P("IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象",0,t+"."+l),3);var m={audio:g,video:u.videoTrackSet||!1};try{var d=o.Scope[f](m,O,z)}catch(e){c.CLog(f,3,e),m={audio:!0,video:!1},d=o.Scope[f](m,O,z)}c.CLog(f+"("+a(m)+") "+_(w)+P("RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置",0,h,t)+"("+s+") LM:"+i+" UA:"+navigator.userAgent),d&&d.then&&d.then(O)[p](z)};E()}else I("",P("COxc::此浏览器不支持录音"))}}}else g(P.G("NonBrowser-1",["open"])+P("EMJq::，可尝试使用RecordApp解决方案")+"("+s+"/tree/master/app-support-sample)")},close:function(e){e=e||r;var t=this,n=t._streamStore();t._stop();var a=" stream:"+t._streamTag,o=n.Sync;if(t._O=0,t._O_!=o.O)return t.CLog(P("hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）")+a,3),void e();o.C++,k(n),t.CLog("close,"+a),e()},mock:function(e,t){var r=this;return r._stop(),r.isMock=1,r.mockEnvInfo=null,r.buffers=[e],r.recSize=e.length,r._setSrcSR(t),r._streamTag="mock",r},_setSrcSR:function(e){var t=this,r=t.set,n=r[l];n>e?r[l]=e:n=0,t[u]=e,t.CLog(u+": "+e+" set."+l+": "+r[l]+(n?" "+P("UHvm::忽略")+": "+n:""),n?3:0)},envCheck:function(e){var t,r=this,n=r.set,a="CPU_BE";if(t||o[a]||"function"!=typeof Int8Array||new Int8Array(new Int32Array([1]).buffer)[0]||(t=P("Essp::不支持{1}架构",0,a)),!t){var i=n.type,s=r[i+"_envCheck"];n.takeoffEncodeChunk&&(s?e.canProcess||(t=P("7uMV::{1}环境不支持实时处理",0,e.envName)):t=P("2XBl::{1}类型不支持设置takeoffEncodeChunk",0,i)+(r[i]?"":P("LG7e::(未加载编码器)"))),!t&&s&&(t=r[i+"_envCheck"](e,n))}return t||""},envStart:function(e,t){var r=this,n=r.set;if(r.isMock=e?1:0,r.mockEnvInfo=e,r.buffers=[],r.recSize=0,e&&(r._streamTag="env$"+e.envName),r.state=1,r.envInLast=0,r.envInFirst=0,r.envInFix=0,r.envInFixTs=[],r._setSrcSR(t),r.engineCtx=0,r[n.type+"_start"]){var a=r.engineCtx=r[n.type+"_start"](n);a&&(a.pcmDatas=[],a.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(e,t){var r=this,n=r.set,a=r.engineCtx;if(1==r.state){var i=r[u],s=e.length,c=o.PowerLevel(t,s),f=r.buffers,v=f.length;f.push(e);var p=f,h=v,g=Date.now(),m=Math.round(s/i*1e3);r.envInLast=g,1==r.buffers.length&&(r.envInFirst=g-m);var d=r.envInFixTs;d.splice(0,0,{t:g,d:m});for(var C=g,_=0,y=0;y<d.length;y++){var b=d[y];if(g-b.t>3e3){d.length=y;break}C=b.t,_+=b.d}var S=d[1],x=g-C;if(x-_>x/3&&(S&&x>1e3||d.length>=6)){var w=g-S.t-m;if(w>m/5){var M=!n.disableEnvInFix;if(r.CLog("["+g+"]"+F.get(P(M?"4Kfd::补偿{1}ms":"bM5i::未补偿{1}ms",1),[w]),3),r.envInFix+=w,M){var k=new Int16Array(w*i/1e3);s+=k.length,f.push(k)}}}var L=r.recSize,I=s,T=L+I;if(r.recSize=T,a){var A=o.SampleData(f,i,n[l],a.chunkInfo);a.chunkInfo=A,T=(L=a.pcmSize)+(I=A.data.length),a.pcmSize=T,f=a.pcmDatas,v=f.length,f.push(A.data),i=A[l]}var R=Math.round(T/i*1e3),O=f.length,z=p.length,D=function(){for(var e=E?0:-I,t=null==f[0],o=v;o<O;o++){var i=f[o];null==i?t=1:(e+=i.length,a&&i.length&&r[n.type+"_encode"](a,i))}if(t&&a){o=h;for(p[0]&&(o=0);o<z;o++)p[o]=null}t&&(e=E?I:0,f[0]=null),a?a.pcmSize+=e:r.recSize+=e},E=0,N="rec.set.onProcess";try{E=!0===(E=n.onProcess(f,c,R,i,v,D))}catch(e){console.error(N+P("gFUF::回调出错是不允许的，需保证不会抛异常"),e)}var G=Date.now()-g;if(G>10&&r.envInFirst-g>1e3&&r.CLog(N+P("2ghS::低性能，耗时{1}ms",0,G),3),E){var U=0;for(y=v;y<O;y++)null==f[y]?U=1:f[y]=new Int16Array(0);U?r.CLog(P("ufqH::未进入异步前不能清除buffers"),3):a?a.pcmSize-=I:r.recSize-=I}else D()}else r.state||r.CLog("envIn at state=0",3)},start:function(){var e=this,t=1;if(e.set.sourceStream?e.Stream||(t=0):o.IsOpen()||(t=0),t){var r=e._streamCtx();if(e.CLog(P("kLDN::start 开始录音，")+_(r)+" stream:"+e._streamTag),e._stop(),e.envStart(null,r[l]),e.state=3,e._SO&&e._SO+1!=e._S)e.CLog(P("Bp2y::start被中断"),3);else{e._SO=0;var n=function(){3==e.state&&(e.state=1,e.resume())},a="AudioContext resume: ";e._streamGet()._call[e.id]=function(){e.CLog(a+r.state+"|stream ok"),n()},d(r,function(t){return t&&e.CLog(a+"wait..."),3==e.state},function(t){t&&e.CLog(a+r.state),n()},function(t){e.CLog(a+r.state+P("upkE::，可能无法录音：")+t,1),n()})}}else e.CLog(P("6WmN::start失败：未open"),1)},pause:function(){var e=this,t=e._streamGet();e.state&&(e.state=2,e.CLog("pause"),t&&delete t._call[e.id])},resume:function(){var e=this,t=e._streamGet(),r="resume",n=r+"(wait ctx)";if(3==e.state)e.CLog(n);else if(e.state){e.state=1,e.CLog(r),e.envResume(),t&&(t._call[e.id]=function(t,r){1==e.state&&e.envIn(t,r)},x(t));var a=e._streamCtx();a&&d(a,function(t){return t&&e.CLog(n+"..."),1==e.state},function(r){r&&e.CLog(n+a.state),x(t)},function(t){e.CLog(n+a.state+"[err]"+t,1)})}},_stop:function(e){var t=this,r=t.set;t.isMock||t._S++,t.state&&(t.pause(),t.state=0),!e&&t[r.type+"_stop"]&&(t[r.type+"_stop"](t.engineCtx),t.engineCtx=0)},stop:function(e,t,r){var n,a=this,f=a.set,v=a.envInLast-a.envInFirst,p=v&&a.buffers.length;a.CLog(P("Xq4s::stop 和start时差:")+(v?v+"ms "+P("3CQP::补偿:")+a.envInFix+"ms envIn:"+p+" fps:"+(p/v*1e3).toFixed(1):"-")+" stream:"+a._streamTag+" ("+s+") LM:"+i);var h=function(){a._stop(),r&&a.close()},g=function(e){a.CLog(P("u8JG::结束录音失败：")+e,1),t&&t(e),h()},m=function(t,r,i){var s="blob",u="arraybuffer",l="dataType",v="DefaultDataType",p=a[l]||o[v]||s,m=l+"="+p,d=t instanceof ArrayBuffer,C=0,_=d?t.byteLength:t.size;if(p==u?d||(C=1):p==s?"function"!=typeof Blob?C=P.G("NonBrowser-1",[m])+P("1skY::，请设置{1}",0,c+"."+v+'="'+u+'"'):(d&&(t=new Blob([t],{type:r})),t instanceof Blob||(C=1),r=t.type||r):C=P.G("NotSupport-1",[m]),a.CLog(P("Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b",0,Date.now()-n,i,_)+" "+m+","+r),C)g(1!=C?C:P("Vkbd::{1}编码器返回的不是{2}",0,f.type,p)+", "+m);else{if(f.takeoffEncodeChunk)a.CLog(P("QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"),3);else if(_<Math.max(50,i/5))return void g(P("Sz2H::生成的{1}无效",0,f.type));e&&e(t,i,r),h()}};if(!a.isMock){var d=3==a.state;if(!a.state||d)return void g(P("wf9t::未开始录音")+(d?P("Dl2c::，开始录音前无用户交互导致AudioContext未运行"):""))}a._stop(!0);var C=a.recSize;if(C)if(a[f.type]){if(a.isMock){var _=a.envCheck(a.mockEnvInfo||{envName:"mock",canProcess:!1});if(_)return void g(P("AxOH::录音错误：")+_)}var y=a.engineCtx;if(a[f.type+"_complete"]&&y){var b=Math.round(y.pcmSize/f[l]*1e3);return n=Date.now(),void a[f.type+"_complete"](y,function(e,t){m(e,t,b)},g)}if(n=Date.now(),a.buffers[0]){var S=o.SampleData(a.buffers,a[u],f[l]);f[l]=S[l];var x=S.data;b=Math.round(x.length/f[l]*1e3);a.CLog(P("CxeT::采样:{1} 花:{2}ms",0,C+"->"+x.length,Date.now()-n)),setTimeout(function(){n=Date.now(),a[f.type](x,function(e,t){m(e,t,b)},function(e){g(e)})})}else g(P("xkKd::音频buffers被释放"))}else g(P("xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载",0,f.type,c));else g(P("Ltz3::未采集到录音"))}};var z=function(e,t){t.pos||(t.pos=[0],t.tracks={},t.bytes=[]);var r=t.tracks,n=[t.pos[0]],a=function(){t.pos[0]=n[0]},o=t.bytes.length,i=new Uint8Array(o+e.length);if(i.set(t.bytes),i.set(e,o),t.bytes=i,!t._ht){if(N(i,n),G(i,n),!D(N(i,n),[24,83,128,103]))return;for(N(i,n);n[0]<i.length;){var s=N(i,n),c=G(i,n),f=[0],u=0;if(!c)return;if(D(s,[22,84,174,107])){for(;f[0]<c.length;){var v=N(c,f),p=G(c,f),h=[0],g={channels:0,sampleRate:0};if(D(v,[174]))for(;h[0]<p.length;){var m=N(p,h),d=G(p,h),C=[0];if(D(m,[215])){var _=E(d);g.number=_,r[_]=g}else if(D(m,[131])){1==(_=E(d))?g.type="video":2==_?(g.type="audio",u||(t.track0=g),g.idx=u++):g.type="Type-"+_}else if(D(m,[134])){for(var y="",b=0;b<d.length;b++)y+=String.fromCharCode(d[b]);g.codec=y}else if(D(m,[225]))for(;C[0]<d.length;){var S=N(d,C),x=G(d,C);if(D(S,[181])){_=0;var w=new Uint8Array(x.reverse()).buffer;4==x.length?_=new Float32Array(w)[0]:8==x.length?_=new Float64Array(w)[0]:T("WebM Track !Float",1,x),g[l]=Math.round(_)}else D(S,[98,100])?g.bitDepth=E(x):D(S,[159])&&(g.channels=E(x))}}}t._ht=1,T("WebM Tracks",r),a();break}}}var M=t.track0;if(M){var k=M[l];if(t.webmSR=k,16==M.bitDepth&&/FLOAT/i.test(M.codec)&&(M.bitDepth=32,T("WebM 16->32 bit",3)),k<8e3||32!=M.bitDepth||M.channels<1||!/(\b|_)PCM\b/i.test(M.codec))return t.bytes=[],t.bad||T("WebM Track Unexpected",3,t),t.bad=1,-1;for(var L=[],I=0;n[0]<i.length;){v=N(i,n);if(!(p=G(i,n)))break;if(D(v,[163])){var A=15&p[0];if(!(g=r[A]))return T("WebM !Track"+A,1,r),-1;if(0===g.idx){var R=new Uint8Array(p.length-4);for(b=4;b<p.length;b++)R[b-4]=p[b];L.push(R),I+=R.length}}a()}if(I){var O=new Uint8Array(i.length-t.pos[0]);O.set(i.subarray(t.pos[0])),t.bytes=O,t.pos[0]=0;R=new Uint8Array(I),b=0;for(var z=0;b<L.length;b++)R.set(L[b],z),z+=L[b].length;w=new Float32Array(R.buffer);if(M.channels>1){var F=[];for(b=0;b<w.length;)F.push(w[b]),b+=M.channels;w=new Float32Array(F)}return w}}},D=function(e,t){if(!e||e.length!=t.length)return!1;if(1==e.length)return e[0]==t[0];for(var r=0;r<e.length;r++)if(e[r]!=t[r])return!1;return!0},E=function(e){for(var t="",r=0;r<e.length;r++){var n=e[r];t+=(n<16?"0":"")+n.toString(16)}return parseInt(t,16)||0},N=function(e,t,r){var n=t[0];if(!(n>=e.length)){var a=("0000000"+e[n].toString(2)).substr(-8),o=/^(0*1)(\d*)$/.exec(a);if(o){var i=o[1].length,s=[];if(!(n+i>e.length)){for(var c=0;c<i;c++)s[c]=e[n],n++;return r&&(s[0]=parseInt(o[2]||"0",2)),t[0]=n,s}}}},G=function(e,t){var r=N(e,t,1);if(r){var n=E(r),a=t[0],o=[];if(n<2147483647){if(a+n>e.length)return;for(var i=0;i<n;i++)o[i]=e[a],a++}return t[0]=a,o}},F=o.i18n={lang:"zh-CN",alias:{"zh-CN":"zh","en-US":"en"},locales:{},data:{},put:function(e,t){var r=c+".i18n.put: ",n=e.overwrite;n=null==n||n;var a=e.lang;if(!(a=F.alias[a]||a))throw new Error(r+"set.lang?");var o=F.locales[a];o||(o={},F.locales[a]=o);for(var i,s=/^([\w\-]+):/,f=0;f<t.length;f++){var u=t[f];if(i=s.exec(u)){var l=i[1];u=u.substr(l.length+1);!n&&o[l]||(o[l]=u)}else T(r+"'key:'? "+u,3,e)}},get:function(){return F.v_G.apply(null,arguments)},v_G:function(e,t,r){t=t||[],r=r||F.lang,r=F.alias[r]||r;var n=F.locales[r],a=n&&n[e]||"";return a||"zh"==r?(F.lastLang=r,"=Empty"==a?"":a.replace(/\{(\d+)(\!?)\}/g,function(r,n,o){return r=t[(n=+n||0)-1],(n<1||n>t.length)&&(r="{?}",T("i18n["+e+"] no {"+n+"}: "+a,3)),o?"":r})):"en"==r?F.v_G(e,t,"zh"):F.v_G(e,t,"en")},$T:function(){return F.v_T.apply(null,arguments)},v_T:function(){for(var e,t=arguments,r="",a=[],o=0,i=c+".i18n.$T:",s=/^([\w\-]*):/,f=0;f<t.length;f++){var u=t[f];if(0==f){if(!(r=(e=s.exec(u))&&e[1]))throw new Error(i+"0 'key:'?");u=u.substr(r.length+1)}if(-1===o)a.push(u);else{if(o)throw new Error(i+" bad args");if(0===u)o=-1;else if(n(u)){if(u<1)throw new Error(i+" bad args");o=u}else{var l=1==f?"en":f?"":"zh";if((e=s.exec(u))&&(l=e[1]||l,u=u.substr(e[1].length+1)),!e||!l)throw new Error(i+f+" 'lang:'?");F.put({lang:l,overwrite:!1},[r+":"+u])}}}return r?o>0?r:F.v_G(r,a):""}},P=F.$T;P.G=F.get,P("NonBrowser-1::非浏览器环境，不支持{1}",1),P("IllegalArgs-1::参数错误：{1}",1),P("NeedImport-2::调用{1}需要先导入{2}",2),P("NotSupport-1::不支持：{1}",1),o.TrafficImgUrl="";o.Traffic=function(e){if(t){e=e?"/"+c+"/Report/"+e:"";var r=o.TrafficImgUrl;if(r){var n=o.Traffic,a=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],i=a[1]||"http://file/",s=(a[0]||i)+e;if(0==r.indexOf("//")&&(r=/^https:/i.test(s)?"https:"+r:"http:"+r),e&&(r=r+"&cu="+encodeURIComponent(i+e)),!n[s])n[s]=1,(new Image).src=r,T("Traffic Analysis Image: "+(e||c+".TrafficImgUrl="+o.TrafficImgUrl))}}};h&&(T(P("8HO5::覆盖导入{1}",0,c),1),h.Destroy());e[c]=o}(t,e),"function"==typeof define&&define.amd&&define(function(){return t.Recorder}),"object"==typeof module&&module.exports&&(module.exports=t.Recorder)}();