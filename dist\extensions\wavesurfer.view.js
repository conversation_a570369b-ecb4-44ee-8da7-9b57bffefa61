/*
錄音
https://github.com/xiangyuecn/Recorder
src: extensions/wavesurfer.view.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,a=t.i18n;!function(e,t,a,r){"use strict";var n=function(e){return new o(e)},i="WaveSurferView",o=function(e){var t=this,n={scale:2,fps:50,duration:2500,direction:1,position:0,centerHeight:1,linear:[0,"rgba(0,187,17,1)",.7,"rgba(255,215,0,1)",1,"rgba(255,102,0,1)"],centerColor:""};for(var o in e)n[o]=e[o];t.set=e=n;var l="compatibleCanvas";if(e[l])var c=t.canvas=e[l],s=t.canvas2=e[l+"_2x"];else{if(!r)throw new Error(a.G("NonBrowser-1",[i]));var h=e.elem;h&&("string"==typeof h?h=document.querySelector(h):h.length&&(h=h[0])),h&&(e.width=h.offsetWidth,e.height=h.offsetHeight);var f=t.elem=document.createElement("div");f.style.fontSize=0,f.innerHTML='<canvas style="width:100%;height:100%;"/>';c=t.canvas=f.querySelector("canvas"),s=t.canvas2=document.createElement("canvas");h&&(h.innerHTML="",h.appendChild(f))}var d=e.scale,v=e.width*d,m=e.height*d;if(!v||!m)throw new Error(a.G("IllegalArgs-1",[i+" width=0 height=0"]));c.width=v,c.height=m;t.ctx=c.getContext("2d");s.width=2*v,s.height=m;t.ctx2=s.getContext("2d");t.x=0};o.prototype=n.prototype={genLinear:function(e,t,a,r){for(var n=e.createLinearGradient(0,a,0,r),i=0;i<t.length;)n.addColorStop(t[i++],t[i++]);return n},input:function(e,t,a){var r=this;r.sampleRate=a,r.pcmData=e,r.pcmPos=0,r.inputTime=Date.now(),r.schedule()},schedule:function(){var e=this,t=e.set,a=Math.floor(1e3/t.fps);e.timer||(e.timer=setInterval(function(){e.schedule()},a));var r=Date.now();if(!(r-(e.drawTime||0)<a)){e.drawTime=r;for(var n=e.sampleRate/t.fps,i=e.pcmData,o=e.pcmPos,l=new Int16Array(Math.min(n,i.length-o)),c=0;c<l.length;c++,o++)l[c]=i[o];e.pcmPos=o,l.length?e.draw(l,e.sampleRate):r-e.inputTime>1300&&(clearInterval(e.timer),e.timer=0)}},draw:function(e,t){var a=this,r=a.set,n=a.ctx2,i=r.scale,o=r.width*i,l=2*o,c=r.height*i,s=1*i,h=r.position,f=Math.abs(r.position),d=1==h?0:c,v=c;f<1&&(d=v/=2,v=Math.floor(v*(1+f)),d=Math.floor(h>0?d*(1-f):d*(1+f)));var m=1e3*e.length/t*o/r.duration,w=0;(m+=a.drawLoss||0)<s?a.drawLoss=m:(a.drawLoss=0,w=Math.floor(m/s));for(var g=a.genLinear(n,r.linear,d,d-v),p=a.genLinear(n,r.linear,d,d+v),u=a.x,M=e.length/w,x=0,y=0;x<w;x++){var R=Math.floor(y),L=Math.floor(y+M);y+=M;for(var b=0;R<L;R++)b=Math.max(b,Math.abs(e[R]));var S=v*Math.min(1,b/32767);0!=d&&(n.fillStyle=g,n.fillRect(u,d-S,s,S)),d!=c&&(n.fillStyle=p,n.fillRect(u,d,s,S)),(u+=s)>=l&&(n.clearRect(0,0,o,c),n.drawImage(a.canvas2,o,0,o,c,0,0,o,c),n.clearRect(o,0,o,c),u=o)}a.x=u,(n=a.ctx).clearRect(0,0,o,c);var C=r.centerHeight*i;if(C){var I=d-Math.floor(C/2);I=Math.max(I,0),I=Math.min(I,c-C),n.fillStyle=r.centerColor||r.linear[1],n.fillRect(0,I,o,C)}var T=0,H=u,D=0;H>o?(T=H-o,H=o):D=o-H,-1==r.direction?n.drawImage(a.canvas2,T,0,H,c,D,0,H,c):(n.save(),n.scale(-1,1),n.drawImage(a.canvas2,T,0,H,c,-o+D,0,H,c),n.restore())}},e[i]=n}(t,0,a.$T,e)}();