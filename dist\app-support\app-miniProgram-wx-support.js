/*
錄音
https://github.com/xiangyuecn/Recorder
src: app-support/app-miniProgram-wx-support.js
*/
!function(){var e="object"==typeof window&&!!window.document,n=(e?window:Object).Recorder,r=n.i18n;!function(e,n,r,t){"use strict";var a="object"==typeof wx&&!!wx.getRecorderManager,o=e.RecordApp,i=o.CLog,f={Support:function(e){if(a&&t){var n=window,r=n.document,o=n.location,f=r.body;if(o&&o.href&&o.reload&&f&&f.appendChild)return i("识别是浏览器但又检测到wx",3),void e(!1)}e(a)},CanProcess:function(){return!0}};o.RegisterPlatform("miniProgram-wx",f),o.MiniProgramWx_onShow=function(){h()},f.RequestPermission=function(e,n,r){l(n,r)},f.Start=function(n,r,t,a){s.param=r;var i=e(r);i.set.disableEnvInFix=!0,i.dataType="arraybuffer",s.rec=i,o.__Rec=i,_(t,a)},f.Stop=function(e,n,r){v();var t=function(n){o.__Sync(e)&&(s.rec=null),r(n)},a=s.rec;s.rec=null;var f=n?"":o.__StopOnlyClearMsg();if(a){i("rec encode: pcm:"+a.recSize+" srcSR:"+a.srcSampleRate+" set:"+JSON.stringify(s.param));var c=function(){if(o.__Sync(e))for(var n in a.set)s.param[n]=a.set[n]};if(!n)return c(),void t(f);a.stop(function(e,r,t){c(),n(e,r,t)},function(e){c(),t(e)})}else t("未开始录音"+(f?" ("+f+")":""))};var c,s=function(e,n){var r=s.rec;if(r){r._appStart||r.envStart({envName:f.Key,canProcess:f.CanProcess()},n),r._appStart=1;for(var t=0,a=0;a<e.length;a++)t+=Math.abs(e[a]);r.envIn(e,t)}else i("未开始录音，但收到wx PCM数据",3)},u=!1,l=function(e,n){if(v(),x(),u)e();else{var r=wx.getRecorderManager(),t=1;r.onStart(function(){u=!0,t&&(t=0,m(r),e())}),r.onError(function(e){var a="请求录音权限出现错误："+e.errMsg;i(a+"。"+g,1,e),t&&(t=0,m(r),n(a,!0))}),p("req",r)}},g="请自行检查wx.getSetting中的scope.record录音权限，如果用户拒绝了权限，请引导用户到小程序设置中授予录音权限。",d=0,v=function(){var e=c;c=null,e&&m(e)},m=function(e){d=Date.now(),e.stop()},p=function(e,n){var r={duration:6e5,sampleRate:48e3,encodeBitRate:32e4,numberOfChannels:1,format:"PCM",frameSize:w?1:4},t=s.param||{},a=(t.audioTrackSet||{}).echoCancellation;if("android"==S.platform){var f=t.android_audioSource,c="";null==f&&a&&(f=7),null==f&&(f=o.Default_Android_AudioSource),1==f&&(c="mic"),5==f&&(c="camcorder"),6==f&&(c="voice_recognition"),7==f&&(c="voice_communication"),c&&(r.audioSource=c)}a&&i("mg注意：iOS下无法配置回声消除，Android无此问题，建议都启用听筒播放避免回声：wx.setInnerAudioOption({speakerOn:false})",3),i("["+e+"]mg.start obj",r),n.start(r)},h=function(){c&&c.__pause&&(i("mg onShow 录音开始恢复...",3),c.resume())},_=function(e,n){v(),x(),M={},w&&i("RecorderManager.onFrameRecorded 在开发工具中测试返回的是webm格式音频，将会尝试进行解码。开发工具中录音偶尔会非常卡，建议使用真机测试（各种奇奇怪怪的毛病就都正常了）",3);var r=!1,t=1,a=function(t){r||(r=!0,t?(v(),n(t)):e())},o=c=wx.getRecorderManager();o.onInterruptionEnd(function(){o==c&&(i("mg onInterruptionEnd 录音开始恢复...",3),o.resume())}),o.onPause(function(){o==c&&(o.__pause=Date.now(),i("mg onPause 录音被打断",3))}),o.onResume(function(){if(o==c){var e=o.__pause?Date.now()-o.__pause:0,n=0;o.__pause=0,e>300&&(n=Math.min(1e3,e),s(new Int16Array(48*n),48e3)),i("mg onResume 恢复录音，填充了"+n+"ms静默",3)}}),o.onError(function(e){if(o==c){var n=e.errMsg,f="mg onError 开始录音出错：";if(!r&&!o._srt&&/fail.+is.+recording/i.test(n)){var s=600-(Date.now()-d);if(s>0)return s=Math.max(100,s),i(f+"等待"+s+"ms重试",3,e),void setTimeout(function(){o==c&&(o._srt=1,i(f+"正在重试",3),p("retry start",o))},s)}i(t>1?f+"可能无法继续录音["+t+"]。"+n:f+n+"。"+g,1,e),a("开始录音出错："+n)}}),o.onStart(function(){o==c&&(i("mg onStart 已开始录音"),o._srt=0,o._st=Date.now(),a())}),o.onStop(function(e){i("mg onStop 请勿尝试使用此原始结果中的文件路径（此原始文件的格式、采样率等和录音配置不相同）；如需本地文件：可在RecordApp.Stop回调中将得到的ArrayBuffer（二进制音频数据）用RecordApp.MiniProgramWx_WriteLocalFile接口保存到本地，即可得到有效路径。res:",e),o==c&&(!o._st||Date.now()-o._st<600?i("mg onStop但已忽略",3):(i("mg onStop 已停止录音，正在重新开始录音..."),t++,o._st=0,p("restart",o)))});var f=function(){o.onFrameRecorded(function(e){if(o==c){r||i("mg onStart未触发，但收到了onFrameRecorded",3),a();var n=e.frameBuffer;n&&n.byteLength&&(w?R(new Uint8Array(n)):s(new Int16Array(n),48e3))}}),p("start",o)},u=600-(Date.now()-d);u>0?(u=Math.max(100,u),i("mg.start距stop太近需等待"+u+"ms",3),setTimeout(function(){o==c&&f()},u)):f()};o.MiniProgramWx_WriteLocalFile=function(e,n,r,t){var a=e;"string"==typeof a&&(a={fileName:e}),e=a.fileName;var f=a.append,c=a.seekOffset,s=+c||0;c||0===c||(s=-1);var u=wx.env.USER_DATA_PATH,l=e;-1==e.indexOf(u)&&(l=u+"/"+e);var g=y[l]=y[l]||[],d=g[0],v={a:a,b:n,c:r,d:t};if(d&&d._r)return i("wx文件等待写入"+l,3),a._tk=1,void g.push(v);a._tk&&i("wx文件继续写入"+l),g.splice(0,0,v),v._r=1;var m=wx.getFileSystemManager(),p=0,h=function(){p&&m.close({fd:p}),setTimeout(function(){g.shift();var e=g.shift();e&&o.MiniProgramWx_WriteLocalFile(e.a,e.b,e.c,e.d)})},_=function(){h(),r&&r(l)},w=function(e){h();var n=e.errMsg||"-";i("wx文件"+l+"写入出错："+n,1),t&&t(n)};s>-1||f?m.open({filePath:l,flag:s>-1?"r+":"a",success:function(e){var r={fd:p=e.fd,data:n,success:_,fail:w};s>-1&&(r.position=s),m.write(r)},fail:w}):m.writeFile({filePath:l,encoding:"binary",data:n,success:_,fail:w})};var w,S,y={};o.MiniProgramWx_DeleteLocalFile=function(e,n,r){wx.getFileSystemManager().unlink({filePath:e,success:function(){n&&n()},fail:function(e){r&&r(e.errMsg||"-")}})};var b,M,x=function(){S||(S=wx.getSystemInfoSync(),(w="devtools"==S.platform?1:0)&&(b=wx.createWebAudioContext()))},R=function(e){var n=M;n.pos||(n.pos=[0],n.tracks={},n.bytes=[]);var r=n.tracks,t=[n.pos[0]],a=function(){n.pos[0]=t[0]},o=n.bytes.length,f=new Uint8Array(o+e.length);f.set(n.bytes),f.set(e,o),n.bytes=f;var c=function(){n.bytes=[],s(new Int16Array(f),48e3)};if(n.isNotWebM)c();else{if(!n._ht){for(var u=0,l=0;l<f.length;l++)if(26==f[l]&&69==f[l+1]&&223==f[l+2]&&163==f[l+3]){u=l,t[0]=l+4;break}if(!t[0])return void(f.length>5120&&(i("未识别到WebM数据，开发工具可能已支持PCM",3),n.isNotWebM=!0,c()));if(k(f,t),!A(W(f,t),[24,83,128,103]))return;for(W(f,t);t[0]<f.length;){var g=W(f,t);if(!k(f,t))return;if(A(g,[22,84,174,107])){n._ht=f.slice(u,t[0]),i("WebM Tracks",r),a();break}}}for(var d=[],v=0;t[0]<f.length;){var m=t[0],p=W(f,t);t[0];if(!k(f,t))break;if(A(p,[163])){var h=f.slice(m,t[0]);v+=h.length,d.push(h)}a()}if(v){var _=new Uint8Array(f.length-n.pos[0]);_.set(f.subarray(n.pos[0])),n.bytes=_,n.pos[0]=0;var w=[31,67,182,117,1,255,255,255,255,255,255,255];w.push(231,129,0),v+=w.length,d.splice(0,0,w),v+=n._ht.length,d.splice(0,0,n._ht);for(var S=new Uint8Array(v),y=(l=0,0);l<d.length;l++)S.set(d[l],y),y+=d[l].length;b.decodeAudioData(S.buffer,function(e){for(var n=e.getChannelData(0),r=new Int16Array(n.length),t=0;t<n.length;t++){var a=Math.max(-1,Math.min(1,n[t]));a=a<0?32768*a:32767*a,r[t]=a}s(r,e.sampleRate)},function(){i("WebM解码失败",1)})}}},A=function(e,n){if(!e||e.length!=n.length)return!1;if(1==e.length)return e[0]==n[0];for(var r=0;r<e.length;r++)if(e[r]!=n[r])return!1;return!0},P=function(e){for(var n="",r=0;r<e.length;r++){var t=e[r];n+=(t<16?"0":"")+t.toString(16)}return parseInt(n,16)||0},W=function(e,n,r){var t=n[0];if(!(t>=e.length)){var a=("0000000"+e[t].toString(2)).substr(-8),o=/^(0*1)(\d*)$/.exec(a);if(o){var i=o[1].length,f=[];if(!(t+i>e.length)){for(var c=0;c<i;c++)f[c]=e[t],t++;return r&&(f[0]=parseInt(o[2]||"0",2)),n[0]=t,f}}}},k=function(e,n){var r=W(e,n,1);if(r){var t=P(r),a=n[0],o=[];if(t<2147483647){if(a+t>e.length)return;for(var i=0;i<t;i++)o[i]=e[a],a++}return n[0]=a,o}}}(n,0,r.$T,e)}();