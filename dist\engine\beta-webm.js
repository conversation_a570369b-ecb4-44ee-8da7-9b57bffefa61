/*
錄音
https://github.com/xiangyuecn/Recorder
src: engine/beta-webm.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,n=t.i18n;!function(e,t,n,o){"use strict";var r="audio/webm",a=o&&window.MediaRecorder&&MediaRecorder.isTypeSupported(r);e.prototype.enc_webm={stable:!1,getTestMsg:function(){return n(a?"tsTW::只有比较新的浏览器支持，压缩率和mp3差不多。由于未找到对已有pcm数据进行快速编码的方法，只能按照类似边播放边收听形式把数据导入到MediaRecorder，有几秒就要等几秒。输出音频虽然可以通过比特率来控制文件大小，但音频文件中的比特率并非设定比特率，采样率由于是我们自己采样的，到这个编码器随他怎么搞":"L49q::此浏览器不支持进行webm编码，未实现MediaRecorder")}},e.prototype.webm=function(t,i,c){if(o)if(a){var d=this.set,s=t.length,u=d.sampleRate,f=e.GetContext(!0),w=function(){e.CloseNewCtx(f)},m=f.createMediaStreamDestination();m.channelCount=1;var p=new MediaRecorder(m.stream,{mimeType:r,bitsPerSecond:1e3*d.bitRate}),b=[];p.ondataavailable=function(e){b.push(e.data)},p.onstop=function(e){var t=new Blob(b,{type:r}),n=new FileReader;n.onloadend=function(){w(),i(n.result,r)},n.readAsArrayBuffer(t)},p.onerror=function(e){w(),c(n("PIX0::转码webm出错：{1}",0,e.message))},p.start();for(var l=f.createBuffer(1,s,u),v=l.getChannelData(0),R=0;R<s;R++){var y=t[R];y=y<0?y/32768:y/32767,v[R]=y}var M=f.createBufferSource();M.channelCount=1,M.buffer=l,M.connect(m),M.start?M.start():M.noteOn(0),M.onended=function(){p.stop()}}else c(n("aG4z::此浏览器不支持把录音转成webm格式"));else c(n.G("NonBrowser-1",["webm encoder"]))}}(t,0,n.$T,e)}();