/*
錄音
https://github.com/xiangyuecn/Recorder
src: extensions/dtmf.decode.js
*/
!function(){var e="object"==typeof window&&!!window.document,o=(e?window:Object).Recorder,r=o.i18n;!function(e,o,r){"use strict";e.DTMF_Decode=function(o,s,l){l||(l={});var c=l.lastIs||"",u=null==l.lastCheckCount?99:l.lastCheckCount,d=l.prevIs||"",h=l.totalLen||0,v=l.pcm,i=l.checkFactor||0,f=l.debug,g=[];if(!e.LibFFT)throw new Error(r.G("NeedImport-2",["DTMF_Decode","src/extensions/lib.fft.js"]));var b=256,p=e.LibFFT(b),F=b/4,k=i||3,w=3,M=h,y=s/4e3,m=Math.floor(o.length/y);h+=m;var C=0;v&&v.length>b&&(m+=C=F*(k+1),M-=C);var I=new Int16Array(m);C&&I.set(v.subarray(v.length-C));for(var D=0;D<o.length;C++,D+=y)I[C]=o[Math.round(D)];o=I;for(var T=(s=4e3)/b,x=20,L=0;L+b<=o.length;L+=F){I=o.subarray(L,L+b);for(var j=p.transform(I),A=[],_=0,B=0,E=0,G=0,N=0,O=0,R=0,$=0,q=0;q<j.length;q++){var z=j[q],H=Math.log(z);A.push(H);var J=(q+1)*T;H>x&&(z>_&&J<1050?(_=z,B=H,E=J,G=q):z>N&&J>1050&&(N=z,O=H,R=J,$=q))}var K=-1,P=-1;if(E>600&&R<1700&&Math.abs(B-O)<2.5){var Q=1,S=B,U=0;for(q=G;q<$;q++){(J=A[q])&&J<S&&(S=J,U=q)}var V=.5*(B-S),W=B;for(q=G;Q&&q<U;q++){(J=A[q])<=W?W=J:J-W>V&&(Q=0)}var X=S;for(q=U;Q&&q<$;q++){(J=A[q])>=X?X=J:X-J>V&&(Q=0)}Q&&(K=n(E,t[0],T),P=n(R,t[1],T))}var Y="";K>=0&&P>=0?(Y=a[K][P],f&&console.log(Y,Math.round((M+L)/s*1e3),B.toFixed(2),O.toFixed(2),Math.abs(B-O).toFixed(2)),c?c.key==Y?u++:(Y="",u=c.old+u):(d&&d.old2&&d.key==Y&&M+L-d.start<100*s/1e3&&(c=d,u=d.old2+1,f&&console.warn("接续了开叉的信号"+u)),c||(u>=w?(c={key:Y,old:u,old2:u,start:M+L,pcms:[],use:0},u=1):(Y="",u=0)))):c&&(c.old2=u,u=c.old+u),Y?(f&&c.pcms.push(I),u>=k&&!c.use&&(c.use=1,g.push({key:Y,time:Math.round(c.start/s*1e3)})),c.use&&(f&&console.log(Y+"有效按键",c),c.old=0,c.old2=0,u=0)):(c&&(f&&console.log(c),d=c),c="",u++)}return{keys:g,lastIs:c,lastCheckCount:u,prevIs:d,totalLen:h,pcm:o,checkFactor:i,debug:f}};var t=[[697,770,852,941],[1209,1336,1477,1633]],a=[["1","2","3","A"],["4","5","6","B"],["7","8","9","C"],["*","0","#","D"]],n=function(e,o,r){for(var t=-1,a=1e3,n=0;n<o.length;n++){var s=Math.abs(o[n]-e);a>s&&(a=s,s<2*r&&(t=n))}return t}}(o,0,r.$T)}();