/*
錄音
https://github.com/xiangyuecn/Recorder
src: app-support/app.js
*/
!function(){var n="object"==typeof window&&!!window.document,t=n?window:Object,r=t.Recorder,e=r.i18n;!function(n,t,r,e,o){"use strict";var u={LM:"2024-04-09 19:22",Current:0,Platforms:{}},a=u.Platforms,c="RecordApp",i="RequestPermission",f="RegisterPlatform",l=n[c];if(l&&l.LM==u.LM)return void l.CLog(e("uXtA::重复导入{1}",0,c),3);n[c]=u,t[c]=u,u.__SID_=0;var s=u.__SID=function(){return++u.__SID_},p=u.__Sync=function(n,t,r){return u.__SID_==n||(t&&_(e("kIBu::注意：因为并发调用了其他录音相关方法，当前 {1} 的调用结果已被丢弃且不会有回调",0,t)+(r?", error: "+r:""),3),!1)},_=function(){var n=arguments;n[0]="["+(_.Tag||c)+"]["+(u.Current&&u.Current.Key||"?")+"]"+n[0],t.CLog.apply(null,n)};u.CLog=_,u[f]=function(n,t){t.Key=n,a[n]&&_(e("ha2K::重复注册{1}",0,n),3),a[n]=t},u.__StopOnlyClearMsg=function(){return e("wpTL::仅清理资源")};var v="Default-H5",S=i+"_H5OpenSet";(function(){var n={Support:function(n){n(!0)},CanProcess:function(){return!0}};u[f](v,n),n[i]=function(n,e,o){var a=u.__Rec;a&&(a.close(),u.__Rec=null),r();var c=u[S];u[S]=null,t(c||{}).open(function(){e()},o)};var r=function(){if(t.IsOpen()){_("kill open...");var n=t();n.open(),n.close()}};n.Start=function(n,r,e,o){var a=u.__Rec;null!=a&&a.stop(),u.__Rec=a=t(r),a.appSet=r,a.dataType="arraybuffer",a.open(function(){p(n)&&a.start(),e()},o)},n.Stop=function(n,t,o){var a=u.__Rec,c=t?"":u.__StopOnlyClearMsg();if(!a)return r(),void o(e("bpvP::未开始录音")+(c?" ("+c+")":""));var i=function(){if(p(n))for(var t in a.close(),a.set)a.appSet[t]=a.set[t]},f=function(n){i(),o(n)};t?a.stop(function(n,r,e){i(),t(n,r,e)},f):f(c)}})(),u.GetCurrentRecOrNull=function(){return u.__Rec||null},u.Pause=function(){var n=u.Current,t="Pause";if(!n||!n[t]||!1===n[t]()){var r=u.__Rec;r&&C(t)&&r.pause()}},u.Resume=function(){var n=u.Current,t="Resume";if(!n||!n[t]||!1===n[t]()){var r=u.__Rec;r&&C(t)&&r.resume()}};var C=function(n){var t=u.Current;if(t&&t.CanProcess())return 1;_(e("fLJD::当前环境不支持实时回调，无法进行{1}",0,n),3)};u.Install=function(n,t){var r=u.Current;if(r)n&&n();else{var e=u.__reqs||(u.__reqs=[]);e.push({s:n,f:t}),n=function(){o("s",arguments)},t=function(){o("f",arguments)};var o=function(n,t){var r=[].concat(e);e.length=0;for(var o=0;o<r.length;o++){var u=r[o][n];u&&u.apply(null,t)}};if(!(e.length>1)){var c,i=[v];for(var f in a)f!=v&&i.push(f);i.reverse();var l=function(n){c=i[n],(r=a[c]).Support(function(e){if(!e)return l(n+1);r.Install?r.Install(s,t):s()})},s=function(){u.Current=r,_("Install platform: "+c),n()};l(0)}}},u[i]=function(n,t){var r=s(),o=c+"."+i,a=function(n,u){var a=n+", isUserNotAllow:"+(u=!!u);p(r,o,a)&&(_(e("YnzX::录音权限请求失败：")+a,1),t&&t(n,u))};_(i+"..."),u.Install(function(){if(p(r,o)){var t=m();t?a(t):u.Current[i](r,function(){p(r,o)&&(_(i+" Success"),n&&n())},a)}},a)};var R=function(){return e("nwKR::需先调用{1}",0,i)},m=function(){var n="";return u.Current.Key!=v||o||(n=e("citA::当前不是浏览器环境，需引入针对此平台的支持文件（{1}），或调用{2}自行实现接入",0,"src/app-support/app-xxx-support.js",c+"."+f)),n};u.Start=function(n,r,o){var i=s(),f=c+".Start",l=function(n){p(i,f,n)&&(_(e("ecp9::开始录音失败：")+n,1),o&&o(n))};_("Start...");var v=u.Current;if(v){n||(n={});var S={type:"mp3",sampleRate:16e3,bitRate:16,onProcess:function(){}};for(var C in S)n[C]||(n[C]=S[C]);for(var C in a){var y=a[C];y.AllStart_Clean&&y.AllStart_Clean(n)}var d=!1;if(v.Start_Check&&(d=v.Start_Check(n)),!1===d)(d=t(n).envCheck({envName:v.Key,canProcess:v.CanProcess()}))||(d=m());d?l(e("EKmS::不能录音：")+d):(u._SRec=0,v.Start(i,n,function(){p(i,f)&&(_(e("k7Qo::已开始录音"),n),u._STime=Date.now(),r&&r())},l))}else l(R())},u.Stop=function(n,r){var o=s(),a=c+".Stop",i=function(t){if(p(o,a,t)){_(e("Douz::结束录音失败：")+t,n?1:0);try{r&&r(t)}finally{f()}}},f=function(){u._SRec=u.__Rec,u.__Rec=null};_("Stop... "+e("wqSH::和Start时差：{1}ms",0,u._STime?Date.now()-u._STime:-1)+" Recorder.LM:"+t.LM+" "+c+".LM:"+u.LM);var l=Date.now(),v=u.Current;v?v.Stop(o,n?function(t,r,u){if(p(o,a)){_(e("g3VX::结束录音 耗时{1}ms 音频时长{2}ms 文件大小{3}b {4}",0,Date.now()-l,r,t.byteLength,u));try{n(t,r,u)}finally{f()}}}:null,i):i(R())}}(t,r,0,e.$T,n),"function"==typeof define&&define.amd&&define(function(){return t.RecordApp}),"object"==typeof module&&module.exports&&(module.exports=t.RecordApp)}();