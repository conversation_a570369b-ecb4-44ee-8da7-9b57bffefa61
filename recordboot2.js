class VoiceRecorderCard1 extends HTMLElement {
    constructor() {
        super();
        this.recorder = null;
        this.isRecording = false;
        this._hass = null;
        this.MAX_DURATION = 300000; // Maximum record time (milliseconds)
        this.recordingTimeout = null;
        this.audioContext = null;
        this.gainNode = null;
    }

    setConfig(config) {
        if (!config.token) {
            throw new Error('請設定 HA token');
        }
        if (config.button_mode && !['click', 'hold'].includes(config.button_mode)) {
            throw new Error("無效的 button_mode");
        }

        this.config = config;
        this.token = config.token;
        this.options = config.event_options || null;
        this.notify = config.notify || false;
        this.button_mode = config.button_mode || 'click';
        // 音量增益設定，預設為 1.0 (無增益)，範圍 0.1 - 10.0
        this.volumeGain = Math.max(0.1, Math.min(10.0, config.volume_gain || 1.0));
        this.attachShadow({ mode: 'open' });
        this._buildCard();
    }

    _buildCard() {
        const card = document.createElement('ha-card');
        if (this.config.name) {
            card.header = this.config.name;
        }

        const style = document.createElement('style');
        style.textContent = `
            ha-card {
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                background: var(--card-background-color);
                padding: 0px;
                align-items: center;
                justify-content: center;
            }
            
            .card-content {
                max-width: 100%;
                padding: 18px 12px 12px 12px;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                gap: 8px;
                box-sizing: border-box;
                flex-wrap: wrap;
            }
            
            .gain-info {
                width: 100%;
                text-align: center;
                font-size: 12px;
                color: var(--secondary-text-color);
                margin-bottom: 8px;
                padding: 4px;
                background: rgba(var(--rgb-primary-color), 0.1);
                border-radius: 8px;
            }
            
            ha-select {
                margin: 0;
                flex: 1 1 150px;
                min-width: 0;
                max-width: 100%;
                text-overflow: ellipsis;
                
                /* 基本顏色設定 */
                --mdc-select-fill-color: var(--card-background-color);
                --mdc-select-ink-color: var(--primary-text-color);
                --mdc-select-label-ink-color: var(--primary-color);
                --mdc-select-dropdown-icon-color: var(--primary-color);

                /* 邊框相關 */
                --mdc-select-idle-line-color: var(--primary-color);
                --mdc-select-outlined-idle-border-color: var(--primary-color);
                --mdc-select-outlined-hover-border-color: var(--accent-color);

                --mdc-select-hover-line-color: var(--accent-color);
                --mdc-theme-primary: var(--accent-color);  /* 選中項目顏色 */
                --mdc-theme-surface: var(--card-background-color);  /* 下拉選單背景 */

                --mdc-menu-surface-fill-color: var(--card-background-color);
                --mdc-menu-text-color: var(--primary-text-color);
                --mdc-menu-min-width: 100%;
                --mdc-menu-max-width: 100%;
            }
            
            ha-list-item {
                --mdc-theme-text-primary-on-background: var(--primary-text-color);
                --mdc-theme-text-secondary-on-background: var(--secondary-text-color);
                --mdc-ripple-color: var(--accent-color);
            }
            
            ha-list-item[selected] {
                color: var(--accent-color);
                background-color: rgba(var(--rgb-accent-color), 0.12);
            }

            ha-button {
                margin: 0;
                flex: 0 1 80px;
                max-width: 100%;
                min-width: 0;
                transition: all 0.3s ease;
                --mdc-theme-primary: var(--primary-color);
                --mdc-shape-small: 12px;
            }
            
            ha-button:hover {
                opacity: 0.9;
            }
            
            /* 錄音狀態的紅色樣式 */
            ha-button.recording::part(base) {
                background-color: #ff0000 !important;
            }

             /* 添加脈衝動畫效果 */
             ha-button.recording::part(base) {
                 animation: pulse-red 1.5s infinite;
             }

             @keyframes pulse-red {
                 0% { 
                     box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
                 }
                 70% {
                     box-shadow: 0 0 0 10px rgba(255, 0, 0, 0);
                 }
                 100% {
                     box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
                 }
             }
        `;

        const content = document.createElement('div');
        content.className = 'card-content';

        // 顯示音量增益資訊
        if (this.volumeGain !== 1.0) {
            const gainInfo = document.createElement('div');
            gainInfo.className = 'gain-info';
            gainInfo.textContent = `音量增益: ${this.volumeGain}x ${this.volumeGain > 1 ? '(放大)' : '(縮小)'}`;
            content.appendChild(gainInfo);
        }

        // Add eventname menu
        const eventnameSelect = document.createElement('ha-select');
        eventnameSelect.id = 'eventnameInput';

        // Add options
        if (this.options) {
            this.options.forEach((option, index) => {
                const listItem = document.createElement('ha-list-item');
                listItem.value = option;
                listItem.textContent = option;

                if (index === 0) {
                    listItem.setAttribute('selected', 'true');
                }

                eventnameSelect.appendChild(listItem);
            });
        } else {
            // Add empty option as default value
            const emptyOption = document.createElement('ha-list-item');
            emptyOption.value = ''
            emptyOption.textContent = '選擇事件名稱';
            eventnameSelect.appendChild(emptyOption);
        }

        content.appendChild(eventnameSelect);

        // Add record button
        const recordButton = document.createElement('ha-button');
        recordButton.raised = true;
        recordButton.id = 'recordButton';
        recordButton.innerHTML = `
          <ha-icon icon="mdi:microphone"></ha-icon>
        `;
        if (this.button_mode === 'click') {
            recordButton.addEventListener('click', () => {
                if (this.isRecording) {
                    this.stopRecording();
                } else {
                    this.startRecording();
                }
            });
        } else {
            recordButton.addEventListener('pointerdown', (e) => {
                e.preventDefault();
                this.startRecording();
            });
            recordButton.addEventListener('pointerup', (e) => {
                e.preventDefault();
                this.stopRecording();
            });
        }

        content.appendChild(recordButton);

        card.appendChild(style);
        card.appendChild(content);

        while (this.shadowRoot.firstChild) {
            this.shadowRoot.removeChild(this.shadowRoot.firstChild);
        }
        this.shadowRoot.appendChild(card);
    }

    async initRecorder() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/gh/kukuxx/Recorder@master/recorder.mp3.min.js';

            script.onload = () => {
                // 初始化音訊處理器
                this._initAudioProcessor();
                
                this.recorder = Recorder({
                    type: "mp3",
                    sampleRate: 16000,
                    bitRate: 128,
                    onProcess: (buffers, powerLevel, bufferDuration, bufferSampleRate, newBufferIdx, asyncEnd) => {
                        // 在這裡應用音量增益
                        if (this.volumeGain !== 1.0 && buffers && buffers[0]) {
                            this._applyGainToBuffer(buffers[0]);
                        }
                    }
                });

                this.recorder.open(() => {
                    resolve();
                }, (msg) => {
                    this._showError('無法開始錄音: ' + msg);
                    reject(new Error(msg));
                });
            };

            script.onerror = () => {
                this._showError('錄音器外掛載入失敗');
                reject(new Error('腳本載入失敗'));
            };

            document.body.appendChild(script);
        });
    }

    _initAudioProcessor() {
        try {
            // 建立 AudioContext (如果支援的話)
            if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                this.gainNode = this.audioContext.createGain();
                this.gainNode.gain.value = this.volumeGain;
            }
        } catch (error) {
            console.warn('AudioContext 不支援，將使用軟體增益處理:', error);
        }
    }

    _applyGainToBuffer(buffer) {
        if (!buffer || this.volumeGain === 1.0) return;
        
        // 對音訊緩衝區應用增益
        for (let i = 0; i < buffer.length; i++) {
            let sample = buffer[i] * this.volumeGain;
            // 防止音訊削波 (clipping)
            if (sample > 1.0) {
                sample = 1.0;
            } else if (sample < -1.0) {
                sample = -1.0;
            }
            buffer[i] = sample;
        }
    }

    async startRecording() {
        if (this.isRecording) return;

        try {
            if (!this.recorder) {
                await this.initRecorder();
            }

            this.recorder.start();
            this.isRecording = true;

            const button = this.shadowRoot.querySelector('#recordButton');
            button.classList.add('recording');

            // Set the maximum record time
            this.recordingTimeout = setTimeout(() => {
                if (this.isRecording) {
                    this.stopRecording();
                    this._showMessage('達到最大錄音時間 (300 秒)');
                }
            }, this.MAX_DURATION);

        } catch (error) {
            this._showError('錄音啟動失敗: ' + error.message);
            this.isRecording = false;
        }
    }

    async stopRecording() {
        if (!this.recorder || !this.isRecording) return;

        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
            this.recordingTimeout = null;
        }

        try {
            this.isRecording = false;
            const button = this.shadowRoot.querySelector('#recordButton');
            button.classList.remove('recording');

            this.recorder.stop(async (blob, duration) => {
                try {
                    if (duration < 200) {
                        this._showError('錄音時間太短 (少於 200 毫秒)');
                        return;
                    }

                    const formData = new FormData();
                    const browserID = window.browser_mod?.browserID ? window.browser_mod.browserID : '';
                    const eventName = String(this.shadowRoot.querySelector('#eventnameInput').value || '').trim();
                    formData.append('file', blob, 'recording.mp3');
                    formData.append('browserid', browserID);
                    formData.append('eventname', eventName);

                    const response = await fetch('/api/voice_recorder/upload', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        },
                        body: formData
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`上傳失敗 (${response.status}): ${errorText}`);
                    }

                    const result = await response.json();

                    if (result.success && this.notify) {
                        const notification = `瀏覽器ID: ${result.browserID}\n事件名稱: ${result.eventName}\n檔名: ${result.filename}\n路徑: ${result.path}`;
                        this._hass.callService('persistent_notification', 'create', {
                            message: notification,
                            title: '錄音儲存成功'
                        });
                    } else {
                        if (!result.success) {
                            throw new Error(result.msg);
                        }
                    }

                } catch (error) {
                    this._showError('儲存錄音失敗: ' + error.message);
                }
            });
        } catch (error) {
            this._showError('停止錄音失敗: ' + error.message);
            this.isRecording = false;
        }
    }

    _showError(message) {
        if (this._hass) {
            this._hass.callService('persistent_notification', 'create', {
                message: message,
                title: '錄音器卡片錯誤'
            });
        } else {
            console.error(message);
        }
    }

    _showMessage(message) {
        if (this._hass) {
            this._hass.callService('persistent_notification', 'create', {
                message: message,
                title: '錄音器卡片提示'
            });
        } else {
            console.log(message);
        }
    }

    disconnectedCallback() {
        if (this.recorder) {
            this.recorder.close();
            this.recorder = null;
        }
        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
            this.recordingTimeout = null;
        }
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
    }

    set hass(hass) {
        this._hass = hass;
    }
}

console.info(
    `%c  VOICE-RECORDER-CARD  \n%c   Version:   V1.1.0 (音量增益版)   `,
    'color: orchid; font-weight: bold; background: dimgray;',
    'color: orange; font-weight: bold; background: white;'
);

customElements.define('voice-recorder-card1', VoiceRecorderCard1);

window.customCards = window.customCards || [];
window.customCards.push({
    type: "voice-recorder-card1",
    name: "Voice Recorder Card1",
    description: "具有音量增益功能的錄音卡片",
});