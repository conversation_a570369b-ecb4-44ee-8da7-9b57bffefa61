/*
錄音
https://github.com/xiangyuecn/Recorder
src: extensions/asr.aliyun.short.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,n=t.i18n;!function(e,t,n,s){"use strict";var o=function(e){return new i(e)},a="ASR_Aliyun_Short",i=function(e){var t=this,n={tokenApi:"",apiArgs:{action:"token",lang:"普通话"},apiRequest:null,compatibleWebSocket:null,log:u,fileSpeed:6};for(var s in e)n[s]=e[s];t.set=e=n,t.state=0,t.started=0,t.sampleRate=16e3,t.pcmBuffers=[],t.pcmTotal=0,t.pcmOffset=0,t.pcmSend=0,t.joinBuffers=[],t.joinSize=0,t.joinSend=0,t.joinOffset=-1,t.joinIsOpen=0,t.joinSendTotal=0,t.sendCurSize=0,t.sendTotal=0,t.resTxts=[],e.asrProcess||t.log("未绑定asrProcess回调无法感知到abort事件",3)},r=function(){var t=arguments;t[0]="["+a+"]"+t[0],e.CLog.apply(null,t)};function f(e,t,n,s){var o=new XMLHttpRequest;o.timeout=2e4,o.open("POST",e),o.onreadystatechange=function(){if(4==o.readyState)if(200==o.status){try{var e=JSON.parse(o.responseText)}catch(e){}if(0!==e.c||!e.v)return void s(e.m||"接口返回非预定义json数据");n(e.v)}else s("请求失败["+o.status+"]")};var a=[];for(var i in t)a.push(i+"="+encodeURIComponent(t[i]));o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.send(a.join("&"))}function u(){}i.prototype=o.prototype={log:function(e,t){r(e,"number"==typeof t?t:0),this.set.log("["+a+"]"+e,3==t?"#f60":t)},inputDuration:function(){return Math.round(this.pcmTotal/this.sampleRate*1e3)},sendDuration:function(e){var t=this.sendTotal;return t+=e||0,Math.round(t/this.sampleRate*1e3)},asrDuration:function(){return this.sendDuration(-this.joinSendTotal)},audioToText:function(t,n,s){var o=this,a=function(e){o.log(e,1),s&&s(e)};if(e.GetContext()){var i=new FileReader;i.onloadend=function(){e.Ctx.decodeAudioData(i.result,function(e){for(var t=e.getChannelData(0),a=e.sampleRate,i=new Int16Array(t.length),r=0;r<t.length;r++){var f=Math.max(-1,Math.min(1,t[r]));f=f<0?32768*f:32767*f,i[r]=f}o.pcmToText(i,a,n,s)},function(e){a("音频解码失败["+t.type+"]:"+e.message)})},i.readAsArrayBuffer(t)}else a("浏览器不支持音频解码")},pcmToText:function(e,t,n,s){var o=this;o.start(function(){o.log("单个文件"+Math.round(e.length/t*1e3)+"ms转文字"),o.sendSpeed=o.set.fileSpeed,o.input([e],t),o.stop(n,s)},s)},start:function(e,t){var n=this,o=n.set,a=function(e){n.sendAbortMsg=e,t&&t(e)};if(o.compatibleWebSocket||s)if(0==n.state){n.state=1;var i=function(){n.log("ASR start被stop中断",1),n._send()};n._token(function(){1!=n.state?i():(n.log("OK start",2),n.started=1,e&&e(),n._send())},function(e){e="语音识别token接口出错："+e,n.log(e,1),1!=n.state?i():(a(e),n._send())})}else a("ASR对象不可重复start");else a("非浏览器环境，请提供compatibleWebSocket配置来返回一个兼容的WebSocket")},stop:function(e,t){e=e||u,t=t||u;var n=this,s=function(e){e="语音识别stop出错："+e,n.log(e,1),t(e)};2!=n.state?(n.state=2,n.stopWait=function(){if(n.stopWait=null,n.started){var s=n.getText();!s&&n.sendAbortMsg?t(n.sendAbortMsg):e(s,n.sendAbortMsg||"")}else t(n.sendAbortMsg||"未开始语音识别")},n._send()):s("ASR对象不可重复stop")},input:function(t,n,s){var o=this;if(2!=o.state){var a="input输入的采样率低于"+o.sampleRate;if(n<o.sampleRate)return r(a+"，数据已丢弃",3),o.pcmTotal||(o.sendAbortMsg=a),void o._send();if(o.sendAbortMsg==a&&(o.sendAbortMsg=""),s){for(var i=[],f=s;f<t.length;f++)i.push(t[f]);t=i}var u=e.SampleData(t,n,o.sampleRate).data;o.pcmTotal+=u.length,o.pcmBuffers.push(u),o._send()}else o._send()},_send:function(){var e=this,t=e.set;if(!e.sendWait){var n=function(){e.stopWait&&e.stopWait()};if(2!=e.state||e.started&&e.stopWait)if(e.sendAbort)n();else{var s=function(t){e.sendAbort||(e.sendAbort=1,e.sendAbortMsg=t||"-",o(0,1)),e._send()},o=function(n,s){if(!s&&e.sendAbort)return!1;if(n=n||0,!t.asrProcess)return e.sendTotal+n<=i;var o=t.asrProcess(e.getText(),e.sendDuration(n),e.sendAbort?e.sendAbortMsg:"");return e._prsw||"boolean"==typeof o||r("asrProcess返回值必须是boolean类型，true才能继续识别，否则立即超时",1),e._prsw=1,o},a=5*e.sampleRate,i=60*e.sampleRate,f=e.wsCur;if(f){if(!e.wsLock&&2==f._s&&!f.isStop)if(e.pcmSend>=e.pcmTotal){if(1==e.state)return;f.stopWs(function(){n()},function(e){s(e)})}else{var u=e.sampleRate/1e3*50,l=e.sampleRate;if((f.bufferedAmount||0)/2>3*l)e.sendWait=setTimeout(function(){e.sendWait=0,e._send()},100);else{if(e.sendSpeed){var c=(Date.now()-f.okTime)*e.sendSpeed,p=(e.sendCurSize+l/3)/e.sampleRate*1e3,d=Math.floor((p-c)/e.sendSpeed);if(d>0)return r("[ASR]延迟"+d+"ms发送"),void(e.sendWait=setTimeout(function(){e.sendWait=0,e._send()},d))}var m=1,g=function(e,t,n){for(var s=n.length,o=0,a=0;a<s&&o<t.length;){var i=t[o];if(!(i.length-e<=s-a)){n.set(i.subarray(e,e+(s-a)),a),e+=s-a;break}n.set(0==e?i:i.subarray(e),a),a+=i.length-e,e=0,t.splice(o,1)}return e};if(e.joinIsOpen){if(-1==e.joinOffset){e.joinSend=0,e.joinOffset=0,e.log("发送上1分钟结尾5秒数据...");for(var h=0,v=e.joinBuffers.length-1;v>=0;v--)if((h+=e.joinBuffers[v].length)>=a){e.joinBuffers.splice(0,v),e.joinSize=h,e.joinOffset=h-a;break}}var _=e.joinSize-e.joinOffset;if((R=Math.min(l,_))<=0)return e.log("发送新1分钟数据(重叠"+Math.round(e.joinSend/e.sampleRate*1e3)+"ms)..."),e.joinBuffers=[],e.joinSize=0,e.joinOffset=-1,e.joinIsOpen=0,void e._send();var S=new Int16Array(R);e.joinSend+=R,e.joinSendTotal+=R,e.joinOffset=g(e.joinOffset,e.joinBuffers,S),e.joinSize=0;for(v=0;v<e.joinBuffers.length;v++)e.joinSize+=e.joinBuffers[v].length}else{_=e.pcmTotal-e.pcmSend;var T=Math.round(_/e.sampleRate*1e3),k=i-e.sendCurSize,b=Math.min(l,_),R=Math.min(b,k);if(1==e.state&&R<Math.min(u,k))return;var j=0;if(k<=0&&(2==e.state&&_<1.2*e.sampleRate?(R=_,e.log("丢弃结尾"+T+"ms数据","#999"),m=0):j=!0),m&&!o(b)){var A=Math.round(e.asrDuration()/1e3);return e.log("已主动超时，共识别"+A+"秒，丢弃缓冲"+T+"ms，正在终止..."),e.wsLock=1,void f.stopWs(function(){s("已主动超时，共识别"+A+"秒，终止识别")},function(e){s(e)})}if(j)return r("[ASR]新1分钟接续，当前缓冲"+T+"ms..."),e.wsLock=1,void f.stopWs(function(){e._token(function(){e.log("新1分钟接续OK，当前缓冲"+T+"ms",2),e.wsLock=0,e.wsCur=0,e.sendCurSize=0,e.joinIsOpen=1,e.joinOffset=-1,e._send()},function(e){s("语音识别新1分钟token接口出错："+e)})},function(e){s(e)});S=new Int16Array(R);e.pcmOffset=g(e.pcmOffset,e.pcmBuffers,S),e.pcmSend+=R,e.joinBuffers.push(S),e.joinSize+=R}if(e.sendCurSize+=S.length,e.sendTotal+=S.length,m)try{f.send(S.buffer)}catch(e){r("ws.send",1,e)}e.sendWait=setTimeout(function(){e.sendWait=0,e._send()})}}}else if(e.started){var w={};e.resTxts.push(w),f=e.wsCur=e._wsNew(e.tokenData,"ws:"+e.resTxts.length,w,function(){o()},function(){e._send()},function(t){f==e.wsCur&&s(t)})}}else n()}},getText:function(){for(var e=this.resTxts,t="",n=0;n<e.length;n++){var s=e[n];if(s.fullTxt)t=s.fullTxt;else{var o=s.tempTxt||"";if(s.okTxt&&(o=s.okTxt),t){for(var a=t.substr(-20),i=[],r=0,f=Math.min(17,o.length-3);r<=f;r++)for(var u=0;u<17;u++)if(a[u]==o[r]){for(var l=1;l<17&&a[u+l]==o[r+l];l++);l>=3&&i.push({x:r,i0:u,n:l})}i.sort(function(e,t){var n=t.n-e.n;return 0!=n?n:t.i0-e.i0});var c=i[0];c?(t=t.substr(0,t.length-a.length+c.i0),t+=o.substr(c.x)):t+=o}else t=o;null!=s.okTxt&&o==s.okTxt&&(s.fullTxt=t)}}return t},_wsNew:function(e,t,n,s,o,a){var i=function(){for(var e,t=[],n=0;n<32;n++)e=Math.floor(16*Math.random()),t.push(String.fromCharCode(e<10?e+48:e-10+97));return t.join("")},f=this,u=f.set;r("[ASR "+t+"]正在连接...");var l="wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token="+e.token;if(u.compatibleWebSocket)var c=u.compatibleWebSocket(l);else c=new WebSocket(l);return c.onclose=function(){if(-1!=c._s){var e=4!=c._s;c._s=-1,f.log("["+t+"]close"),e&&a(c._err||"连接"+t+"已关闭")}},c.onerror=function(e){if(-1!=c._s){var n="网络连接错误";c._err||(c._err=n),f.log("["+t+"]"+n,1),c.onclose()}},c.onopen=function(){-1!=c._s&&(c._s=1,r("[ASR "+t+"]open"),c._task=i(),c.send(JSON.stringify({header:{message_id:i(),task_id:c._task,appkey:e.appkey,namespace:"SpeechRecognizer",name:"StartRecognition"},payload:{format:"pcm",sample_rate:f.sampleRate,enable_intermediate_result:!0,enable_punctuation_prediction:!0,enable_inverse_text_normalization:!0},context:{}})))},c.onmessage=function(e){var a=e.data,i=!0;if("string"==typeof a&&"{"==a[0]){var u=(a=JSON.parse(a)).header||{},l=a.payload||{},p=u.name||"",d=u.status||0,m="TaskFailed"==p,g="";if(1!=c._s||"RecognitionStarted"!=p&&!m||(m?g="连接"+t+"失败["+d+"]"+u.status_text:(c._s=2,f.log("["+t+"]连接OK"),c.okTime=Date.now(),o())),2!=c._s||"RecognitionResultChanged"!=p&&!m||(m?g="识别出现错误["+d+"]"+u.status_text:(i=!c._clmsg,c._clmsg=1,n.tempTxt=l.result||"",s())),3==c._s&&("RecognitionCompleted"==p||m)){var h="";m?g="停止识别出现错误["+d+"]"+u.status_text:(h=l.result||"",f.log("["+t+"]最终识别结果："+h)),c.stopCall&&c.stopCall(h,g)}g&&(f.log("["+t+"]"+g,1),c._err||(c._err=g))}i&&r("[ASR "+t+"]msg",a)},c.stopWs=function(o,a){2==c._s?(c._s=3,c.isStop=1,c.stopCall=function(e,t){clearTimeout(c.stopInt),c.stopCall=0,c._s=4,c.close(),n.okTxt=e,s(),t?a(t):o()},c.stopInt=setTimeout(function(){c.stopCall&&c.stopCall("","停止识别返回结果超时")},1e4),r("[ASR "+t+"]send stop"),c.send(JSON.stringify({header:{message_id:i(),task_id:c._task,appkey:e.appkey,namespace:"SpeechRecognizer",name:"StopRecognition"}}))):a(t+"状态不正确["+c._s+"]")},c.connect&&c.connect(),c},_token:function(e,t){var n=this,s=n.set;s.tokenApi?(s.apiRequest||f)(s.tokenApi,s.apiArgs||{},function(s){s&&s.appkey&&s.token?(n.tokenData=s,e()):t("apiRequest回调的数据格式不正确")},t):t("未配置tokenApi")}},e[a]=o}(t,0,n.$T,e)}();