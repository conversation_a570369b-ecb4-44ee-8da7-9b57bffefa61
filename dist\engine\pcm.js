/*
錄音
https://github.com/xiangyuecn/Recorder
src: engine/pcm.js
*/
!function(){var e="object"==typeof window&&!!window.document,t=(e?window:Object).Recorder,r=t.i18n;!function(e,t,r){"use strict";e.prototype.enc_pcm={stable:!0,fast:!0,getTestMsg:function(){return r("fWsN::pcm为未封装的原始音频数据，pcm音频文件无法直接播放，可用Recorder.pcm2wav()转码成wav播放；支持位数8位、16位（填在比特率里面），采样率取值无限制")}};var n=function(t){var n=t.bitRate,o=8==n?8:16;n!=o&&e.CLog(r("uMUJ::PCM Info: 不支持{1}位，已更新成{2}位",0,n,o),3),t.bitRate=o};e.prototype.pcm=function(e,t,r){var a=this.set;n(a),t(o(e,a.bitRate).buffer,"audio/pcm")};var o=function(e,t){if(8==t)for(var r=e.length,n=new Uint8Array(r),o=0;o<r;o++){var a=128+(e[o]>>8);n[o]=a}else{e=new Int16Array(e);n=new Uint8Array(e.buffer)}return n};e.pcm2wav=function(t,n,o){t.blob||(t={blob:t});var a=t.blob,f=t.sampleRate||16e3,m=t.bitRate||16;if(t.sampleRate&&t.bitRate||e.CLog(r("KmRz::pcm2wav必须提供sampleRate和bitRate"),3),e.prototype.wav){var c=function(t,r){var a;if(8==m){var c=new Uint8Array(t);a=new Int16Array(c.length);for(var i=0;i<c.length;i++)a[i]=c[i]-128<<8}else a=new Int16Array(t);var p=e({type:"wav",sampleRate:f,bitRate:m});r&&(p.dataType="arraybuffer"),p.mock(a,f).stop(function(e,t,r){n(e,t,r)},o)};if(a instanceof ArrayBuffer)c(a,1);else{var i=new FileReader;i.onloadend=function(){c(i.result)},i.readAsArrayBuffer(a)}}else o(r.G("NeedImport-2",["pcm2wav","src/engine/wav.js"]))},e.prototype.pcm_envCheck=function(e,t){return""},e.prototype.pcm_start=function(e){return n(e),{set:e,memory:new Uint8Array(5e5),mOffset:0}};var a=function(e,t){var r=t.length;if(e.mOffset+r>e.memory.length){var n=new Uint8Array(e.memory.length+Math.max(5e5,r));n.set(e.memory.subarray(0,e.mOffset)),e.memory=n}e.memory.set(t,e.mOffset),e.mOffset+=r};e.prototype.pcm_stop=function(e){e&&e.memory&&(e.memory=null)},e.prototype.pcm_encode=function(e,t){if(e&&e.memory){var r=e.set,n=o(t,r.bitRate);r.takeoffEncodeChunk?r.takeoffEncodeChunk(n):a(e,n)}},e.prototype.pcm_complete=function(e,t,n,o){e&&e.memory?(o&&this.pcm_stop(e),t(e.memory.buffer.slice(0,e.mOffset),"audio/pcm")):n(r("sDkA::pcm编码器未start"))}}(t,0,r.$T)}();